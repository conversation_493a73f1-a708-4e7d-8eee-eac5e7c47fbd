<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iaa.datacenter.module.buttjoint.dal.mapper.erp.ErpShipMapper">

    <select id="getItemShip" resultType="com.iaa.datacenter.module.buttjoint.dal.dataobject.erp.ErpItemShipDO">
        select
        CBO_Customer.Code as customerCode,
        CBO_Customer_Trl.Name as customerName,
        <if test="type != null and (type == 1 or type == 2)">
            <if test="type == 1">
                CBO_ItemMaster.DescFlexField_PrivateDescSeg1
            </if>
            <if test="type == 2">
                CBO_ItemMaster.Name
            </if>
            as model,
        </if>
        sum(ShipQtyTUAmount) as shipQty,
        sum(SM_ShipLine.TotalMoneyFC) as shipMoney,
        ROW_NUMBER() OVER (PARTITION BY CBO_Customer.Code ORDER BY sum(SM_ShipLine.TotalMoneyFC) DESC) as rn
        from SM_Ship With(NoLock)
        left join SM_ShipLine With(NoLock) on Ship=SM_Ship.ID
        left join CBO_Customer With(NoLock) on OrderBy_Customer=CBO_Customer.ID
        left join CBO_Customer_Trl With(NoLock) on OrderBy_Customer=CBO_Customer_Trl.ID and SysMLFlag='zh-CN'
        left join CBO_ItemMaster With(NoLock) on ItemInfo_ItemID=CBO_ItemMaster.ID
        where DocumentType='1002211110003554'
        and BusinessDate between #{timestamp[0]} and #{timestamp[1]}
        and ItemInfo_ItemCode like concat(#{type},'%')
        <if test="customers != null and customers.size() > 0">
            and CBO_Customer.Code in
            <foreach collection="customers" open="(" item="customer" separator="," close=")">
                #{customer}
            </foreach>
        </if>
        <if test="model != null">
            <if test="type==1">
                and CBO_ItemMaster.DescFlexField_PrivateDescSeg1 like concat('%',#{model},'%')
            </if>
            <if test="type==2">
                and CBO_ItemMaster.Name  like concat('%',#{model},'%')
            </if>
        </if>
        group by CBO_Customer.Code, CBO_Customer_Trl.Name
        <if test="type==1">
            , CBO_ItemMaster.DescFlexField_PrivateDescSeg1
        </if>
        <if test="type==2">
            , CBO_ItemMaster.Name
        </if>
        order by customerCode asc, shipMoney desc;
    </select>

</mapper>
