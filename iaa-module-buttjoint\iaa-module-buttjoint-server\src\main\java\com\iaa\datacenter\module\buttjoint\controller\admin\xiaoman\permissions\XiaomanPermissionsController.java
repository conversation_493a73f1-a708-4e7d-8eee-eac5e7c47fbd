package com.iaa.datacenter.module.buttjoint.controller.admin.xiaoman.permissions;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iaa.datacenter.framework.common.pojo.CommonResult;
import com.iaa.datacenter.framework.common.pojo.PageResult;
import com.iaa.datacenter.framework.web.core.util.WebFrameworkUtils;
import com.iaa.datacenter.module.buttjoint.controller.admin.xiaoman.permissions.vo.XiaomanUserPageReqVO;
import com.iaa.datacenter.module.buttjoint.controller.admin.xiaoman.permissions.vo.XiaomanViewUserRespVO;
import com.iaa.datacenter.module.buttjoint.controller.admin.xiaoman.report.vo.XiaomanOpportunityReportPageReqVO;
import com.iaa.datacenter.module.buttjoint.controller.admin.xiaoman.report.vo.XiaomanOpportunityReportRespVO;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.xiaoman.XiaomanUserDO;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.xiaoman.XiaomanViewAuthorityDO;
import com.iaa.datacenter.module.buttjoint.dal.mapper.backend.BackendUserMapper;
import com.iaa.datacenter.module.buttjoint.service.xiaoman.customer.XiaomanViewAuthorityService;
import com.iaa.datacenter.module.buttjoint.service.xiaoman.user.XiaomanUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.iaa.datacenter.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 小满数据权限")
@RestController
@RequestMapping("/butt-joint/xiaoman/permissions")
@Validated
@Slf4j
public class XiaomanPermissionsController {
    @Resource
    private XiaomanUserService xiaomanUserService;
    @Resource
    private BackendUserMapper backendUserMapper;
    @Resource
    private XiaomanViewAuthorityService xiaomanViewAuthorityService;

    @GetMapping("/page-user")
    @Operation(summary = "获取可见小满报表用户")
    @PreAuthorize("@ss.hasPermission('xiaoman:permissions:list')")
    public CommonResult<PageResult<XiaomanViewUserRespVO>> getXiaomanViewUserPage(XiaomanUserPageReqVO pageReqVO) {
        QueryWrapper<XiaomanViewUserRespVO> wrapper = new QueryWrapper<>();
        wrapper.eq("type", 3);
        wrapper.likeRight("permission", "xiaoman");
        wrapper.eq("system_role_menu.deleted ", false);
        wrapper.eq("system_user_role.deleted",false);
        wrapper.like(StrUtil.isNotEmpty(pageReqVO.getNickname()), "nickname", pageReqVO.getNickname());
        Page<XiaomanViewUserRespVO> xiaomanViewUserPage = backendUserMapper.getXiaomanViewUserPage(new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize()), wrapper);
        return success(new PageResult<>(xiaomanViewUserPage.getRecords(), xiaomanViewUserPage.getTotal()));
    }

    @GetMapping("/list-xiaoman-user")
    @Operation(summary = "获取小满用户")
    @PreAuthorize("@ss.hasPermission('xiaoman:permissions:list')")
    public CommonResult<List<XiaomanUserDO>> getXiaomanUserList() {
        return success(xiaomanUserService.list(new com.anwen.mongo.conditions.query.QueryWrapper<XiaomanUserDO>().eq("is_salesman",true)));
    }

    @GetMapping("/get-view/{backendUserId}")
    @Operation(summary = "获取指定后台用户的可见范围")
    @PreAuthorize("@ss.hasPermission('xiaoman:permissions:list')")
    public CommonResult<XiaomanViewAuthorityDO> getXiaomanViewAuthorityDO(@PathVariable Long backendUserId) {
        return success(xiaomanViewAuthorityService.getById(backendUserId));
    }

    @GetMapping("/get-view-user")
    @Operation(summary = "获取指定后台用户的可见范围")
    @PreAuthorize("@ss.hasPermission('xiaoman:permissions:list')")
    public CommonResult<List<XiaomanUserDO>> getXiaomanViewUserDO() {
        List<XiaomanUserDO> list = new ArrayList<>();
        if (WebFrameworkUtils.getLoginUserId() == 1) {
            list.addAll(xiaomanUserService.list(new com.anwen.mongo.conditions.query.QueryWrapper<XiaomanUserDO>().eq("is_salesman",true)));
        } else {
            XiaomanViewAuthorityDO authority = xiaomanViewAuthorityService.getById(WebFrameworkUtils.getLoginUserId());
            if (Objects.nonNull(authority)) {
                list.addAll(xiaomanUserService.getByIds(authority.getXiaoman_user_ids()));
            }
        }
        return success(list);
    }

    @PostMapping("/set-view")
    @Operation(summary = "设定指定后台用户的可见范围")
    @PreAuthorize("@ss.hasPermission('xiaoman:permissions:set')")
    public CommonResult<Boolean> setXiaomanViewAuthorityDO(@RequestBody XiaomanViewAuthorityDO data) {
        XiaomanViewAuthorityDO xiaomanViewAuthorityDO = new XiaomanViewAuthorityDO(data.getBackend_user_id(), data.getXiaoman_user_ids());
        return success(xiaomanViewAuthorityService.saveOrUpdate(xiaomanViewAuthorityDO, true));
    }
}
