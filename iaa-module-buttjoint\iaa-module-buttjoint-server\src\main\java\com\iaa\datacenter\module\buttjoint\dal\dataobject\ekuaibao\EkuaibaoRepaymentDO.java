package com.iaa.datacenter.module.buttjoint.dal.dataobject.ekuaibao;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.anwen.mongo.annotation.ID;
import com.anwen.mongo.annotation.collection.CollectionField;
import com.anwen.mongo.annotation.collection.CollectionName;
import com.iaa.datacenter.framework.buttjoint.dataobject.BaseOrderDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 合思还款单信息
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@CollectionName("ekuaibao_repayment")
public class EkuaibaoRepaymentDO extends BaseOrderDO {
    @ID
    private String currentId;

    private String recordId;

    private String orderId;

    private JSONObject accountInfo;

    private Integer pipeline;

    private String grayver;

    private String threadId;

    private Integer version;

    private Boolean active;

    private String createTime;

    private String updateTime;

    private String corporationId;

    private String sourceCorporationId;

    private String dataCorporationId;

    private String ownerId;

    private String repaymentType;

    private Double amount;

    private String loanInfoId;

    private Double totalProfitAndLoss;

    private Double foreignAmount;

    private Boolean chargeAgainstMark;

    private JSONArray attachments;

    private String payerOwnerId;

    private String casherId;

    private String accountId;

    private String comment;

    private Long realArrivalDate;

    @CollectionField(exist = false)
    private JSONObject loanForm;
}
