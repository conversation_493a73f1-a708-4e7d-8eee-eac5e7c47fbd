package com.iaa.datacenter.module.buttjoint.dal.dataobject.erp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ErpItemShipDO {
    /**
     * 客户代码
     */
    private String customerCode;
    /**
     * 客户名称
     */
    private String customerName;
    /**
     * 型号
     */
    private String model;
    /**
     * 出库数量
     */
    private Integer shipQty;
    /**
     * 出库金额
     */
    private BigDecimal shipMoney;
}
