package com.iaa.datacenter.module.buttjoint.dal.dataobject.ekuaibao;

import com.alibaba.fastjson.JSONObject;
import com.anwen.mongo.annotation.ID;
import com.anwen.mongo.annotation.collection.CollectionLogic;
import com.anwen.mongo.annotation.collection.CollectionName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 合思付款账号信息
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@CollectionName("ekuaibao_pay_account")
public class EkuaibaoPayDO {
    @ID
    private String id;
    /**
     * 账户名称
     */
    private String name;
    /**
     * 账号
     */
    private String code;
    /**
     * 详情
     */
    private JSONObject detail;

    private Boolean active;
    /**
     * 排序
     */
    private String sort;
    /**
     * 可见性
     */
    private JSONObject visibility;
    /**
     * 备注
     */
    private String remark;
}
