package com.iaa.datacenter.module.buttjoint.controller.admin.plm.utils;

import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;

import java.net.InetAddress;
import java.net.UnknownHostException;

@Slf4j
public class IpUtils {

    // 私有IP段（局域网）
    private static final String[] PRIVATE_IP_RANGES = {
            "10.",          // 10.0.0.0-**************
            "172.16.",      // **********-**************
            "172.17.",
            "172.18.",
            "172.19.",
            "172.20.",
            "172.21.",
            "172.22.",
            "172.23.",
            "172.24.",
            "172.25.",
            "172.26.",
            "172.27.",
            "172.28.",
            "172.29.",
            "172.30.",
            "172.31.",
            "192.168."      // ***********-***************
    };

    /**
     * 获取客户端真实IP（处理代理情况）
     */
    public static String getClientIp(HttpServletRequest request) {
        String ipAddress = null;

        // 1. 优先读取Nginx配置的X-Real-IP（关键补充）
        ipAddress = request.getHeader("X-Real-IP");
        log.debug("X-Real-IP: {}", request.getHeader("X-Real-IP"));
        log.debug("X-Forwarded-For: {}", request.getHeader("X-Forwarded-For"));
        log.debug("RemoteAddr: {}", request.getRemoteAddr());
        if (isEmptyOrUnknown(ipAddress)) {
            ipAddress = request.getHeader("X-REAL-IP");
        }
        if (isEmptyOrUnknown(ipAddress)) {
            ipAddress = request.getHeader("x-real-ip");
        }
        // 2. 读取X-Forwarded-For（多级代理时，取第一个IP）
        if (isEmptyOrUnknown(ipAddress)) {
            ipAddress = request.getHeader("x-forwarded-for");
            if (ipAddress != null && ipAddress.contains(",")) {
                ipAddress = ipAddress.split(",")[0].trim(); // 多级代理时取第一个
            }
        }

        // 3. 读取其他可能的代理头（针对特定代理服务器）
        if (isEmptyOrUnknown(ipAddress)) {
            ipAddress = request.getHeader("Proxy-Client-IP");
        }
        if (isEmptyOrUnknown(ipAddress)) {
            ipAddress = request.getHeader("WL-Proxy-Client-IP");
        }

        // 4. 最后 fallback 到连接IP（此时可能是Nginx的IP）
        if (isEmptyOrUnknown(ipAddress)) {
            ipAddress = request.getRemoteAddr();
            // 处理本地回环地址
            if ("0:0:0:0:0:0:0:1".equals(ipAddress)) {
                try {
                    ipAddress = InetAddress.getLocalHost().getHostAddress();
                    log.info("无法获取客户端IP，使用本机IP：{}", ipAddress);
                } catch (UnknownHostException e) {
                    e.printStackTrace();
                }
            }
        }

        return ipAddress;
    }

    // 辅助方法：判断IP是否为空或unknown
    private static boolean isEmptyOrUnknown(String ip) {
        return ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip);
    }

    /**
     * 判断IP是否为局域网IP
     */
    public static boolean isLocalIp(String ip) {
        if (ip == null || ip.isEmpty()) {
            return false;
        }
        // 检查是否在私有IP段内
        for (String range : PRIVATE_IP_RANGES) {
            if (ip.startsWith(range)) {
                return true;
            }
        }
        // 检查是否为本地回环地址
        return "127.0.0.1".equals(ip) || "localhost".equals(ip);
    }
}