package com.iaa.datacenter.module.buttjoint.dal.mapper.erp;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.erp.ErpCustomerRcvDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.Collection;
import java.util.List;

/**
 * ERP 收款 Mapper
 */
@DS("erp")
@Mapper
public interface ErpRecMapper {

    /**
     * 查询客户收款信息
     * @param customers
     * @param dateRange
     * @return
     */
    List<ErpCustomerRcvDO> getCustomerRcv(Collection<String> customers, String[] dateRange);
}
