package com.iaa.datacenter.module.buttjoint.framework.factory;

import cn.hutool.core.lang.Assert;
import com.iaa.datacenter.framework.buttjoint.annotation.ClientIdentifier;
import com.iaa.datacenter.framework.buttjoint.service.ButtJointClientService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.List;

/**
 * 集成客户端工厂方法
 */
@Component
public class ButtJointClientFactory {

    /**
     * 客户端仓库
     */
    private final ConcurrentHashMap<String, ButtJointClientService> clientMap;

    /**
     * 初始化客户端并存入仓库
     * @param clientList
     */
    @Autowired
    public ButtJointClientFactory(List<ButtJointClientService> clientList){
        clientMap=new ConcurrentHashMap<>();
        clientList.forEach(client->{
            clientMap.put(readClientIdentifier(client),client);
        });
    }

    private String readClientIdentifier(ButtJointClientService client){
        ClientIdentifier annotation = client.getClass().getAnnotation(ClientIdentifier.class);
        Assert.notNull(annotation,"有对接客户端未定义名称");
        return annotation.name();
    }

    /**
     * 获取对接客户端
     * @param name
     * @return
     */
    public ButtJointClientService getClient(String name){ return clientMap.get(name); }
}
