package com.iaa.datacenter.module.buttjoint.service.landray.todo;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSONObject;
import com.anwen.mongo.conditions.query.QueryWrapper;
import com.iaa.datacenter.framework.buttjoint.constant.ButtJointNameConstants;
import com.iaa.datacenter.framework.buttjoint.service.ButtJointClientService;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.ekuaibao.EkuaibaoApproveMessageDO;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.erp.ErpApproveMessageDO;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.wework.WeworkUserDO;
import com.iaa.datacenter.module.buttjoint.framework.constant.LandrayApiConstant;
import com.iaa.datacenter.module.buttjoint.framework.factory.ButtJointClientFactory;
import com.iaa.datacenter.module.buttjoint.service.ekuaibao.message.EkuaibaoApproveMessageService;
import com.iaa.datacenter.module.buttjoint.service.wework.user.WeworkUserService;
import com.iaa.datacenter.module.system.api.social.SocialClientApi;
import com.iaa.datacenter.module.system.api.social.SocialUserApi;
import com.iaa.datacenter.module.system.api.user.AdminUserApi;
import com.iaa.datacenter.module.system.api.user.dto.AdminUserRespDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Objects;
import java.util.Optional;
import java.util.Map;

@Service
@Slf4j
public class LandrayTodoServiceImpl implements LandrayTodoService{

    @Resource
    private ButtJointClientFactory buttJointClientFactory;
    @Resource
    private AdminUserApi adminUserApi;
    @Resource
    private SocialClientApi socialClientApi;
    @Resource
    private WeworkUserService weworkUserService;

    /**
     * 合思审批节点与蓝凌阅读类、审批类通知对应
     */
    private final HashMap<String,Integer> todoTypeMap = new HashMap<String,Integer>(){{
        put("flow.rejected",2);//被驳回  -> 阅读类通知
        put("freeflow.ticketReserve",2);//审批通过
        put("freeflow.retract",2);//单据撤回
        put("freeflow.delete",2);//单据删除
        put("backlog.sending",2);//待寄送
        put("backlog.receiving",2);//待收单
        put("flow.paid",2);//已支付
        put("urgent.remind",2);//加急审批
        put("freeflow.mention",2);//被@
        put("freeflow.print",2);//打印提醒
        put("freeflow.comment",2);//评论
        put("freeflow.carbonCopy",2);//抄送
        put("backlog.paying",1);//待支付 -> 审批类通知
        put("backlog.approving",1);//待审批
        put("freeflow.remind",1);//催办
        put("timeout.remind",1);//超时提醒
    }};

    @Override
    public EkuaibaoApproveMessageDO sendToDo(EkuaibaoApproveMessageDO messageDO) {
        ButtJointClientService client = buttJointClientFactory.getClient(ButtJointNameConstants.LANDRAY);
        // 构建蓝凌待办消息体
        HashMap<String, Object> body = new HashMap<>();
        body.put("appName","合思费控");
        body.put("modelName","合思费控");

        if(messageDO.getAction().equals("flow.rejected")){
            body.put("modelId", IdUtil.randomUUID());
        }else{
            String uuid = Optional.ofNullable(messageDO.getBacklogId()).orElse(IdUtil.randomUUID());
            messageDO.setBacklogId(uuid);
            body.put("modelId", uuid);
        }
        if(messageDO.getAction().equals("backlog.processed")){
            body.put("optType",1);
            JSONObject object = client.post(LandrayApiConstant.SETTODODONE, new HashMap<>(), body);
            if (object.getInteger("returnState")==2){
                messageDO.setOrderStatus(1);
            }else{
                messageDO.setOrderStatus(2);
                messageDO.setErrorMsg(object.getString("message"));
            }
        }else{
            body.put("link",LandrayApiConstant.TODO_TO_URL+"flowId="+messageDO.getFlowId()+"&userId="+messageDO.getUserInfo().getString("id"));
            body.put("mobileLink",LandrayApiConstant.MOBILE_TODO_TO_URL+"flowId="+messageDO.getFlowId()+"&userId="+messageDO.getUserInfo().getString("id"));
            body.put("padLink",LandrayApiConstant.MOBILE_TODO_TO_URL+"flowId="+messageDO.getFlowId()+"&userId="+messageDO.getUserInfo().getString("id"));
            if(todoTypeMap.get(messageDO.getAction())==2){
                body.put("subject","你的单据:"+messageDO.getActionName()+" ["+messageDO.getTitle()+"],单号:"+messageDO.getCode());
            }else{
                body.put("subject","有单据"+messageDO.getActionName()+":"+messageDO.getSubmitterId().getString("name")+"的 ["+messageDO.getTitle()+"],单号:"+messageDO.getCode());
            }
            body.put("type",todoTypeMap.get(messageDO.getAction()));
            JSONObject user=new JSONObject();
            AdminUserRespDTO userInfo = adminUserApi.getUserByMobileOpen(messageDO.getUserInfo().getString("cellphone")).getData();
            user.put("LoginName",userInfo.getUsername());
            body.put("targets",user.toJSONString());
            body.put("createTime", DateUtil.now());
            body.put("level",messageDO.getUrgentApproval().equals("true")?1:3);
            JSONObject object = client.post(LandrayApiConstant.SENDTODO, new HashMap<>(), body);
            if (object.getInteger("returnState")==2){
                messageDO.setOrderStatus(1);
            }else{
                messageDO.setOrderStatus(2);
                messageDO.setErrorMsg(object.getString("message"));
            }
        }
        return messageDO;
    }

    @Override
    public ErpApproveMessageDO sendToDo(ErpApproveMessageDO messageDO) {
        ButtJointClientService client = buttJointClientFactory.getClient(ButtJointNameConstants.LANDRAY);
        // 构建蓝凌待办消息体
        HashMap<String, Object> body = new HashMap<>();
        body.put("appName","U9C审批");
        body.put("modelName","U9C审批");
        body.put("modelId","erp_approve_"+messageDO.getId());
        if(messageDO.getIsApproved()){
            body.put("optType",1);
            JSONObject posted = client.post(LandrayApiConstant.SETTODODONE, new HashMap<>(), body);
            log.info("蓝凌待办消息发送结果:"+posted);
            // todo 这是一个冗余处理 用来处理历史单据
            body.put("modelId",messageDO.getMessageId());
            JSONObject posted1 = client.post(LandrayApiConstant.SETTODODONE, new HashMap<>(), body);
        }else{
            body.put("link",LandrayApiConstant.U9C_TODO_URL+"messageID="+messageDO.getMessageId()+"&dataID="+messageDO.getId()+"&userID="+messageDO.getUserId());
            body.put("mobileLink",LandrayApiConstant.U9C_TODO_URL+"messageID="+messageDO.getMessageId()+"&dataID="+messageDO.getId()+"&userID="+messageDO.getUserId());
            body.put("padLink",LandrayApiConstant.U9C_TODO_URL+"messageID="+messageDO.getMessageId()+"&dataID="+messageDO.getId()+"&userID="+messageDO.getUserId());
            body.put("type",1);
            body.put("subject",
                    "请审批" +
                    extractValue(messageDO.getContent(),"创建人")+
                    extractValue(messageDO.getContent(),"变更人")+
                    "提交的流程：【"+
                    messageDO.getTitle()+"】"+
                    extractValue(messageDO.getContent(),"供应商")+
                    extractValue(messageDO.getContent(),"单号")
            );
            JSONObject userObj=new JSONObject();
            WeworkUserDO user = weworkUserService.getById(messageDO.getWechat());
            if(Objects.isNull(user)){
                String phone = socialClientApi.getPhoneByWeWork(messageDO.getWechat());
                user=new WeworkUserDO();
                user.setId(messageDO.getWechat());
                user.setPhone(phone);
                weworkUserService.save(user);
            }
            AdminUserRespDTO userInfo = adminUserApi.getUserByMobile(user.getPhone()).getData();
            userObj.put("LoginName",userInfo.getUsername());
            body.put("targets",userObj.toJSONString());
            body.put("createTime", DateUtil.now());
            body.put("level",3);
            JSONObject object = client.post(LandrayApiConstant.SENDTODO, new HashMap<>(), body);
            log.info("蓝凌待办消息发送结果:"+object);
            if (object.getInteger("returnState")==2){
                messageDO.setOrderStatus(1);
            }else{
                messageDO.setOrderStatus(2);
                messageDO.setErrorMsg(object.getString("message"));
            }
        }

        return messageDO;
    }


    /**
     * 从文本中截取从用户输入的第一个字符开始到第一个&符号结束的部分
     * @param text 输入的文本
     * @param startChar 用户输入的第一个字符
     * @return 截取的结果，如果没有匹配到则返回null
     */
    public static String extractValue(String text, String startChar) {
        // 找到用户输入的第一个字符的位置
        int startIndex = text.indexOf(startChar);
        if (startIndex == -1) {
            return ""; // 如果没有找到用户输入的第一个字符，返回null
        }

        // 找到从startIndex开始的第一个&符号的位置
        int endIndex = text.indexOf('&', startIndex);
        if (endIndex == -1) {
            return null; // 如果没有找到&符号，返回null
        }

        // 返回从startIndex到endIndex的部分
        return text.substring(startIndex, endIndex);
    }
}
