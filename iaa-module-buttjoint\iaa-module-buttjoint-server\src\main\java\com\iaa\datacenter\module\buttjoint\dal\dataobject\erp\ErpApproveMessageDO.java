package com.iaa.datacenter.module.buttjoint.dal.dataobject.erp;

import com.anwen.mongo.annotation.ID;
import com.anwen.mongo.annotation.collection.CollectionName;
import com.iaa.datacenter.framework.buttjoint.dataobject.BaseOrderDO;
import com.iaa.datacenter.framework.mybatis.core.dataobject.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@CollectionName("erp_approve")
public class ErpApproveMessageDO extends BaseOrderDO {
    /**
     * 审批ID
     */
    @ID
    private String id;
    /**
     * 标题
     */
    private String title;
    /**
     * 单据编号
     */
    private String content;
    /**
     * 微信号
     */
    private String wechat;
    /**
     * 用户ID
     */
    private String userId;
    /**
     * 审批消息ID
     */
    private String messageId;
    /**
     * 是否通过
     */
    private Boolean isApproved;

    private String createOn;

    private String taskId;
    /**
     * 处理操作
     */
    private Integer operation;
    /**
     * 状态信息
     */
    private Integer stateInfo;
    /**
     * 处理状态
     */
    private Integer tackState;
    /**
     * 流程状态
     */
    private Integer state;
}
