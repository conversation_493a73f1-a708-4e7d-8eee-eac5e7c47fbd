package com.iaa.datacenter.module.buttjoint.controller.admin.ekuaibao.order.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.iaa.datacenter.framework.common.annotation.WebTableColumn;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Data
@ExcelIgnoreUnannotated
@Schema(description = "管理后台 - 合思订单 Request VO")
public class EkuaibaoOrderRespVO {

    @ExcelProperty("单据ID")
    @WebTableColumn(label="单据ID",noSearch = true,width = 120)
    @NotNull(message="单据编号不能为空")
    private String id;

    @ExcelProperty("单据编码")
    @WebTableColumn(label="单据编号",width = 80)
    private String code;

    @ExcelProperty("单据类型")
    @WebTableColumn(label="单据类型",dict = "ekuaibao_order_type",width = 80)
    private String formType;

    @ExcelProperty("单据状态")
    @WebTableColumn(label="单据状态",dict = "ekuaibao_order_state",width = 80)
    private String state;

    @ExcelProperty("同步状态")
    @WebTableColumn(label="同步状态",dict = "ekuaibao_to_erp_status",width = 110)
    private Integer orderStatus;

    @ExcelProperty("组织")
    @WebTableColumn(label="组织",dict = "erp_org_value",width = 80)
    private String org;

    @ExcelProperty("支付时间")
    @WebTableColumn(label="支付时间",date=true,width = 140)
    private String payDate;

    @ExcelProperty("付款单号")
    @WebTableColumn(label="付款单号",width = 120)
    private String payCode;

    @ExcelProperty("费用报销发票")
    @WebTableColumn(label="费用报销发票",width = 200)
    private String apCode;

    @ExcelProperty("凭证编号")
    @WebTableColumn(label="凭证编号",width = 120)
    private String voucherCode;

    @ExcelProperty("同步消息")
    @WebTableColumn(label="同步消息",width = 400,noSearch = true)
    private String errorMsg;

    @ExcelProperty("创建人")
    @WebTableColumn(label="创建人",width = 80,noSearch = true)
    private String createBy;

    @WebTableColumn(exists = true)
    private String deptName;

    @WebTableColumn(exists = true)
    private List<EkuaibaoOrderItemRespVO> feeItems;

    @WebTableColumn(exists = true)
    private List<EkuaibaoOrderItemRespVO> writeOffItems;

}
