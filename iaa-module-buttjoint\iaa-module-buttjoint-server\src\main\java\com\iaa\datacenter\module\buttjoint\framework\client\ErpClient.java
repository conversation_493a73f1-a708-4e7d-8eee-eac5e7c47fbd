package com.iaa.datacenter.module.buttjoint.framework.client;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.http.*;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.iaa.datacenter.framework.buttjoint.annotation.ClientIdentifier;
import com.iaa.datacenter.framework.buttjoint.config.ButtJointConfig;
import com.iaa.datacenter.framework.buttjoint.config.ButtJointProperties;
import com.iaa.datacenter.framework.buttjoint.constant.ButtJointNameConstants;
import com.iaa.datacenter.framework.buttjoint.dataobject.ButtJointAuthorization;
import com.iaa.datacenter.framework.buttjoint.service.ButtJointClientService;
import com.iaa.datacenter.framework.common.util.json.JsonUtils;
import com.iaa.datacenter.module.buttjoint.service.buttjointauth.ButtJointAuthService;
import jakarta.annotation.Nullable;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Service
@ClientIdentifier(name=ButtJointNameConstants.ERP)
public class ErpClient implements ButtJointClientService {

    @Resource
    private ButtJointAuthService buttJointAuthService;

    @Resource
    private ButtJointProperties buttJointProperties;
    @Override
    public JSONObject auth() {
        return null;
    }

    @Override
    public JSONObject auth(String code) {
        ButtJointAuthorization auth = buttJointAuthService.getById(code+ButtJointNameConstants.ERP);
        if(Objects.nonNull(auth)){
            // 判断是否过期
            if(auth.getExpireTime()>System.currentTimeMillis()){
                return auth.getAuthorization();
            }else{
                return refresh(code).getAuthorization();
            }
        }else{
            return refresh(code).getAuthorization();
        }
    }

    /**
     * 刷新token
     * @return
     */
    private ButtJointAuthorization refresh(String orgCode){
        ButtJointConfig ekuaibaoConfig = buttJointProperties.getType().get(ButtJointNameConstants.ERP);
        JSONObject token = getToken(ekuaibaoConfig,orgCode);

        ButtJointAuthorization auth = ButtJointAuthorization.builder()
                .id(orgCode+ButtJointNameConstants.ERP)
                .authorization(token)
                .expireTime(System.currentTimeMillis()+(1000*60*59))
                .build();

        Boolean exist = buttJointAuthService.exist(auth.getId());
        if(exist){
            buttJointAuthService.updateById(auth);
        }else{
            buttJointAuthService.save(auth);
        }
        return auth;
    }
    /**
     * 获取授权
     * @param ekuaibaoConfig
     * @return
     */
    private JSONObject getToken(ButtJointConfig ekuaibaoConfig,String orgCode){
        HashMap<String, Object> params = new HashMap<>();
        params.put("clientid",ekuaibaoConfig.getAppKey());
        params.put("clientsecret",ekuaibaoConfig.getAppSecret());
        params.put("entCode",ekuaibaoConfig.getEntCode());
        params.put("userCode",ekuaibaoConfig.getUserCode());
        params.put("orgCode",orgCode);
        try (HttpResponse response = HttpUtil.createGet(HttpUtil.urlWithFormUrlEncoded(ekuaibaoConfig.getUrl()+"OAuth2/AuthLogin",params, StandardCharsets.UTF_8))
                .contentType(ContentType.FORM_URLENCODED.getValue())
                .execute()){
            return JsonUtils.parseObject(response.body(),JSONObject.class);
        }
    }
    @Override
    public JSONObject readObj(String url, Method type, Map<String, Object> query, Map<String, Object> body,String ...code) {
        return getJsonObject(url, type, query, CollUtil.isNotEmpty(body), JSON.toJSONString(body), code);
    }

    @Override
    public JSONObject readObj(String url, Method type, Map<String, Object> query, ArrayList<Object> body, String... code) {
        return getJsonObject(url, type, query, CollUtil.isNotEmpty(body), JSON.toJSONString(body), code);
    }

    @Nullable
    private JSONObject getJsonObject(String url, Method type, Map<String, Object> query, boolean notEmpty, String jsonString, String[] code) {
        String auth = auth(code[0]).getString("Data");
        HttpRequest request = HttpUtil.createRequest(type, HttpUtil.urlWithFormUrlEncoded(buttJointProperties.getType().get(ButtJointNameConstants.ERP).getUrl()+url,query, StandardCharsets.UTF_8));
        request.addHeaders(new HashMap<>(){{
            put("token",auth);
        }});
        if(notEmpty){
            request.contentType(ContentType.JSON.getValue()).body(jsonString);
        }
        try(HttpResponse response = request.execute()){
            return JsonUtils.parseObject(response.body(),JSONObject.class);
        }
    }
}
