<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.iaa.datacenter</groupId>
        <artifactId>iaa-module-buttjoint</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>iaa-module-buttjoint-server</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>

    <dependencies>
        <!-- Spring Cloud 基础 -->
        <dependency>
            <groupId>com.iaa.datacenter</groupId>
            <artifactId>iaa-spring-boot-starter-env</artifactId>
        </dependency>

        <!-- 依赖服务 -->
        <dependency>
            <groupId>com.iaa.datacenter</groupId>
            <artifactId>iaa-module-buttjoint-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.iaa.datacenter</groupId>
            <artifactId>iaa-module-system-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.iaa.datacenter</groupId>
            <artifactId>iaa-module-infra-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- RPC 远程调用相关 -->
        <dependency>
            <groupId>com.iaa.datacenter</groupId>
            <artifactId>iaa-spring-boot-starter-rpc</artifactId>
        </dependency>

        <!-- Registry 注册中心相关 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <!-- Config 配置中心相关 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <!-- Web 相关 -->
        <dependency>
            <groupId>com.iaa.datacenter</groupId>
            <artifactId>iaa-spring-boot-starter-security</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <dependency>
            <groupId>com.iaa.datacenter</groupId>
            <artifactId>iaa-spring-boot-starter-mongo</artifactId>
        </dependency>

        <dependency>
            <groupId>com.iaa.datacenter</groupId>
            <artifactId>iaa-spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.iaa.datacenter</groupId>
            <artifactId>iaa-spring-boot-starter-job</artifactId>
        </dependency>

        <dependency>
            <groupId>com.iaa.datacenter</groupId>
            <artifactId>iaa-spring-boot-starter-mybatis</artifactId>
        </dependency>

        <!-- 工具类相关 -->
        <dependency>
            <groupId>com.iaa.datacenter</groupId>
            <artifactId>iaa-spring-boot-starter-excel</artifactId>
        </dependency>
        <!-- Test 测试相关 -->
        <dependency>
            <groupId>com.iaa.datacenter</groupId>
            <artifactId>iaa-spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.iaa.datacenter</groupId>
            <artifactId>iaa-spring-boot-starter-protection</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>wx-java-cp-spring-boot-starter</artifactId>
        </dependency>

    </dependencies>

    <build>
        <!-- 设置构建的 jar 包名 -->
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <!-- 打包 -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring.boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal> <!-- 将引入的 jar 打入其中 -->
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
