package com.iaa.datacenter.module.buttjoint.controller.admin.backend;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.iaa.datacenter.framework.common.pojo.CommonResult;
import com.iaa.datacenter.framework.mybatis.core.dataobject.BaseDO;
import com.iaa.datacenter.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.backend.EngBomComparisonDO;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.backend.EngBomComparisonRuleDO;
import com.iaa.datacenter.module.buttjoint.dal.mapper.backend.EngBomComparisonMapper;
import com.iaa.datacenter.module.buttjoint.dal.mapper.backend.EngBomComparisonRuleMapper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Tag(name = "管理后台 - BOM 比对记录及规则")
@RestController
@RequestMapping("/butt-joint/backend/bom-comparison")
@Validated
@Slf4j
public class EngBomComparisonController {
    @Resource
    private EngBomComparisonMapper engBomComparisonMapper;
    @Resource
    private EngBomComparisonRuleMapper engBomComparisonRuleMapper;


    @PostMapping("/create-rule")
    @Operation(summary = "创建推送规则")
    @PreAuthorize("@ss.hasPermission('bom:comparison:create')")
    public CommonResult<Boolean> createBomComparisonRule(@RequestBody EngBomComparisonRuleDO createReqVO) {
        engBomComparisonRuleMapper.delete(new LambdaQueryWrapperX<EngBomComparisonRuleDO>().eq(BaseDO::getDeleted,false));
        engBomComparisonRuleMapper.insert(createReqVO);
        return CommonResult.success(true);
    }

    @PostMapping("/get-rule")
    @Operation(summary = "获取推送规则")
    @PreAuthorize("@ss.hasPermission('bom:comparison:get')")
    public CommonResult<EngBomComparisonRuleDO> getBomComparisonRule(){
        return CommonResult.success(engBomComparisonRuleMapper.selectOne(new LambdaQueryWrapperX<EngBomComparisonRuleDO>().eq(BaseDO::getDeleted,false)));
    }

    @PostMapping("/list")
    @Operation(summary = "获取推送规则")
    @PreAuthorize("@ss.hasPermission('bom:comparison:list')")
    public CommonResult<List<EngBomComparisonDO>> getBomComparisonList(@RequestParam LocalDate date){
        List<EngBomComparisonDO> list = engBomComparisonMapper.selectList(new QueryWrapper<EngBomComparisonDO>().eq("cast(create_time as date)", date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))));
        return  CommonResult.success(list);
    }
}
