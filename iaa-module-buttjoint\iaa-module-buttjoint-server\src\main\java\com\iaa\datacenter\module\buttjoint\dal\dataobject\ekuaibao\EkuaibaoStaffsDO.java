package com.iaa.datacenter.module.buttjoint.dal.dataobject.ekuaibao;

import com.alibaba.fastjson.JSONArray;
import com.anwen.mongo.annotation.ID;
import com.anwen.mongo.annotation.collection.CollectionField;
import com.anwen.mongo.annotation.collection.CollectionLogic;
import com.anwen.mongo.annotation.collection.CollectionName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.List;

/**
 * 合思员工信息
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@CollectionName("ekuaibao_user")
public class EkuaibaoStaffsDO {
    @ID
    private String id;
    /**
     * 姓名
     */
    private String name;
    /**
     * 昵称
     */
    private String nickName;
    /**
     * 编码
     */
    private String code;
    /**
     * 部门集合
     */
    private JSONArray departments;
    /**
     * 默认部门
     */
    private String defaultDepartment;

    @CollectionField(exist = false)
    private String deptName;
    /**
     * 手机号
     */
    private String cellphone;
    /**
     * 可用性
     */
    private Boolean active;
    /**
     * 第三方平台人员ID
     */
    private String userId;
    /**
     * 登录邮箱
     */
    private String email;
    /**
     * 是否外部员工
     */
    private Boolean external;
    /**
     * 是否激活
     */
    private Boolean authState;
    /**
     * 国际区号
     */
    private String globalRoaming;
    /**
     * 备注
     */
    private String note;
    /**
     * 更新时间
     */
    private String updateTime;
    /**
     * 创建时间
     */
    private String createTime;

    /**
     * erp用户
     */
    private  List<String> erpSellerCode;

    private String erpUserCode;
}
