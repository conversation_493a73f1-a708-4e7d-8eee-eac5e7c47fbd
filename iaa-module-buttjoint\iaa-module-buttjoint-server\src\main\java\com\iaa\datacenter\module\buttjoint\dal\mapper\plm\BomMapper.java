package com.iaa.datacenter.module.buttjoint.dal.mapper.plm;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.plm.PlmBomTopDO;
import lombok.Data;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
@DS("plm")
public interface BomMapper {

//    where BOMSTATE='frozen' and PARTFIXEDBACK4='1'
//    and BOM_027.CFROZENTIME between #{startTime} and #{endTime}
//    order by BOM_054.BOMACHIVETIME desc
    @Select("""
            select
                BOM_054.BOMNAME as bom_name,
                BOM_027.PARTID as item_code,
                BOM_054.CFROZENTIME frozen_time,
                BOM_094.PackagingComplete as packaging_complete,
                BOM_094.ProgramComplete as program_complete,
                BOM_094.PanelComplete as panel_complete,
                BOM_094.OtherCompelete as other_complete
            from BOM_054
            join BOM_027 on BOM_054.PARTID=BOM_027.PARTID and BOM_054.PARTVAR=BOM_027.PARTVAR
            left join BOM_094 on BOM_054.PARTID=BOM_094.PARTID and BOM_054.PARTVAR=BOM_094.PARTVAR
            ${ew.getCustomSqlSegment}
            """)
    List<PlmBomTopDO> selectBomTop(@Param("ew")QueryWrapper<?> wrapper);

    @Select("""
            select
                concat(PARENTID,'-',PARTID,'-',ASSEMBLELEVEL-1)
            from BOM_016
            where BOMNAME=#{bomName}
            and SEQNUM !='1'
            order by concat(PARENTID,'-',PARTID,'-',ASSEMBLELEVEL-1)
            """)
    List<String> selectBomItemList(String bomName);
}
