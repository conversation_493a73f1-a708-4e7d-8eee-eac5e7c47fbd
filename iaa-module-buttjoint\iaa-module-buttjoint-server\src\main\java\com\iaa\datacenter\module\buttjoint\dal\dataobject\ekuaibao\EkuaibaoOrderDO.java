package com.iaa.datacenter.module.buttjoint.dal.dataobject.ekuaibao;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.anwen.mongo.annotation.ID;
import com.anwen.mongo.annotation.collection.CollectionField;
import com.anwen.mongo.annotation.collection.CollectionName;
import com.iaa.datacenter.framework.buttjoint.dataobject.BaseOrderDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@CollectionName("ekuaibao_orders")
public class EkuaibaoOrderDO extends BaseOrderDO {

    @ID
    private String id;
    /**
     * 应用ID
     */
    private String appId;
    /**
     * 是否有发票
     */
    private Boolean inviceRemind;
    /**
     * 事件记录
     */
    private JSONObject actions;
    /**
     * 日志
     */
    private JSONArray logs;
    //单据类型
    private String formType;
    //流程类型
    private String flowType;
    //单据状态
    private String state;
    //用户所属部门
    private String ownerDefaultDepartment;
    //用户ID
    private String ownerId;
    //创建人
    @CollectionField(exist = false)
    private String ownerName;
    //表单信息
    private JSONObject form;
    //数据所属公司ID
    private String dataCorporationId;
    //来源公司ID
    private String sourceCorporationId;
    //公司ID
    private String corporationId;
    //更新时间
    private Long updateTime;
    //创建时间
    private Long createTime;
    @CollectionField(exist = false)
    private String updateTimeStamp;
    //?
    private Boolean active;
    //版本
    private Integer version;
    private String threadId;
    private Integer dbVersion;
    private String grayver;
    private Integer pipeline;
    //付款单号
    private String payCode;
    //费用报销发票单号
    private ArrayList<String> apCode;
    //凭证单号
    private String voucherCode;
}
