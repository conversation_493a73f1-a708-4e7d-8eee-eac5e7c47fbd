package com.iaa.datacenter.module.buttjoint.dal.dataobject.ekuaibao;

import com.anwen.mongo.annotation.ID;
import com.anwen.mongo.annotation.collection.CollectionField;
import com.anwen.mongo.annotation.collection.CollectionLogic;
import com.anwen.mongo.annotation.collection.CollectionName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.List;
/**
 * 合思自定义档案类
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@CollectionName("ekuaibao_custom")
public class EkuaibaoCustomDO {

    @ID
    private String id;
    /**
     * 编码
     */
    private String code;
    /**
     * 分类ID
     */
    private String dimensionId;
    /**
     * 名称
     */
    private String name;
    /**
     * 父项名称
     */
    @CollectionField(exist = false)
    private String parentName;
    /**
     * 启用状态
     */
    private Boolean active;
    /**
     * 父ID
     */
    private String parentId;

    private String u9cId;

    private List<String> u9cCode;
}
