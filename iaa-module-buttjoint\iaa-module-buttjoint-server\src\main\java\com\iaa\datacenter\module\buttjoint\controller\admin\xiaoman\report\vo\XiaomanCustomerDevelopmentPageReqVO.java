package com.iaa.datacenter.module.buttjoint.controller.admin.xiaoman.report.vo;

import com.iaa.datacenter.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Data
public class XiaomanCustomerDevelopmentPageReqVO extends PageParam {

    @Schema(description = "商机时间")
    @NotNull(message = "商机数据时间范围不能为空")
    @NotEmpty(message = "商机数据时间范围不能为空")
    private String[] opportunityTime;

    @Schema(description = "业务员")
    private List<Long> salesperson;

    @Schema(description = "组别")
    private List<Long> departmentName;

    @Schema(description = "客户名称")
    private String customerName;

    @Schema(description = "国家/地区")
    private String countryName;

    @Schema(description = "洲/省")
    private String regionOrProvince;

    @Schema(description = "城市")
    private String city;

    @Schema(description = "合作阶段")
    List<String> trailStatus;

    @Schema(description = "客户画像")
    private List<String> customerPortraits;

    @Schema(description = "客户性质")
    private String nature;

    @Schema(description = "客户官网")
    private String homepage;

    @Schema(description = "客户营收规模评估")
    private List<String> revenueScale;

    @Schema(description = "香氛产品规模评估")
    private List<String> fragranceRevenueScale;

    @Schema(description = "开发类型")
    private List<String> developmentType;

    @Schema(description = "进展")
    private List<String> stageInfo;

    @Schema(description = "作战产品")
    private String combatProduct;
}
