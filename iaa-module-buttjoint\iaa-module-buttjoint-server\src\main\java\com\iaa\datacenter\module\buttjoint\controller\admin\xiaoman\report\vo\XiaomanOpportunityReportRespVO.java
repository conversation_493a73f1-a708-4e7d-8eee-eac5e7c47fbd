package com.iaa.datacenter.module.buttjoint.controller.admin.xiaoman.report.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@ExcelIgnoreUnannotated
@Schema(description = "管理后台 - 小满机会点明细表")
public class XiaomanOpportunityReportRespVO extends XiaomanCustomerBaseInfoVO{

    @ExcelProperty("竟对机器类数据描述")
    @Schema(description = "竟对机器类数据描述")
    private String untoMachineRemark;

    @ExcelProperty("竟对精油类数据描述")
    @Schema(description = "竟对精油类数据描述")
    private String untoEssentialOilRemark;

    @ExcelProperty("相关性产品描述")
    @Schema(description = "相关性产品描述")
    private String relevantProductRemark;

    @ExcelProperty({"合作机器类产品TOP5","产品1","型号"})
    @Schema(description = "产品1 型号")
    private String machineModel1;
    @ExcelProperty({"合作机器类产品TOP5","产品1","金额"})
    @Schema(description = "产品1 金额")
    private Double machinePrice1;

    @ExcelProperty({"合作机器类产品TOP5","产品2","型号"})
    @Schema(description = "产品2 型号")
    private String machineModel2;
    @ExcelProperty({"合作机器类产品TOP5","产品2","金额"})
    @Schema(description = "产品2 金额")
    private Double machinePrice2;

    @ExcelProperty({"合作机器类产品TOP5","产品3","型号"})
    @Schema(description = "产品3 型号")
    private String machineModel3;
    @ExcelProperty({"合作机器类产品TOP5","产品3","金额"})
    @Schema(description = "产品3 金额")
    private Double machinePrice3;

    @ExcelProperty({"合作机器类产品TOP5","产品4","型号"})
    @Schema(description = "产品4 型号")
    private String machineModel4;
    @ExcelProperty({"合作机器类产品TOP5","产品4","金额"})
    @Schema(description = "产品4 金额")
    private Double machinePrice4;

    @ExcelProperty({"合作机器类产品TOP5","产品5","型号"})
    @Schema(description = "产品5 型号")
    private String machineModel5;
    @ExcelProperty({"合作机器类产品TOP5","产品5","金额"})
    @Schema(description = "产品5 金额")
    private Double machinePrice5;

    @ExcelProperty({"合作机器类产品TOP5","机器合计金额"})
    @Schema(description = "机器合计金额")
    private Double machineTotalPrice;

    @ExcelProperty({"合作精油类产品TOP5","香型1","香型"})
    @Schema(description = "香型1 香型")
    private String oilModel1;
    @ExcelProperty({"合作精油类产品TOP5","香型1","金额"})
    @Schema(description = "香型1 金额")
    private Double oilPrice1;

    @ExcelProperty({"合作精油类产品TOP5","香型2","香型"})
    @Schema(description = "香型2 香型")
    private String oilModel2;
    @ExcelProperty({"合作精油类产品TOP5","香型2","金额"})
    @Schema(description = "香型2 金额")
    private Double oilPrice2;

    @ExcelProperty({"合作精油类产品TOP5","香型3","香型"})
    @Schema(description = "香型3 香型")
    private String oilModel3;
    @ExcelProperty({"合作精油类产品TOP5","香型3","金额"})
    @Schema(description = "香型3 金额")
    private Double oilPrice3;

    @ExcelProperty({"合作精油类产品TOP5","香型4","香型"})
    @Schema(description = "香型4 香型")
    private String oilModel4;
    @ExcelProperty({"合作精油类产品TOP5","香型4","金额"})
    @Schema(description = "香型4 金额")
    private Double oilPrice4;

    @ExcelProperty({"合作精油类产品TOP5","香型5","香型"})
    @Schema(description = "香型5 香型")
    private String oilModel5;
    @ExcelProperty({"合作精油类产品TOP5","香型5","金额"})
    @Schema(description = "香型5 金额")
    private Double oilPrice5;

    @ExcelProperty({"合作精油类产品TOP5","精油合计金额"})
    @Schema(description = "精油合计金额")
    private Double oilTotalPrice;
}


