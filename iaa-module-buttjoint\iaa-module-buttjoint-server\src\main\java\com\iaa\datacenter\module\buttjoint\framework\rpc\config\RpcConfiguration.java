package com.iaa.datacenter.module.buttjoint.framework.rpc.config;

import com.iaa.datacenter.module.infra.api.config.ConfigApi;
import com.iaa.datacenter.module.system.api.social.SocialClientApi;
import com.iaa.datacenter.module.system.api.social.SocialUserApi;
import com.iaa.datacenter.module.system.api.user.AdminUserApi;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;

@Configuration(value = "buttjointRpcConfiguration", proxyBeanMethods = false)
@EnableFeignClients(clients = {ConfigApi.class, AdminUserApi.class, SocialClientApi.class, SocialUserApi.class})
public class RpcConfiguration {
}
