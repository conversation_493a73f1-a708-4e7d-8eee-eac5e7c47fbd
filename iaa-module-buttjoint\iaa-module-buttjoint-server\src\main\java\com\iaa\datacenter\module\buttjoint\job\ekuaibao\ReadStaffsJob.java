package com.iaa.datacenter.module.buttjoint.job.ekuaibao;

import com.alibaba.fastjson.JSONObject;
import com.iaa.datacenter.framework.buttjoint.constant.ButtJointNameConstants;
import com.iaa.datacenter.framework.buttjoint.service.ButtJointClientService;
import com.iaa.datacenter.framework.quartz.core.handler.JobHandler;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.ekuaibao.EkuaibaoStaffsDO;
import com.iaa.datacenter.module.buttjoint.framework.constant.EkuaibaoApiConstant;
import com.iaa.datacenter.module.buttjoint.framework.factory.ButtJointClientFactory;
import com.iaa.datacenter.module.buttjoint.service.ekuaibao.staffs.EkuaibaoStaffsService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;


/**
 * 读取合思员工数据任务
 */
@Component
@Slf4j
public class ReadStaffsJob implements JobHandler {

    @Resource
    private ButtJointClientFactory buttJointClientFactory;
    @Resource
    private EkuaibaoStaffsService ekuaibaoStaffsService;
    @Override
    public String execute(String param) throws Exception {
        ButtJointClientService ekuaibao = buttJointClientFactory.getClient(ButtJointNameConstants.EKUAIBAO);
        HashMap<String, Object> query = new HashMap<>();
        int start=0,count;
        query.put("count",100);
        List<EkuaibaoStaffsDO> dataList = new ArrayList<>();
        do {
            query.put("start",start);
            JSONObject result = ekuaibao.get(EkuaibaoApiConstant.STAFFS, query);
            count=result.getInteger("count");
            dataList.addAll(result.getJSONArray("items").toJavaList(EkuaibaoStaffsDO.class));
            start+=100;
        }while (start<=count);
//        // 已经存在本地数据库的数据不更新，防止覆盖本地配置
//        List<EkuaibaoStaffsDO> saveList = new ArrayList<>();
//        for (EkuaibaoStaffsDO ekuaibaoStaffsDO : dataList) {
//            if (!ekuaibaoStaffsService.exist(ekuaibaoStaffsDO.getId())) {
//                saveList.add(ekuaibaoStaffsDO);
//            }
//        }
//        if(!saveList.isEmpty()){
//            ekuaibaoStaffsService.saveOrUpdateBatch(saveList,true);
//        }
//        log.info("同步合思用户成功，数据条数：{}",saveList.size());
        ekuaibaoStaffsService.saveOrUpdateBatch(dataList,true);
        return "同步合思用户成功："+dataList.size()+"条";
    }
}
