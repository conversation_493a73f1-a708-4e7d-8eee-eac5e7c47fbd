package com.iaa.datacenter.module.buttjoint.service.xiaoman.customer;

import com.iaa.datacenter.framework.common.pojo.PageResult;
import com.iaa.datacenter.module.buttjoint.controller.admin.xiaoman.report.vo.XiaomanOrderPageReqVO;
import com.iaa.datacenter.module.buttjoint.controller.admin.xiaoman.report.vo.XiaomanOrderRespVO;
import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.net.http.HttpResponse;
import java.util.List;

public interface XiaomanOrderService {

    /**
     * 获取小满客户滚动订单表
     * @param pageReqVO
     * @return
     */
    PageResult<XiaomanOrderRespVO> getXiaomanOrderPage(XiaomanOrderPageReqVO pageReqVO);

    /**
     * 计算订单总金额
     * @param contentList
     * @return
     */
    XiaomanOrderRespVO calculateOrderTotal(List<XiaomanOrderRespVO> contentList);

    /**
     * 导出小满客户滚动订单表
     * @param pageReqVO
     * @param contentList
     */
    void exportXiaomanOrderPage(HttpServletResponse response, XiaomanOrderPageReqVO pageReqVO, List<XiaomanOrderRespVO> contentList) throws Exception;
}
