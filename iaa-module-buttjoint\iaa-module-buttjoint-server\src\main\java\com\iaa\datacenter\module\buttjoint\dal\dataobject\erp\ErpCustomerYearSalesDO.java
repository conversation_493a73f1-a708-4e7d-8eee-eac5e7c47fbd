package com.iaa.datacenter.module.buttjoint.dal.dataobject.erp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * ERP 客户年销售额
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ErpCustomerYearSalesDO {
    /**
     * 客户编码
     */
    private String customerCode;
    /**
     * 客户名称
     */
    private String customerName;
    /**
     * 年份
     */
    private String year;
    /**
     * 总销售额（本币）
     */
    private Double totalSales;

}
