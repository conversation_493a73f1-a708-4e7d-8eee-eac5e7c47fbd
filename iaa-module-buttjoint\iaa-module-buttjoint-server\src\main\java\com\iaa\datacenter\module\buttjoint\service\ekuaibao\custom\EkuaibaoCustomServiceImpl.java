package com.iaa.datacenter.module.buttjoint.service.ekuaibao.custom;

import com.alibaba.fastjson.JSONObject;
import com.anwen.mongo.service.impl.ServiceImpl;
import com.iaa.datacenter.framework.buttjoint.constant.ButtJointNameConstants;
import com.iaa.datacenter.framework.buttjoint.service.ButtJointClientService;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.ekuaibao.EkuaibaoCustomDO;
import com.iaa.datacenter.module.buttjoint.framework.constant.EkuaibaoApiConstant;
import com.iaa.datacenter.module.buttjoint.framework.factory.ButtJointClientFactory;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Objects;

import static com.iaa.datacenter.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.iaa.datacenter.module.buttjoint.ErrorCodeConstants.SEND_EKUAIBAO_ERROR;

@Service
public class EkuaibaoCustomServiceImpl extends ServiceImpl<EkuaibaoCustomDO> implements EkuaibaoCustomService {
    @Resource
    private ButtJointClientFactory buttJointClientFactory;
    @Override
    public void sendDataToCustom(String dimensionId, String code, String name) {
        ButtJointClientService ekuaibao = buttJointClientFactory.getClient(ButtJointNameConstants.EKUAIBAO);
        HashMap<String, Object> body = new HashMap<>();
        body.put("dimensionId",dimensionId);
        body.put("code",code);
        body.put("name",name);
        body.put("parentId","");

        JSONObject result = ekuaibao.post(EkuaibaoApiConstant.DIMENSIONS_ITEM, new HashMap<>(), body);

        if(!result.containsKey("id")) throw exception(SEND_EKUAIBAO_ERROR);
        Boolean save = save(EkuaibaoCustomDO.builder()
                .id(result.getString("id"))
                .dimensionId(dimensionId)
                .active(true)
                .code(code)
                .name(name)
                .parentId("")
                .u9cId("")
                .u9cCode(new ArrayList<>())
                .build()
        );
        if(!save) throw exception(SEND_EKUAIBAO_ERROR);
    }
}
