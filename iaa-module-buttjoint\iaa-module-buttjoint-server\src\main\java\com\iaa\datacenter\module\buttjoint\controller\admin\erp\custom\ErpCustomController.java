package com.iaa.datacenter.module.buttjoint.controller.admin.erp.custom;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iaa.datacenter.framework.common.core.KeyValue;
import com.iaa.datacenter.framework.common.pojo.CommonResult;
import com.iaa.datacenter.framework.common.pojo.PageResult;
import com.iaa.datacenter.framework.common.pojo.WebTableColumn;
import com.iaa.datacenter.framework.common.util.object.BeanUtils;
import com.iaa.datacenter.framework.common.util.object.WebColumnUtils;
import com.iaa.datacenter.module.buttjoint.controller.admin.erp.custom.vo.ErpCustomPageReqVO;
import com.iaa.datacenter.module.buttjoint.controller.admin.erp.custom.vo.ErpCustomRespVO;
import com.iaa.datacenter.module.buttjoint.controller.admin.erp.custom.vo.ErpItemRespVO;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.erp.ErpOrderDeliveryDO;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.erp.ErpProjectDO;
import com.iaa.datacenter.module.buttjoint.dal.mapper.erp.ErpOrderDeliveryMapper;
import com.iaa.datacenter.module.buttjoint.service.erp.custom.ErpCustomService;
import com.iaa.datacenter.module.buttjoint.service.erp.customer.ErpCustomerService;
import com.iaa.datacenter.module.buttjoint.service.erp.project.ErpProjectService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.iaa.datacenter.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - ERP 项目 操作")
@RestController
@RequestMapping("/butt-joint/erp/project")
@Validated
@Slf4j
public class ErpCustomController {

    @Resource
    private ErpProjectService erpProjectService;
    @Resource
    private ErpCustomService erpCustomService;
    @Resource
    private ErpCustomerService erpCustomerService;

    @Resource
    private ErpOrderDeliveryMapper erpOrderDeliveryMapper;

    @GetMapping("/get-columns")
    @Operation(summary = "获取ERP档案列信息")
    @PreAuthorize("@ss.hasPermission('erp:send:list')")
    public CommonResult<List<WebTableColumn>> getColumns(){
        return success(WebColumnUtils.parseTableColumn(ErpCustomRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获取ERP项目数据")
    @PreAuthorize("@ss.hasPermission('erp:send:list')")
    public CommonResult<PageResult<ErpCustomRespVO>> page(@Valid ErpCustomPageReqVO reqVO){
        PageResult<ErpProjectDO> page = erpProjectService.page(reqVO);
        return success(BeanUtils.toBean(page, ErpCustomRespVO.class));
    }

    @PostMapping("/update-jst-token")
    @Operation(summary = "更新聚水潭Token")
    @PreAuthorize("@ss.hasPermission('erp:send:list')")
    public CommonResult<Boolean> updateJstToken(@RequestParam String token){
        erpCustomService.updateJstToken(token);
        return success(true);
    }

    @GetMapping("/get-jst-token")
    @Operation(summary = "获取聚水潭token数据")
    @PreAuthorize("@ss.hasPermission('erp:send:list')")
    public CommonResult<List<ErpCustomRespVO>> getJstToken(){
        List<KeyValue<String, String>> jstToken = erpCustomService.getJstToken();
        List<ErpCustomRespVO> result = new ArrayList<>();
        for (KeyValue<String, String> stringStringKeyValue : jstToken) {
            result.add(new ErpCustomRespVO(stringStringKeyValue.getKey(),stringStringKeyValue.getValue()));
        }
        return success(result);
    }

    @GetMapping("/get-customer")
    @Operation(summary = "获取客户信息")
    public CommonResult<List<ErpCustomRespVO>> getCustomerList(@Valid ErpCustomPageReqVO pageReqVO){
        return success(erpCustomerService.getCustomerList(pageReqVO));
    }

    @GetMapping("/get-item")
    @Operation(summary = "获取料品信息")
    public CommonResult<List<ErpItemRespVO>> getItemList(@RequestParam String itemCode){
         return success(erpCustomerService.getItemList(itemCode));
    }

    @GetMapping("/get-order-delivery-list")
    @Operation(summary = "获取料品信息")
    public CommonResult<PageResult<ErpOrderDeliveryDO>> getOrderDeliveryList(@RequestParam Integer pageNo,
                                                                        @RequestParam Integer pageSize,
                                                                        @RequestParam String dateType,
                                                                        @RequestParam String sTime){
        // 使用标准分页构造方法并显式设置参数名称
        Page<ErpOrderDeliveryDO> page = new Page<>();
        page.setCurrent(pageNo);
        page.setSize(pageSize);
        Page<ErpOrderDeliveryDO> result = erpOrderDeliveryMapper.selectPage(page, dateType, sTime);
        return success(new PageResult<>(result.getRecords(),result.getTotal()));
    }

    @GetMapping("/get-order-delivery-rate")
    @Operation(summary = "获取料品信息")
    public CommonResult<Map<String, BigDecimal>> getOrderDeliveryRate(@RequestParam String dateType,
                                                                      @RequestParam String sTime){
        List<ErpOrderDeliveryDO> erpOrderDeliveryDOS = erpOrderDeliveryMapper.selectPage(dateType, sTime);
        LinkedHashMap<String, List<ErpOrderDeliveryDO>> data = erpOrderDeliveryDOS.stream().collect(
                Collectors.groupingBy(
                        ErpOrderDeliveryDO::getDeliveryMonth,
                        LinkedHashMap::new,
                        Collectors.toList())
        );
        Map<String, BigDecimal> result = new LinkedHashMap<>();
        for (Map.Entry<String, List<ErpOrderDeliveryDO>> entry : data.entrySet()) {
            result.put(entry.getKey(),entry.getValue().get(0).getDeliveryRate());
        }
        return success(result);
    }
}
