package com.iaa.datacenter.framework.common.pojo;

import lombok.Data;

import java.io.Serializable;

/**
 * web 列表列
 */
@Data
public class WebTableColumn implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 标题
     */
    private String label;

    /**
     * 字段名
     */
    private String prop;

    /**
     * 宽度
     */
    private Integer width;

    /**
     * 对齐方式
     */
    private String align;

    /**
     * 字典类型
     */
    private String dict;
    /**
     * 不参与搜索
     */
    private Boolean noSearch;
    /**
     * 是否时间类型
     */
    private Boolean date;
    /**
     * 是否数字类型
     */
    private Boolean number;
}
