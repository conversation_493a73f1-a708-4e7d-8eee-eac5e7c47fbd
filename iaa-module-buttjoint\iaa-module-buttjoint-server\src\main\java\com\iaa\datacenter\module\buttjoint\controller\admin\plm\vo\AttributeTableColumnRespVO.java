package com.iaa.datacenter.module.buttjoint.controller.admin.plm.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - PLM 物料分类属性 VO")
@Data
public class AttributeTableColumnRespVO {

    @Schema(description = "列")
    private String prop;

    @Schema(description = "列名")
    private String label;

    @Schema(description = "列类型")
    private String type;

    @Schema(description = "来源表")
    private String sourceTable;

}
