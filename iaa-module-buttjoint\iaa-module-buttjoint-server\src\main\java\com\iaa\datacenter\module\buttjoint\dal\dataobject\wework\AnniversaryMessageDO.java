package com.iaa.datacenter.module.buttjoint.dal.dataobject.wework;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("anniversary_message")
public class AnniversaryMessageDO {

    @TableId
    private Long msgId;

    private Integer type;

    private Integer year;

    private String content;
}
