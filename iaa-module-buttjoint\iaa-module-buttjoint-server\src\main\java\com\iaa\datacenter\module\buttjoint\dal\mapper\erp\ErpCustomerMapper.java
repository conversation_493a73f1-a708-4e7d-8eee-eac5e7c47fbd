package com.iaa.datacenter.module.buttjoint.dal.mapper.erp;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.iaa.datacenter.module.buttjoint.api.erp.dto.ErpLastOrderDTO;
import com.iaa.datacenter.module.buttjoint.controller.admin.erp.custom.vo.ErpCustomPageReqVO;
import com.iaa.datacenter.module.buttjoint.controller.admin.erp.custom.vo.ErpCustomRespVO;
import com.iaa.datacenter.module.buttjoint.controller.admin.erp.custom.vo.ErpItemRespVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * ERP 客户 Mapper
 */
@DS("erp")
@Mapper
public interface ErpCustomerMapper {

    @Select("""
            select
                top 20
                CBO_Customer.Code as code,
                iif(CBO_Customer.ShortName is null ,CBO_Customer_Trl.Name,CBO_Customer.ShortName) as name
            from CBO_Customer
            join CBO_Customer_Trl on CBO_Customer.ID = CBO_Customer_Trl.ID and CBO_Customer_Trl.SysMLFlag='zh-CN'
            ${ew.getCustomSqlSegment}
            """)
    List<ErpCustomRespVO> getCustomerList(@Param("ew")Wrapper<ErpCustomPageReqVO> wrapper);


    @Select("""
            select
                top 20
                Code  as itemCode,
                Name as itemName,
                SPECS as spec
            from CBO_ItemMaster
            where Org =1002211090000331 and Code like concat(#{itemCode},'%')
            """)
    List<ErpItemRespVO> getItemList(@Param("itemCode") String itemCode);

    @Select("""
            WITH RankedRecords AS (
                SELECT
                    SM_SOLine.ItemInfo_ItemCode AS itemCode,
                    SM_SO.BusinessDate AS businessDate,
                    SM_SOLine.OrderByQtyTU AS orderQty,
                    ROW_NUMBER() OVER (
                        PARTITION BY SM_SOLine.ItemInfo_ItemCode
                        ORDER BY SM_SO.BusinessDate DESC
                    ) AS rn
                FROM SM_SOLine
                LEFT JOIN SM_SO ON SM_SOLine.SO = SM_SO.ID
                ${ew.getCustomSqlSegment}
            )
            select
                CBO_ItemMaster.Code as itemCode,
                CBO_ItemMaster.Name as itemName,
                CBO_ItemMaster.SPECS as spec,
                cast(RankedRecords.businessDate as date) as businessDate,
                RankedRecords.orderQty
            from CBO_ItemMaster
            join RankedRecords on CBO_ItemMaster.Code = RankedRecords.itemCode
            where RankedRecords.rn = 1 and CBO_ItemMaster.Org = 1002211090000331
            """)
    List<ErpLastOrderDTO> getLastOrder(@Param("ew") Wrapper<ErpLastOrderDTO> wrapper);

}
