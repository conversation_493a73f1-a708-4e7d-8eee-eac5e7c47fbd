package com.iaa.datacenter.module.buttjoint.controller.admin.ekuaibao.dept;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.anwen.mongo.conditions.query.QueryWrapper;
import com.anwen.mongo.conditions.update.UpdateWrapper;
import com.iaa.datacenter.framework.common.core.KeyValue;
import com.iaa.datacenter.framework.common.pojo.CommonResult;
import com.iaa.datacenter.framework.common.pojo.PageResult;
import com.iaa.datacenter.framework.common.pojo.WebTableColumn;
import com.iaa.datacenter.framework.common.util.object.BeanUtils;
import com.iaa.datacenter.framework.common.util.object.WebColumnUtils;
import com.iaa.datacenter.module.buttjoint.controller.admin.ekuaibao.dept.vo.EkuaibaoDeptPageReqVO;
import com.iaa.datacenter.module.buttjoint.controller.admin.ekuaibao.dept.vo.EkuaibaoDeptRespVO;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.ekuaibao.EkuaibaoCustomDO;
import com.iaa.datacenter.module.buttjoint.dal.mapper.erp.ErpCustomMapper;
import com.iaa.datacenter.module.buttjoint.service.ekuaibao.custom.EkuaibaoCustomService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.iaa.datacenter.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 合思部门维护")
@RestController
@RequestMapping("/butt-joint/ekuaibao/dept")
@Validated
@Slf4j
public class EkuaibaoDeptController {
    /**
     * todo 注意这里是自定义接口的问题，原因是部门字段不再采用组织架构了，而是自定部门
     */
    @Resource
    private EkuaibaoCustomService ekuaibaoCustomService;
    @Resource
    private ErpCustomMapper erpCustomMapper;
    @GetMapping("/get-columns")
    @Operation(summary = "获取合思部门列信息")
    @PreAuthorize("@ss.hasPermission('ekuaibao:dept:list')")
    public CommonResult<List<WebTableColumn>> getColumns(){
        return success(WebColumnUtils.parseTableColumn(EkuaibaoDeptRespVO.class));
    }

    @GetMapping("/get-erp-all-dept")
    @Operation(summary = "获取U9所有部门信息")
    @PreAuthorize("@ss.hasPermission('ekuaibao:dept:list')")
    public CommonResult<List<KeyValue<String,String>>> getErpAllDept(){
        return success(erpCustomMapper.getDept());
    }

    @GetMapping("/page")
    @Operation(summary = "获取合思部门数据")
    @PreAuthorize("@ss.hasPermission('ekuaibao:dept:list')")
    public CommonResult<PageResult<EkuaibaoDeptRespVO>> page(@Valid EkuaibaoDeptPageReqVO reqVO){
        QueryWrapper<EkuaibaoCustomDO> wrapper = new QueryWrapper<>();
        wrapper.eq(EkuaibaoCustomDO::getDimensionId,"ID01AdKeqenyyz:自定部门");
        wrapper.like(StrUtil.isNotEmpty(reqVO.getName()),EkuaibaoCustomDO::getName,reqVO.getName());
        // 如果存在父部门名称，则查询子部门
        if(StrUtil.isNotEmpty(reqVO.getParentName())){
            List<EkuaibaoCustomDO> list = ekuaibaoCustomService.list(new QueryWrapper<EkuaibaoCustomDO>().like(EkuaibaoCustomDO::getName, reqVO.getParentName()));
            wrapper.in(CollUtil.isNotEmpty(list),EkuaibaoCustomDO::getParentId,list.stream().map(EkuaibaoCustomDO::getId).toList());
        }
        com.anwen.mongo.model.PageResult<EkuaibaoCustomDO> page = ekuaibaoCustomService.page(wrapper, reqVO.getPageNo(), reqVO.getPageSize());
        page.getContentData().forEach(item->{
            if(StrUtil.isNotEmpty(item.getParentId())){
                EkuaibaoCustomDO parent = ekuaibaoCustomService.getById(item.getParentId());
                item.setParentName(parent.getName());
            }
        });
        return success(new PageResult<>(BeanUtils.toBean(page.getContentData(), EkuaibaoDeptRespVO.class),page.getTotalSize()));
    }

    @PostMapping("/save")
    @Operation(summary = "更新合思部门数据")
    @PreAuthorize("@ss.hasPermission('ekuaibao:dept:list')")
    public CommonResult<Boolean> save(@Valid @RequestBody EkuaibaoDeptRespVO saveVO){
        return success(ekuaibaoCustomService.update(new UpdateWrapper<EkuaibaoCustomDO>()
                .eq(EkuaibaoCustomDO::getId,saveVO.getId())
                .set(EkuaibaoCustomDO::getU9cCode,saveVO.getU9cCode()))
        );
    }

}
