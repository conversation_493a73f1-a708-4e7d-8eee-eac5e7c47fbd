package com.iaa.datacenter.module.buttjoint.dal.dataobject.plm;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class PlmBomTopDO {
    /**
     * BOM 名称
     */
    private String bomName;
    /**
     * 物料编码
     */
    private String itemCode;
    /**
     * 归档时间
     */
    private LocalDateTime frozenTime;
    /**
     * 包装完成
     */
    private String packagingComplete;
    /**
     * 程序完成
     */
    private String programComplete;
    /**
     * 面板完成
     */
    private String panelComplete;
    /**
     * 其它完成
     */
    private String otherComplete;
}
