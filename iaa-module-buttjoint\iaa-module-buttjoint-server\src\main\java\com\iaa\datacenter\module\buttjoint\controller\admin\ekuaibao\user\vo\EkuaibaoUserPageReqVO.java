package com.iaa.datacenter.module.buttjoint.controller.admin.ekuaibao.user.vo;

import com.iaa.datacenter.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 用户查询 Request VO")
@Data
public class EkuaibaoUserPageReqVO extends PageParam {

    @Schema(description = "用户名")
    private String name;

    @Schema(description = "部门")
    private String deptName;
}
