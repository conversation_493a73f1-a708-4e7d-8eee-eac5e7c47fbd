package com.iaa.datacenter.module.buttjoint.controller.admin.ekuaibao.dept.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.iaa.datacenter.framework.common.annotation.WebTableColumn;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import java.util.List;

@Data
@ExcelIgnoreUnannotated
@Schema(description = "管理后台 - 合思部门 Request VO")
public class EkuaibaoDeptRespVO {
    @ExcelProperty("部门ID")
    @WebTableColumn(label="部门ID",noSearch = true)
    @NotNull(message="部门ID不能为空")
    private String id;

    @ExcelProperty("父部门名称")
    @WebTableColumn(label = "父部门名称")
    private String parentName;

    @ExcelProperty("部门名称")
    @WebTableColumn(label = "部门名称")
    private String name;

    @ExcelProperty("部门编码")
    @WebTableColumn(label = "部门编码",noSearch = true)
    private List<String> u9cCode;
}
