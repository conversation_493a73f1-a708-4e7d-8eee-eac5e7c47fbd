package com.iaa.datacenter.module.buttjoint.dal.mapper.erp;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.erp.ErpCustomerMonthAndWeekSalesDO;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.erp.ErpCustomerYearSalesDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.Collection;
import java.util.List;

/**
 * ERP 销售 Mapper
 */
@DS("erp")
@Mapper
public interface ErpSoMapper {

    /**
     * 获取客户年度销售额数据
     * @param customers
     * @return
     */
    List<ErpCustomerYearSalesDO> getCustomerYearSales(Collection<String> customers);

    /**
     * 获取客户月度/周度销售额数据
     * @param customers
     * @param dateRange
     * @return
     */
    List<ErpCustomerMonthAndWeekSalesDO> getCustomerMonthAndWeekSales(Collection<String> customers,String[] dateRange);

}
