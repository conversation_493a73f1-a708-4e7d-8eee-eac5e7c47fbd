package com.iaa.datacenter.module.buttjoint.dal.mapper.red;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.red.RedFlowerDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
@DS("red")
public interface RedFlowerMapper {

    /**
     * 获取小红花列表
     * @param dateType
     * @param sTime
     * @return
     */
    List<RedFlowerDO> selectList(String dateType,String sTime);

}
