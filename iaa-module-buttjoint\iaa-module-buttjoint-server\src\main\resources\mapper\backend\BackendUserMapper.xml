<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iaa.datacenter.module.buttjoint.dal.mapper.backend.BackendUserMapper">

    <select id="getXiaomanViewUserPage" resultType="com.iaa.datacenter.module.buttjoint.controller.admin.xiaoman.permissions.vo.XiaomanViewUserRespVO">
        select
            system_users.id,
            username,
            nickname
        from system_users
                 left join system_user_role on user_id=system_users.id
                 left join system_role_menu on system_role_menu.role_id=system_user_role.role_id
                 left join system_menu on menu_id=system_menu.id
            ${ew.getCustomSqlSegment}
        group by system_users.id,username,nickname
    </select>

</mapper>
