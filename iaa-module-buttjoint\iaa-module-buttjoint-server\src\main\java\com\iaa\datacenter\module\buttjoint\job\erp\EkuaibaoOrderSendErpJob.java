package com.iaa.datacenter.module.buttjoint.job.erp;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.anwen.mongo.conditions.query.QueryWrapper;
import com.anwen.mongo.conditions.update.UpdateWrapper;
import com.iaa.datacenter.framework.buttjoint.constant.ButtJointNameConstants;
import com.iaa.datacenter.framework.buttjoint.dataobject.BaseOrderDO;
import com.iaa.datacenter.framework.buttjoint.service.ButtJointClientService;
import com.iaa.datacenter.framework.common.core.KeyValue;
import com.iaa.datacenter.framework.quartz.core.handler.JobHandler;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.ekuaibao.EkuaibaoOrderDO;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.ekuaibao.EkuaibaoSubjectDO;
import com.iaa.datacenter.module.buttjoint.dal.mapper.erp.ErpCustomMapper;
import com.iaa.datacenter.module.buttjoint.enums.EkuaibaoToErpOrderTypeEnum;
import com.iaa.datacenter.module.buttjoint.framework.constant.ErpApiConstant;
import com.iaa.datacenter.module.buttjoint.framework.core.EkuaibaoFeeDetails;
import com.iaa.datacenter.module.buttjoint.framework.core.EkuaibaoFeeDetailsLine;
import com.iaa.datacenter.module.buttjoint.framework.core.EkuaibaoFeeInvoice;
import com.iaa.datacenter.module.buttjoint.framework.factory.ButtJointClientFactory;
import com.iaa.datacenter.module.buttjoint.framework.utils.EkuaibaoFC;
import com.iaa.datacenter.module.buttjoint.framework.utils.EkuaibaoOrderUtil;
import com.iaa.datacenter.module.buttjoint.service.ekuaibao.order.EkuaibaoOrderService;
import com.iaa.datacenter.module.buttjoint.service.ekuaibao.subject.EkuaibaoSubjectService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Component
public class EkuaibaoOrderSendErpJob implements JobHandler {
    @Resource
    private ButtJointClientFactory buttJointClientFactory;
    @Resource
    private EkuaibaoOrderService ekuaibaoOrderService;
    @Resource
    private ErpCustomMapper erpCustomMapper;
    @Resource
    private EkuaibaoSubjectService ekuaibaoSubjectService;

    @Override
    public String execute(String param) {
        ButtJointClientService client = buttJointClientFactory.getClient(ButtJointNameConstants.ERP);
        QueryWrapper<EkuaibaoOrderDO> wrapper = new QueryWrapper<EkuaibaoOrderDO>()
                .ne(EkuaibaoOrderDO::getFormType, "requisition");
        if (StrUtil.isNotEmpty(param)) {
            wrapper.in(EkuaibaoOrderDO::getId, param.split(","));
        } else {
            wrapper.eq(BaseOrderDO::getOrderStatus, 0);
        }
        List<EkuaibaoOrderDO> orders = ekuaibaoOrderService.list(wrapper);
        // 循环转单
        for (EkuaibaoOrderDO order : orders) {
            try {
                List<EkuaibaoFeeDetails> feeDetails = EkuaibaoOrderUtil.parseFeeDetails(order.getFormType(), order.getForm());
                for (EkuaibaoFeeDetails feeDetail : feeDetails) {
                    if (feeDetail.getType().equals(EkuaibaoToErpOrderTypeEnum.CM_PAY.getType())) {
                        ArrayList<Object> bill = buildCMPay(feeDetail);
                        JSONObject result = client.post(ErpApiConstant.CMR_RCV_PAY_BILL, new HashMap<>(), bill, feeDetail.getOrg());
                        parsingResult(result, order, ErpApiConstant.CMR_RCV_PAY_BILL_SUBMIT, ErpApiConstant.CMR_RCV_PAY_BILL_AUDIT, client, feeDetail.getOrg(), EkuaibaoToErpOrderTypeEnum.CM_PAY);
                        Assert.isTrue(order.getOrderStatus() != 2, order.getErrorMsg());
                    } else if (feeDetail.getType().equals(EkuaibaoToErpOrderTypeEnum.PAY.getType())) {
                        ArrayList<Object> bill = buildPay(feeDetail);
                        JSONObject result = client.post(ErpApiConstant.PAY_BILL, new HashMap<>(), bill, feeDetail.getOrg());
                        parsingResult(result, order, ErpApiConstant.PAY_BILL_SUBMIT, ErpApiConstant.PAY_BILL_APPROVE, client, feeDetail.getOrg(), EkuaibaoToErpOrderTypeEnum.PAY);
                        Assert.isTrue(order.getOrderStatus() != 2, order.getErrorMsg());
                    } else if (feeDetail.getType().equals(EkuaibaoToErpOrderTypeEnum.DUE.getType())) {
                        ArrayList<Object> bill = buildApBill(feeDetail);
                        JSONObject result = client.post(ErpApiConstant.AP_BILL_CREATE, new HashMap<>(), bill, feeDetail.getOrg());
                        parsingResult(result, order, ErpApiConstant.AP_BILL_SUBMIT, ErpApiConstant.AP_BILL_APPROVE, client, feeDetail.getOrg(), EkuaibaoToErpOrderTypeEnum.DUE);
                        Assert.isTrue(order.getOrderStatus() != 2, order.getErrorMsg());
                    } else if (feeDetail.getType().equals(EkuaibaoToErpOrderTypeEnum.VOUCHER.getType())) {
                        ArrayList<Object> bill = buildVoucher(feeDetail);
                        JSONObject result = client.post(ErpApiConstant.VOUCHER_CREATE, new HashMap<>(), bill, feeDetail.getOrg());
                        parsingResult(result, order, null, null, client, feeDetail.getOrg(), EkuaibaoToErpOrderTypeEnum.VOUCHER);
                        Assert.isTrue(order.getOrderStatus() != 2, order.getErrorMsg());
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
                order.setOrderStatus(2);
                order.setErrorMsg(Optional.ofNullable(e.getMessage()).orElse(Arrays.toString(e.getStackTrace())));
                if (order.getErrorMsg().length() > 100) {
                    order.setErrorMsg(order.getErrorMsg().substring(0, 100));
                }
                ekuaibaoOrderService.update(
                        new UpdateWrapper<EkuaibaoOrderDO>()
                                .eq(EkuaibaoOrderDO::getId, order.getId())
                                .set(BaseOrderDO::getOrderStatus, 2)
                                .set(BaseOrderDO::getErrorMsg, order.getErrorMsg())
                                .set(EkuaibaoOrderDO::getPayCode, order.getPayCode())
                                .set(EkuaibaoOrderDO::getApCode, order.getApCode())
                                .set(EkuaibaoOrderDO::getVoucherCode, order.getVoucherCode())
                );
            }
        }
        return "本次成功同步单据：" + String.join(",", orders.stream().filter(o -> o.getOrderStatus() == 1).map(p -> p.getForm().getString("code")).collect(Collectors.toSet()));
    }

    /**
     * 提交单据
     *
     * @param result
     * @param order
     * @param subUrl
     * @param appUrl
     * @param client
     * @param org
     * @param typeEnum
     */
    private void parsingResult(JSONObject result, EkuaibaoOrderDO order, String subUrl, String appUrl, ButtJointClientService client, String org, EkuaibaoToErpOrderTypeEnum typeEnum) {
        if (result.containsKey("Data")) {
            JSONObject info = result.getJSONArray("Data").toJavaList(JSONObject.class).get(0);
            if (info.containsKey("IsSucess") && info.getBoolean("IsSucess")) {
                order.setOrderStatus(1);
                order.setErrorMsg("");
                if (typeEnum.getType().equals(EkuaibaoToErpOrderTypeEnum.PAY.getType()) || typeEnum.getType().equals(EkuaibaoToErpOrderTypeEnum.CM_PAY.getType())) {
                    order.setPayCode(info.getString("Code"));
                } else if (typeEnum.getType().equals(EkuaibaoToErpOrderTypeEnum.DUE.getType())) {
                    if (CollUtil.isEmpty(order.getApCode())) order.setApCode(new ArrayList<>());
                    order.getApCode().add(info.getString("Code"));
                } else if (typeEnum.getType().equals(EkuaibaoToErpOrderTypeEnum.VOUCHER.getType())) {
                    order.setVoucherCode(info.getString("Code"));
                }
                if (StrUtil.isNotEmpty(subUrl) && StrUtil.isNotEmpty(appUrl)) {
                    HashMap<String, Object> auditObj = new HashMap<>();

                    if (typeEnum.getType().equals(EkuaibaoToErpOrderTypeEnum.CM_PAY.getType())) {
                        auditObj.put("DocNo", info.getString("Code"));
                    } else {
                        auditObj.put("Code", info.getString("Code"));
                    }
                    client.post(subUrl, new HashMap<>(), new ArrayList<>() {{
                        add(auditObj);
                    }}, org);

//                    client.post(appUrl,new HashMap<>(),new ArrayList<>(){{
//                        add(auditObj);
//                    }},org);
                }
            } else {
                order.setOrderStatus(2);
                order.setErrorMsg(info.getString("ErrorMsg"));
            }
        } else {
            order.setOrderStatus(2);
            order.setErrorMsg("请求失败，没有获取到相关信息");
        }
        ekuaibaoOrderService.update(
                new UpdateWrapper<EkuaibaoOrderDO>()
                        .eq(EkuaibaoOrderDO::getId, order.getId())
                        .set(BaseOrderDO::getOrderStatus, order.getOrderStatus())
                        .set(BaseOrderDO::getErrorMsg, order.getErrorMsg())
                        .set(EkuaibaoOrderDO::getPayCode, order.getPayCode())
                        .set(EkuaibaoOrderDO::getApCode, order.getApCode())
                        .set(EkuaibaoOrderDO::getVoucherCode, order.getVoucherCode())
        );
    }

    /**
     * 构建现金银行付款单
     *
     * @param feeDetails
     * @return
     */
    private ArrayList<Object> buildCMPay(EkuaibaoFeeDetails feeDetails) {
        if (StrUtil.isEmpty(feeDetails.getLines().get(0).getIncome())) {
            throw new RuntimeException("当前单据为找到收支项目");
        }
        KeyValue<String, String> cmPayBillExists = erpCustomMapper.getCmPayBillExists(feeDetails.getCode());
        if (Objects.nonNull(cmPayBillExists)) {
            throw new RuntimeException("当前单据在ERP中已经存在现金银行付款单据：" + cmPayBillExists.getKey() + "，请勿重复同步");
        }
        HashMap<String, Object> bill = new HashMap<>();
        HashMap<String, Object> desc = new HashMap<>();
        desc.put("PrivateDescSeg1", feeDetails.getUser());//借款人
        desc.put("PrivateDescSeg2", true);// 是合思单据
        desc.put("PrivateDescSeg3", feeDetails.getCode());//合思编号
        bill.put("DescFlexField", desc);//描述

        bill.put("BizOrgCode", feeDetails.getOrg());// 组织
        bill.put("SettleOrgCode", feeDetails.getOrg());
        bill.put("RcvPayObjType", 4);// 付款对象
        bill.put("DeptCode", feeDetails.getLines().get(0).getDept()); // 部门
        bill.put("isPay", true); // 是付款单
        bill.put("IncExpItemCode", feeDetails.getLines().get(0).getIncome()); // 收支项目
        bill.put("RcvPayCurCode", feeDetails.getLines().get(0).getCurrency());// 币种
        bill.put("RcvPayAccCurCode", feeDetails.getLines().get(0).getCurrency());
        // 处理多币种情况
        if (!feeDetails.getLines().get(0).getCurrency().equals("C001")) {
            bill.put("ExRateType", 2);
        }
        bill.put("RcvPayFCRate", feeDetails.getLines().get(0).getRate());//汇率
        bill.put("RcvPayMoney", feeDetails.getLines().get(0).getAmount());// 金额
        bill.put("SettleMethodCode", feeDetails.getLines().get(0).getSettlement());// 结算方式
        bill.put("RcvPayAccCode", feeDetails.getLines().get(0).getPayCode());//支付银行账号
        bill.put("RcvPayDate", feeDetails.getPayDate());// 付款日期
        bill.put("Memo", feeDetails.getRemark() + feeDetails.getCode());//摘要
        return new ArrayList<>() {{
            add(bill);
        }};
    }

    /**
     * 构建付款单
     *
     * @param feeDetails
     * @return
     */
    private ArrayList<Object> buildPay(EkuaibaoFeeDetails feeDetails) {
        KeyValue<String, String> payBillExists = erpCustomMapper.getPayBillExists(feeDetails.getCode());
        if (Objects.nonNull(payBillExists)) {
            throw new RuntimeException("当前单据在ERP中已经存在应付付款单据：" + payBillExists.getKey() + "，请勿重复同步");
        }
        HashMap<String, Object> bill = new HashMap<>();
        HashMap<String, Object> desc = new HashMap<>();
        desc.put("PrivateDescSeg2", true);
        desc.put("PrivateDescSeg3", feeDetails.getCode());
        bill.put("DescFlexField", desc);

        bill.put("PCToFCExRate", feeDetails.getLines().get(0).getRate());// 汇率
        bill.put("PCCode", feeDetails.getLines().get(0).getCurrency());// 币种
        bill.put("ERType", 2); // 汇率类型
        bill.put("payDate", feeDetails.getPayDate()); // 付款日期
        bill.put("DocumentTypeCode", feeDetails.getHasRecharge() ? "APP004" : feeDetails.getHasPayment() ? "APP002" : "APP004"); //单据类型
        // 妇科对象
        if (StrUtil.isNotEmpty(feeDetails.getSupplier())) {
            bill.put("PayObjType", 1);
            bill.put("SuppCode", feeDetails.getSupplier());
            bill.put("SuppSiteCode", feeDetails.getSupplier());
        } else {
            bill.put("PayObjType", 0);
            bill.put("CustCode", feeDetails.getCustomer());
            bill.put("CustSiteCode", feeDetails.getCustomer());
        }
        // 组织
        bill.put("SrcBillOrgCode", feeDetails.getOrg());
        bill.put("BizOrgCode", feeDetails.getOrg());
        // 摘要
        bill.put("Note", feeDetails.getRemark());
        // 单行
        ArrayList<Object> lineList = new ArrayList<>();
        bill.put("PayBillLines", lineList);
        int lineNo = 10;
        StringBuilder sb = new StringBuilder();
        for (EkuaibaoFeeDetailsLine line : feeDetails.getLines()) {
            if (StrUtil.isEmpty(line.getIncome())) {
                throw new RuntimeException("当前单据为找到收支项目");
            }
            HashMap<String, Object> lineMap = new HashMap<>();
            lineList.add(lineMap);
            HashMap<String, Object> useLine = new HashMap<>();
            lineMap.put("PayBillUseLines", new ArrayList<>() {{
                add(useLine);
            }});

            lineMap.put("LineNum", lineNo);//行号
            useLine.put("LineNum", 10);
            lineMap.put("IncomeExpendItemCode", line.getIncome());//收支项目
            useLine.put("IncExpItemCode", line.getIncome());
            useLine.put("DeptCode", line.getDept());//部门
            lineMap.put("SettlementMethodCode", line.getSettlement());
            lineMap.put("PayBkAccCode", line.getPayCode());
            lineMap.put("PayBACToFCExRate", line.getRate());
            useLine.put("Money", line.getAmount());
            lineMap.put("PCToPayBACExRate", 1);
            useLine.put("PayProperty", feeDetails.getHasRecharge() ? 9 : feeDetails.getHasPayment() ? 3 : 9);
            // 书香组织和退货款特殊操作，用途只能填标准
            if (feeDetails.getOrg().equals("105")||line.getIncome().equals("0045")) {
                useLine.put("PayProperty", 0);
            }
            lineNo += 10;
            if (StrUtil.isNotEmpty(line.getRemark())) {
                sb.append(line.getRemark()).append(";");
            }
        }
        if (!feeDetails.getRemark().endsWith(";"))
            bill.put("Note", feeDetails.getRemark() + sb + ";" + feeDetails.getCode());
        return new ArrayList<>() {{
            add(bill);
        }};
    }

    /**
     * 构建应付发票
     *
     * @param feeDetails
     * @return
     */
    private ArrayList<Object> buildApBill(EkuaibaoFeeDetails feeDetails) {
        KeyValue<String, String> payBillExists = erpCustomMapper.getApBillExists(feeDetails.getCode());
        if (Objects.nonNull(payBillExists)) {
            throw new RuntimeException("当前单据在ERP中已经存在应付应付单据：" + payBillExists.getKey() + "，请勿重复同步");
        }
        HashMap<String, Object> bill = new HashMap<>();
        HashMap<String, Object> desc = new HashMap<>();
        desc.put("PrivateDescSeg2", true);
        desc.put("PrivateDescSeg3", feeDetails.getCode());
        bill.put("DescFlexField", desc);

        bill.put("AccrueDate", feeDetails.getPayDate());//日期
        //供应商
        String supplier = StrUtil.isNotEmpty(feeDetails.getSupplier()) ? feeDetails.getSupplier() : "36-118";
        bill.put("AccrueSuppCode", supplier);
        bill.put("AccrueSuppSiteCode", supplier);
        bill.put("PaySuppCode", supplier);
        bill.put("PaySuppSiteCode", supplier);
        bill.put("BusinessType", "189");
        //组织
        bill.put("SrcOrgCode", feeDetails.getOrg());
        bill.put("BizOrgCode", feeDetails.getOrg());
        bill.put("ACCode", feeDetails.getLines().get(0).getCurrency());//币种
        bill.put("DocumentTypeCode", feeDetails.getOrg().equals("102") ? "7107" : feeDetails.getOrg().equals("104") ? "7111" : "7104");//单据类型
        bill.put("Memo", feeDetails.getRemark());
        //单行
        ArrayList<Object> lines = new ArrayList<>();
        bill.put("ImportAPBillLineDTOs", lines);
        int lienNum = 10;
        StringBuilder sb = new StringBuilder();
        for (EkuaibaoFeeDetailsLine tempLine : feeDetails.getLines()) {
            if (StrUtil.isEmpty(tempLine.getIncome())) {
                throw new RuntimeException("当前单据为找到收支项目");
            }
            HashMap<String, Object> line = new HashMap<>();
            HashMap<String, Object> lineDesc = new HashMap<>();
            line.put("DescFlexField", lineDesc);
            lineDesc.put("PrivateDescSeg1", tempLine.getDept());
            lines.add(line);
            line.put("LineNum", lienNum);
            line.put("Money", tempLine.getAmount());
            line.put("Maturity", feeDetails.getPayDate());
            line.put("IncomeExpendItemCode", tempLine.getIncome());
            line.put("deptCode", tempLine.getDept());
            line.put("IsIncludeTax", true);
            // 设定发票
            ArrayList<Object> invoices = new ArrayList<>();
            int invoiceLineNum = 0;
            for (EkuaibaoFeeInvoice ekuaibaoFeeInvoice : Optional.ofNullable(tempLine.getInvoiceList()).orElse(new ArrayList<>())) {
                if (!ekuaibaoFeeInvoice.isHasSpecial()) continue;
                HashMap<String, Object> invoiceLine = new HashMap<>();
                invoiceLine.put("TaxCode", ekuaibaoFeeInvoice.getTaxCode());
                invoiceLine.put("TaxRate", ekuaibaoFeeInvoice.getRate());
                invoiceLine.put("TaxMoney", ekuaibaoFeeInvoice.getTaxAmount().setScale(2, RoundingMode.HALF_UP));
                invoiceLine.put("LineNum", invoiceLineNum += 10);
                invoices.add(invoiceLine);
            }
            if (invoices.size() > 0) line.put("ImportAPBillTaxDTOs", invoices);
            lienNum += 10;
            if (StrUtil.isNotEmpty(tempLine.getRemark())) {
                sb.append(tempLine.getRemark()).append(";");
            }
        }
        if (!feeDetails.getRemark().endsWith(";"))
            bill.put("Memo", feeDetails.getRemark() + sb + ";" + feeDetails.getCode());
        return new ArrayList<>() {{
            add(bill);
        }};
    }

    /**
     * 构建总账凭证
     *
     * @param feeDetails
     * @return
     */
    private ArrayList<Object> buildVoucher(EkuaibaoFeeDetails feeDetails) {
        KeyValue<String, String> payBillExists = erpCustomMapper.getVoucherExists(feeDetails.getCode());
        if (Objects.nonNull(payBillExists)) {
            throw new RuntimeException("当前单据在ERP中已经存在总账凭证单据：" + payBillExists.getKey() + "，请勿重复同步");
        }
        HashMap<String, Object> bill = new HashMap<>();
        HashMap<String, Object> desc = new HashMap<>();
        desc.put("PrivateDescSeg2", true);
        desc.put("PrivateDescSeg3", feeDetails.getCode());
        bill.put("DescFlexField", desc);

        bill.put("SOB", feeDetails.getOrg());
        bill.put("VoucherCategory", "01");
        bill.put("PostedPeriod", feeDetails.getPayDate().substring(0, 7));
        bill.put("CreateDate", DateUtil.today());
        // 制造部门
        Set<String> manufactureDeptIds = Stream.of("ID01Ere3r8n0Yv").collect(Collectors.toSet());
        // 销售部门
        Set<String> salesDeptIds = Stream.of(
                "ID01El6wmobWiP",
                "ID01ErdYi1LcMD",
                "ID01ErdRBOWbbF",
                "ID01ErekSs1xEP",
                "ID01H0JEQe8Q0L"
        ).collect(Collectors.toSet());
        // 研发部门
        Set<String> developmentDeptIds = Stream.of("ID01ErdFRbJ2hh").collect(Collectors.toSet());
        //无需拼接部门的费用
        Set<String> notDeptFee = Stream.of("ID01ANQIsENEM7", "ID01E3VgFaQ5OL").collect(Collectors.toSet());
        // 行信息
        ArrayList<Object> entries = new ArrayList<>();
        bill.put("Entries", entries);
        for (EkuaibaoFeeDetailsLine tempLine : feeDetails.getLines()) {
            if (StrUtil.isEmpty(tempLine.getIncome())) {
                throw new RuntimeException("当前单据为找到收支项目");
            }
            HashMap<String, Object> line = new HashMap<>();
            entries.add(line);
            EkuaibaoSubjectDO subject = ekuaibaoSubjectService.getById(tempLine.getIncomeId());
            // 凭证借方科目
            ArrayList<String> deptIds = new ArrayList<>();
            EkuaibaoFC.getCustomIds(deptIds, tempLine.getDeptId());
            String account = "";
            if (deptIds.stream().anyMatch(manufactureDeptIds::contains)) {
                account = subject.getManufactureCode();
            } else if (deptIds.stream().anyMatch(salesDeptIds::contains)) {
                account = subject.getSalesCode();
            } else if (deptIds.stream().anyMatch(developmentDeptIds::contains)) {
                account = subject.getDevelopmentCode();
            } else {
                account = subject.getManagementCode();
            }
            if (StrUtil.isEmpty(account))
                throw new RuntimeException("未找到费用" + tempLine.getIncome() + "的科目，请查询维护");
            if (notDeptFee.stream().anyMatch(subject.getId()::equals)) {
                line.put("Account", account + "|0|0|0|0|0");
            } else {
                line.put("Account", account + "|0|0|0|0|" + tempLine.getDept());
            }

            line.put("AccountedDr", tempLine.getAmountCny());
            line.put("EnteredDr", tempLine.getAmount());
            line.put("Currency", tempLine.getCurrency());

            StringBuilder sb = new StringBuilder();
            sb.append(feeDetails.getRemark());
            if(StrUtil.isNotEmpty(tempLine.getRemark())){
                sb.append(tempLine.getRemark()).append(";");
            }
            sb.append("&").append(feeDetails.getCode());
            if (StrUtil.isNotEmpty(feeDetails.getCustomer()))
                sb.append("&").append(EkuaibaoFC.getCustomName(feeDetails.getCustomerId()));
            line.put("Abstracts", sb.toString());
        }
        putEntries(EkuaibaoFeeDetailsLine::getHasMall, "122109", feeDetails, entries);
        putEntries(EkuaibaoFeeDetailsLine::getHasDebit, "122103", feeDetails, entries);
        putEntries(EkuaibaoFeeDetailsLine::getHasDeposit, "********", feeDetails, entries);
        putEntries(EkuaibaoFeeDetailsLine::getHasRecharge, "224301", feeDetails, entries);
        return new ArrayList<>() {{
            add(bill);
        }};
    }

    private void putEntries(Predicate<? super EkuaibaoFeeDetailsLine> predicate, String account, EkuaibaoFeeDetails feeDetails, ArrayList<Object> entries) {
        List<EkuaibaoFeeDetailsLine> list = feeDetails.getLines().stream().filter(predicate).toList();
        if (CollUtil.isEmpty(list)) return;
        HashMap<String, Object> line = new HashMap<>();
        entries.add(line);
        // 填写科目
        String abstracts = "";
        switch (account) {
            case "122109" -> {
                line.put("Account", account + "|0|0|0|0|0");
                abstracts = "&合思商城支付";
            }
            case "122103" -> {
                line.put("Account", account + "|0|0|0|" + feeDetails.getUser() + "|0");
                abstracts = "&员工个人借支";
            }
            case "********" -> {
                line.put("Account", account + "|0|" + feeDetails.getSupplier() + "|0|0|0");
                abstracts = "&核销押金";
            }
            case "224301" -> {
                line.put("Account", account + "|0|0|0|0|0");
                abstracts = "&" + EkuaibaoFC.getCustomName(feeDetails.getCustomerId());
            }
        }
        line.put("AccountedCr", list.stream().map(EkuaibaoFeeDetailsLine::getAmountCny).reduce(BigDecimal.ZERO, BigDecimal::add));
        line.put("EnteredCr", list.stream().map(EkuaibaoFeeDetailsLine::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        line.put("Currency", list.get(0).getCurrency());

        line.put("Abstracts", feeDetails.getRemark() + "&" + feeDetails.getCode() + abstracts);
    }
}
