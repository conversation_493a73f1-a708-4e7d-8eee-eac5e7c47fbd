package com.iaa.datacenter.module.buttjoint.framework.client;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.http.*;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.iaa.datacenter.framework.buttjoint.annotation.ClientIdentifier;
import com.iaa.datacenter.framework.buttjoint.config.ButtJointConfig;
import com.iaa.datacenter.framework.buttjoint.config.ButtJointProperties;
import com.iaa.datacenter.framework.buttjoint.constant.ButtJointNameConstants;
import com.iaa.datacenter.framework.buttjoint.service.ButtJointClientService;
import com.iaa.datacenter.framework.common.util.json.JsonUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.*;

@Service
@Slf4j
@ClientIdentifier(name= ButtJointNameConstants.XIEKEYUN)
public class XiekeyunClient implements ButtJointClientService {

    @Resource
    private ButtJointProperties buttJointProperties;
    @Override
    public JSONObject auth() {
        ButtJointConfig buttJointConfig = buttJointProperties.getType().get(ButtJointNameConstants.XIEKEYUN);
        JSONObject result = new JSONObject();
        result.put("appKey",buttJointConfig.getAppKey());
        result.put("version","2.0");
        result.put("operateCompanyCode",buttJointConfig.getEntCode());
        result.put("ownerCompanyCode",buttJointConfig.getEntCode());
        result.put("timestamps",System.currentTimeMillis()/1000);
//        result.put("Reserver","");
        result.put("sign",buildCurrentSign(result,buttJointConfig.getAppSecret()));
        return result;
    }

    /**
     * 构造本次请求的签名
     * @param jsonObject
     * @param appSecret
     * @return
     */
    private String buildCurrentSign(JSONObject jsonObject,String appSecret){
        Map<String,Object> javaObject = jsonObject.toJavaObject(new TypeReference<TreeMap<String, Object>>() { });
        javaObject.remove("sign");
        StringBuilder sb =new StringBuilder();
        for (Object value : javaObject.values()) {
            sb.append(value.toString().trim()).append(":");
        }
        sb.append(appSecret);
        try{
            return getMD5(sb.toString()).toLowerCase();
        }catch (Exception e){
            log.error("签名生成失败",e);
            return null;
        }
    }
    /*
     * 得到MD5值
     * @param str
     * @return
     * @throws Exception
     */
    private static String getMD5(String str) throws IOException, NoSuchAlgorithmException {
        if (str == null || str.isEmpty()) {
            return null;
        }

        StringBuilder ret = new StringBuilder();
        MessageDigest digest = MessageDigest.getInstance("MD5");
        digest.update(str.getBytes(StandardCharsets.UTF_8));
        byte[] hash = digest.digest();
        // 转为ASCII码,byte为8位,计算机为32位,采用补码形式,所以要补全.
        for (byte b : hash) {
            if ((0xFF & b) < 0x10) {
                ret.append("0").append(Integer.toHexString((b & 0xFF)));
            } else {
                ret.append(Integer.toHexString(b & 0xFF));
            }
        }
        return ret.toString().toUpperCase();
    }


    @Override
    public JSONObject auth(String code) {
        return null;
    }

    @Override
    public JSONObject readObj(String url, Method type, Map<String, Object> query, Map<String, Object> body, String... code) {
        JSONObject auth = auth();
        System.out.println(auth);
        HashMap<String, Object> requestMap = new HashMap<>();
        requestMap.put("commonParam",auth);
        requestMap.put("body",body);
        ButtJointConfig buttJointConfig = buttJointProperties.getType().get(ButtJointNameConstants.XIEKEYUN);
        HttpRequest request = HttpUtil.createRequest(type, HttpUtil.urlWithFormUrlEncoded(buttJointConfig.getUrl()+url,query, StandardCharsets.UTF_8));
        if(CollUtil.isNotEmpty(requestMap)){
            request.contentType(ContentType.JSON.getValue()).body(JSON.toJSONString(requestMap));
        }
        try(HttpResponse response = request.execute()){
            return JsonUtils.parseObject(response.body(),JSONObject.class);
        }
    }

    @Override
    public JSONObject readObj(String url, Method type, Map<String, Object> query, ArrayList<Object> body, String... code) {
        return null;
    }
}
