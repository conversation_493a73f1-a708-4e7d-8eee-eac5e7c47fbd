package com.iaa.datacenter.module.buttjoint.job.xiekeyun;

import com.alibaba.fastjson.JSONObject;
import com.iaa.datacenter.framework.buttjoint.constant.ButtJointNameConstants;
import com.iaa.datacenter.framework.buttjoint.service.ButtJointClientService;
import com.iaa.datacenter.framework.quartz.core.handler.JobHandler;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.xiekeyun.XiekeyunEmployeeDO;
import com.iaa.datacenter.module.buttjoint.framework.constant.XiekeyunApiConstant;
import com.iaa.datacenter.module.buttjoint.framework.factory.ButtJointClientFactory;
import com.iaa.datacenter.module.buttjoint.service.xiekeyun.employee.XiekeyunEmployeeService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;

/**
 * 读取携客云员工信息
 */
@Component
@Slf4j
public class ReadEmployeeJob implements JobHandler {

    @Resource
    private ButtJointClientFactory buttJointClientFactory;
    @Resource
    private XiekeyunEmployeeService xiekeyunEmployeeService;
    @Override
    public String execute(String param) throws Exception {
        ButtJointClientService xiekeyun = buttJointClientFactory.getClient(ButtJointNameConstants.XIEKEYUN);
        JSONObject result = xiekeyun.post(XiekeyunApiConstant.EMPLOYEE, new HashMap<>(), new HashMap<>() {{
            put("erpCode", "陈自强");
            put("inviteStatus", 2);
        }});
        if (result.getInteger("result")!=1) {
            log.error("读取携客云用户信息失败，{}",result.getString("errorMsg"));
            return String.format("读取携客云用户信息失败，%s",result.getString("errorMsg"));
        }
        List<XiekeyunEmployeeDO> dataList = result.getJSONArray("dataList").toJavaList(XiekeyunEmployeeDO.class);
        xiekeyunEmployeeService.saveOrUpdateBatch(dataList,true);
        return "读取携客云用户信息成功，"+dataList.size()+"条";
    }
}
