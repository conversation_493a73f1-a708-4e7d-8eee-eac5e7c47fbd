package com.iaa.datacenter.module.buttjoint.dal.dataobject.xiaoman;

import com.alibaba.fastjson.JSONObject;
import com.anwen.mongo.annotation.ID;
import com.anwen.mongo.annotation.collection.CollectionName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@CollectionName("xiaoman_customer_group")
public class XiaomanCustomerGroupDO {

    @ID
    private Long id;
    /**
     * 分组名称
     */
    private String name;
    /**
     * 分组信息
     */
    private JSONObject name_info;
    /**
     * 时区
     */
    private String prefix;
    /**
     * 父节点
     */
    private Long parent_id;
    /**
     * 层级
     */
    private Integer layer;
    /**
     * 创建人
     */
    private Integer create_user;
    /**
     * 序号
     */
    private Long order_rank;
    /**
     * 创建时间
     */
    private String create_time;
    /**
     * 更新时间
     */
    private String update_time;

    private Integer relate_id;
    /**
     * 状态
     */
    private Boolean system_flag;
    /**
     * 是否系统分组
     */
    private Boolean is_sys;
    /**
     * 序号
     */
    private Integer rank;
}
