package com.iaa.datacenter.module.buttjoint.controller.admin.xiaoman.report;

import com.iaa.datacenter.framework.apilog.core.annotation.ApiAccessLog;
import com.iaa.datacenter.framework.common.core.KeyValue;
import com.iaa.datacenter.framework.common.pojo.CommonResult;
import com.iaa.datacenter.framework.common.pojo.PageResult;
import com.iaa.datacenter.framework.excel.core.util.ExcelUtils;
import com.iaa.datacenter.module.buttjoint.controller.admin.xiaoman.report.vo.*;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.xiaoman.XiaomanUserDO;
import com.iaa.datacenter.module.buttjoint.service.xiaoman.customer.XiaomanCustomerService;
import com.iaa.datacenter.module.buttjoint.service.xiaoman.customer.XiaomanOpportunityService;
import com.iaa.datacenter.module.buttjoint.service.xiaoman.customer.XiaomanOrderService;
import com.iaa.datacenter.module.buttjoint.service.xiaoman.user.XiaomanUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.net.http.HttpResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.iaa.datacenter.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.iaa.datacenter.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 小满报表")
@RestController
@RequestMapping("/butt-joint/xiaoman/report")
@Validated
@Slf4j
public class XiaomanReportController {

    @Resource
    private XiaomanCustomerService xiaomanCustomerService;
    @Resource
    private XiaomanOpportunityService xiaomanOpportunityService;
    @Resource
    private XiaomanOrderService xiaomanOrderService;

    @PostMapping("/page-opportunity")
    @Operation(summary = "获取机会点明细报表")
    @PreAuthorize("@ss.hasPermission('xiaoman:report:page-opportunity')")
    public CommonResult<PageResult<XiaomanOpportunityReportRespVO>> getXiaomanOpportunityReportPage(@RequestBody XiaomanOpportunityReportPageReqVO pageReqVO) {
        PageResult<XiaomanOpportunityReportRespVO> opportunityReportPage = xiaomanCustomerService.getOpportunityReportPage(pageReqVO);
        pageReqVO.setPageSize(XiaomanOpportunityReportPageReqVO.PAGE_SIZE_NONE);
        PageResult<XiaomanOpportunityReportRespVO> total = xiaomanCustomerService.getOpportunityReportPage(pageReqVO);
        List<XiaomanOpportunityReportRespVO> contentList = new ArrayList<>(opportunityReportPage.getList());
        contentList.add(xiaomanCustomerService.calculateOpportunityReport(total.getList()));
        opportunityReportPage.setList(contentList);
        return success(opportunityReportPage);
    }

    @PostMapping("/export-opportunity")
    @Operation(summary = "导出机会点明细报表")
    @PreAuthorize("@ss.hasPermission('xiaoman:report:export-opportunity')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportXiaomanOpportunityReport(HttpServletResponse response,@RequestBody XiaomanOpportunityReportPageReqVO pageReqVO) throws IOException {
        pageReqVO.setPageSize(XiaomanOpportunityReportPageReqVO.PAGE_SIZE_NONE);
        PageResult<XiaomanOpportunityReportRespVO> total = xiaomanCustomerService.getOpportunityReportPage(pageReqVO);
        ExcelUtils.write(response,"小满客户开发作战机会点明细表","数据",XiaomanOpportunityReportRespVO.class,total.getList());
    }

    @PostMapping("/page-customer-development")
    @Operation(summary = "获取客户开发作战报表")
    @PreAuthorize("@ss.hasPermission('xiaoman:report:page-customer-development')")
    public CommonResult<PageResult<XiaomanCustomerDevelopmentVO>> getXiaomanCustomerDevelopmentPage(@RequestBody XiaomanCustomerDevelopmentPageReqVO pageReqVO){
        return success(xiaomanOpportunityService.getCustomerDevelopmentPage(pageReqVO));
    }

    @PostMapping("/export-customer-development")
    @Operation(summary = "导出客户开发作战报表")
    @PreAuthorize("@ss.hasPermission('xiaoman:report:export-customer-development')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportXiaomanCustomerDevelopmentPage(HttpServletResponse response,@RequestBody XiaomanCustomerDevelopmentPageReqVO pageReqVO) throws IOException {
        pageReqVO.setPageSize(XiaomanCustomerDevelopmentPageReqVO.PAGE_SIZE_NONE);
        PageResult<XiaomanCustomerDevelopmentVO> total = xiaomanOpportunityService.getCustomerDevelopmentPage(pageReqVO);
        ExcelUtils.write(response,"小满销售作战客户开发明细表","数据",XiaomanCustomerDevelopmentVO.class,total.getList());
     }

    @PostMapping("/page-order")
    @Operation(summary = "获取滚动订单")
    @PreAuthorize("@ss.hasPermission('xiaoman:report:order')")
    public CommonResult<PageResult<XiaomanOrderRespVO>> getXiaomanOrderPage(@RequestBody XiaomanOrderPageReqVO pageReqVO) {
        PageResult<XiaomanOrderRespVO> xiaomanOrderPage = xiaomanOrderService.getXiaomanOrderPage(pageReqVO);
        pageReqVO.setPageSize(XiaomanOrderPageReqVO.PAGE_SIZE_NONE);
        PageResult<XiaomanOrderRespVO> total = xiaomanOrderService.getXiaomanOrderPage(pageReqVO);
        List<XiaomanOrderRespVO> contentList = new ArrayList<>(xiaomanOrderPage.getList());
        contentList.add(xiaomanOrderService.calculateOrderTotal(total.getList()));
        xiaomanOrderPage.setList(contentList);
        return success(xiaomanOrderPage);
    }

    @PostMapping("/export-order")
    @Operation(summary = "导出滚动订单报表")
    @PreAuthorize("@ss.hasPermission('xiaoman:report:export-order')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportXiaomanOrderPage(HttpServletResponse response,@RequestBody XiaomanOrderPageReqVO pageReqVO) throws Exception {
        pageReqVO.setPageSize(XiaomanOrderPageReqVO.PAGE_SIZE_NONE);
        PageResult<XiaomanOrderRespVO> total = xiaomanOrderService.getXiaomanOrderPage(pageReqVO);
        xiaomanOrderService.exportXiaomanOrderPage(response,pageReqVO,total.getList());
    }
}
