package com.iaa.datacenter.module.buttjoint.framework.constant;

/**
 * 合思接口常量
 */
public interface EkuaibaoApiConstant {
    /**
     * 员工列表
     */
    String STAFFS="v1.1/staffs";
    /**
     * 自定档案分类
     */
    String DIMENSIONS="v1/dimensions/items/withAll";

    /**
     * 费用类型接口
     */
    String FEE_TYPE="v1/feeTypes";
    /**
     * 付款账号
     */
    String PAY_ACCOUNT="v1/paymentAccounts";
    /**
     * 获取单据列表
     */
    String GET_APPLY_LIST= "v1.1/docs/getApplyList";
    /**
     * 批量获取发票详情
     */
    String INVOICE_DETAIL_BATCH="v2/extension/INVOICE/object/invoice/detailBatch";
    /**
     * 获取发票主体
     */
    String INVOICE_OBJECT="v2/extension/INVOICE/object/";
    /**
     * 根据单据获取发票类型
     */
    String INVOICE_SEARCH="v2/extension/flow/INVOICE/search";
    /**
     * 合思还款单接口
     */
    String REPAYMENT_RECORD="v1/loan/repaymentRecord/";
    /**
     * 获取临时授权码
     */
    String PROVISIONAL_AUTH="v1.1/provisional/getProvisionalAuth";

    //根据单据id获取单据
    String FLOW_DETAILS="v1.1/flowDetails";
    /**
     * 新增自定义档案
     */
    String DIMENSIONS_ITEM="v1.1/dimensions/items";
}
