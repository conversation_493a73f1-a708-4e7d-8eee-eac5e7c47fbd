package com.iaa.datacenter.module.buttjoint.job.ekuaibao;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.anwen.mongo.conditions.query.QueryWrapper;
import com.iaa.datacenter.framework.buttjoint.constant.ButtJointNameConstants;
import com.iaa.datacenter.framework.buttjoint.dataobject.BaseOrderDO;
import com.iaa.datacenter.framework.buttjoint.service.ButtJointClientService;
import com.iaa.datacenter.framework.quartz.core.handler.JobHandler;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.ekuaibao.EkuaibaoInvoiceDO;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.ekuaibao.EkuaibaoOrderDO;
import com.iaa.datacenter.module.buttjoint.framework.constant.EkuaibaoApiConstant;
import com.iaa.datacenter.module.buttjoint.framework.factory.ButtJointClientFactory;
import com.iaa.datacenter.module.buttjoint.service.ekuaibao.invoice.EkuaibaoInvoiceService;
import com.iaa.datacenter.module.buttjoint.service.ekuaibao.order.EkuaibaoOrderService;
import com.jayway.jsonpath.DocumentContext;
import com.jayway.jsonpath.JsonPath;
import jakarta.annotation.Resource;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 同步合思单据任务
 */
@Component("readOrderJob")
public class ReadOrderJob implements JobHandler {
    @Resource
    private ButtJointClientFactory buttJointClientFactory;
    @Resource
    private EkuaibaoOrderService ekuaibaoOrderService;
    @Resource
    private EkuaibaoInvoiceService ekuaibaoInvoiceService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;
    /**
     * 合思单据类型
     */
    private static final HashSet<String> orderType = new HashSet<String>() {{
        add("loan");
        add("expense");
        add("requisition");
    }};
    /**
     * 发票类型
     */
    private static final HashMap<String, String> INVOICE_TYPE_MAP = new HashMap<String, String>() {{
        put("FULL_DIGITAl_NORMAL", "普票");//电子发票（普通发票）
        put("FULL_DIGITAl_SPECIAL", "专票");//电子发票（增值税专用发票）
        put("FULL_DIGITAl_PAPER", "专票");//全电纸质发票（增值税专用发票）
        put("FULL_DIGITAl_PAPER_NORMAL", "普票");//全电纸质发票（增值税普通发票）
        put("DIGITAL_NORMAL", "普票");//增值税电子普通发票
        put("DIGITAL_SPECIAL", "专票");//增值税电子专用发票
        put("PAPER_NORMAL", "普票");//增值税普通发票
        put("PAPER_ROLL", "普票");//增值税普通发票（卷式）
        put("PAPER_SPECIAL", "专票");//增值税普通发票（卷式）
        put("PAPER_CAR", "普票");//机动车销售统一发票
        put("SECOND_CAR", "普票");//二手车销售统一发票
        put("PAPER_FEE", "普票");//通行费发票
        put("BLOCK_CHAIN", "普票");//区块链电子发票
        put("ELECTRONIC_AIRCRAFT_INVOICE", "普票");//电子发票（航空运输电子客票行程单）
        put("ELECTRONIC_TRAIN_INVOICE", "普票");//电子发票（铁路电子客票）
    }};

    @Override
    public String execute(String param) throws Exception {
        ButtJointClientService ekuaibao = buttJointClientFactory.getClient(ButtJointNameConstants.EKUAIBAO);
        Map<String, Object> query = new HashMap<>();
        //根据入参读取指定单据
        if (StrUtil.isNotEmpty(param)) {
            query = JSONObject.parseObject(param);
            int count = readOrder(ekuaibao, query);
            return "单据同步成功：" + count + "张";
        } else {
            String startTime = stringRedisTemplate.opsForValue().get("EKUAIBAO_ORDER_QUERY_END_TIME");
            if(StrUtil.isEmpty(startTime)){
                startTime= DateUtil.date().offset(DateField.DAY_OF_YEAR,-1).toString("yyyy-MM-dd HH:mm:ss");
            }
            String endTime = DateUtil.now();
            // 自动读取单据
            query.put("start", 0);
            query.put("count", 100);
            query.put("orderBy", "updateTime");
            query.put("startDate", startTime);
            query.put("endDate", endTime);
            query.put("state", "paid,archived");
            int count = 0;
            for (String type : orderType) {
                query.put("type", type);
                count += readOrder(ekuaibao, query);
            }
            stringRedisTemplate.opsForValue().set("EKUAIBAO_ORDER_QUERY_END_TIME",endTime);
            return "单据同步成功：" + count + "张";
        }
    }

    /**
     * 根据单据ID获取单张单据
     * @param orderId
     * @return
     */
    public Boolean readOneOrder(String orderId){
        ButtJointClientService ekuaibao = buttJointClientFactory.getClient(ButtJointNameConstants.EKUAIBAO);
        JSONObject result = ekuaibao.get(EkuaibaoApiConstant.FLOW_DETAILS, new HashMap<>() {{
            put("flowId", orderId);
//            put("code",orderId);
        }});
        EkuaibaoOrderDO order = result.getJSONObject("value").toJavaObject(EkuaibaoOrderDO.class);
        order.setOrderStatus(2);
        order.setErrorMsg("手动从合思更新单据");
        ekuaibaoOrderService.updateById(order);
        if (order.getForm().containsKey("details")) {
            getInvoice(ekuaibao,List.of(order));
        }
        return true;
    }

    public Boolean readOrderByCode(String code){
        ButtJointClientService ekuaibao = buttJointClientFactory.getClient(ButtJointNameConstants.EKUAIBAO);
        JSONObject result = ekuaibao.get(EkuaibaoApiConstant.FLOW_DETAILS+"/byCode", new HashMap<>() {{
            put("code",code);
        }});
        EkuaibaoOrderDO order = result.getJSONObject("value").toJavaObject(EkuaibaoOrderDO.class);
        order.setOrderStatus(0);
        order.setErrorMsg("手动下载单据");
        ekuaibaoOrderService.saveOrUpdate(order,true);
        if (order.getForm().containsKey("details")) {
            getInvoice(ekuaibao,List.of(order));
        }
        return true;
    }

    private int readOrder(ButtJointClientService client, Map<String, Object> query) {
        int start = 0,count,saveCount=0;
        List<EkuaibaoOrderDO> orders = new ArrayList<>();
        do {
            query.put("start", start);
            JSONObject result = client.get(EkuaibaoApiConstant.GET_APPLY_LIST, query);
            count = result.getInteger("count");
            orders.addAll(result.getJSONArray("items").toJavaList(EkuaibaoOrderDO.class));
            start += 100;
        } while (start <= count);

        // 提前获取所有需要保存的订单ID
        List<String> orderIdsToSave = orders.stream()
                .map(EkuaibaoOrderDO::getId)
                .collect(Collectors.toList());

        // 批量查询已存在的订单ID
        List<String> existingOrderIds = ekuaibaoOrderService.getByIds(orderIdsToSave)
                .stream()
                .map(EkuaibaoOrderDO::getId)
                .toList();

        // 保存新订单
        List<EkuaibaoOrderDO> newOrders = orders.stream()
                .filter(order -> !existingOrderIds.contains(order.getId()))
                .collect(Collectors.toList());
        if (!newOrders.isEmpty()) {
            ekuaibaoOrderService.saveBatch(newOrders);
            saveCount+=newOrders.size();
        }

        // 更新已存在的订单
        List<EkuaibaoOrderDO> updatedOrders = orders.stream()
                .filter(order -> existingOrderIds.contains(order.getId()))
                .filter(order -> !ekuaibaoOrderService.exist(new QueryWrapper<EkuaibaoOrderDO>() //排除已经转单的订单
                        .eq(EkuaibaoOrderDO::getId, order.getId())
                        .eq(BaseOrderDO::getOrderStatus, 1)))
                .collect(Collectors.toList());
        if (!updatedOrders.isEmpty()) {
            ekuaibaoOrderService.updateBatchByIds(updatedOrders);
            saveCount+=updatedOrders.size();
        }

        // 如果是报销单
        if (query.get("type").equals("expense")&&orders.size()>0) {
            getInvoice(client, orders);
        }
        return saveCount;
    }

    /**
     * 根据单据ID获取发票主体信息
     * @param client
     * @param orders
     */
    private void getInvoice(ButtJointClientService client, List<EkuaibaoOrderDO> orders) {
        // 读取单据类型
        HashMap<String, Object> invoiceBody = new HashMap<>();
        invoiceBody.put("type","id");
        invoiceBody.put("codeOrIds", orders.stream().map(EkuaibaoOrderDO::getId).toList());
        JSONObject invoiceType = client.post(EkuaibaoApiConstant.INVOICE_SEARCH, new HashMap<>(), invoiceBody);
        HashMap<String,JSONArray> invoiceTypeMap=invoiceType.getJSONObject("value").toJavaObject(new TypeReference<HashMap<String,JSONArray>>(){});
        Map<String,List<String>> invoicesAll=new HashMap<>();
        invoiceTypeMap.values().forEach(item->{
            List<Map<String,Object>> invoice = JsonPath.read(item, "$.[*]");
            invoice.forEach(invoiceItem->{
                if(invoicesAll.containsKey(invoiceItem.get("invoiceType").toString())){
                    invoicesAll.get(invoiceItem.get("invoiceType").toString()).add(invoiceItem.get("invoiceId").toString());
                }else {
                    invoicesAll.put(invoiceItem.get("invoiceType").toString(), new ArrayList<String>(){{
                        add(invoiceItem.get("invoiceId").toString());
                    }});
                }
            });
        });

        invoicesAll.forEach((type,ids)->{
            JSONObject invoiceObject = client.post(EkuaibaoApiConstant.INVOICE_OBJECT + type + "/search", new HashMap<>(), new HashMap<String, Object>() {{
                put("ids", ids);
            }});
            List<EkuaibaoInvoiceDO> items = invoiceObject.getJSONArray("items").toJavaList(EkuaibaoInvoiceDO.class);
            items.forEach(item->{
                item.setHasExclusive(INVOICE_TYPE_MAP.getOrDefault(item.getE_system_发票主体_发票类别(),"普票").equals("专票"));
            });
            ekuaibaoInvoiceService.saveOrUpdateBatch(items,true);
        });
    }
}
