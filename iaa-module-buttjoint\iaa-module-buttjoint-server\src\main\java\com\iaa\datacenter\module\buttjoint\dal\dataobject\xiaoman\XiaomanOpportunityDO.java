package com.iaa.datacenter.module.buttjoint.dal.dataobject.xiaoman;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.anwen.mongo.annotation.ID;
import com.anwen.mongo.annotation.collection.CollectionName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 小满商机数据对象
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@CollectionName("xiaoman_opportunity")
public class XiaomanOpportunityDO {

    /**
     * 商机ID
     */
    @ID
    private String opportunity_id;
    /**
     * 线索ID
     */
    private String serial_id;
    /**
     * 创建人
     */
    private JSONArray user_id;
    /**
     * 商机标签
     */
    private JSONArray cust_tag;
    /**
     * 产品是否停售
     */
    private Long product_disable_flag;
    /**
     * 协同跟进人
     */
    private JSONArray handler;
    /**
     * 负责人
     */
    private String main_user;
    /**
     * 创建人
     */
    private String create_user;
    /**
     * 归属部门
     */
    private String department;
    /**
     * 输单描述
     */
    private String fail_remark;
    /**
     * 输单原因
     */
    private String fail_type;
    /**
     * 产品总金额
     */
    private BigDecimal product_total_amount;
    /**
     * 其他总费用
     */
    private BigDecimal product_other_amount;
    /**
     * 产品总数量
     */
    private BigDecimal product_total_count;
    /**
     * 备注
     */
    private String product_remark;
    /**
     * 产品金额
     */
    private BigDecimal cost_amount;
    /**
     * 其他费用
     */
    private BigDecimal other_cost;
    /**
     * 产品数量
     */
    private BigDecimal count;
    /**
     * 产品价格
     */
    private BigDecimal unit_price;
    /**
     * 产品规格
     */
    private String sku_id;
    /**
     * 产品型号
     */
    private String product_model;
    /**
     * 产品名称
     */
    private String product_name;
    /**
     * 产品ID
     */
    private String product_id;
    /**
     * 产品编号
     */
    private String product_no;
    /**
     * 商机铲平唯一ID
     */
    private String opportunity_product_id;
    /**
     * 备注
     */
    private String remark;
    /**
     * 商机类型
     */
    private Integer type;
    /**
     * 商机类型名称
     */
    private String type_name;
    /**
     * 来源
     */
    private Long origin;
    /**
     * 商机来源
     */
    private JSONArray origin_list;
    /**
     * 商机来源名称
     */
    private String origin_name;
    /**
     * 销售阶段
     */
    private String stage;
    /**
     * 阶段信息
     */
    private JSONObject stage_info;
    /**
     * 阶段类型
     */
    private Integer stage_type;
    /**
     * 阶段类型名称
     */
    private String stage_type_name;
    /**
     * 结束日期
     */
    private String account_date;
    /**
     * 销售流程
     */
    private String flow_id;
    /**
     * 来源线索
     */
    private String main_lead_id;
    /**
     * 销售金额
     */
    private BigDecimal amount;
    /**
     * 本币金额
     */
    private Double amount_rmb;
    /**
     * 核币金额
     */
    private BigDecimal amount_usd;
    /**
     * 关联联系人
     */
    private String customer_id;
    /**
     * 汇率
     */
    private BigDecimal exchange_rate;
    /**
     * 币种
     */
    private String currency;
    /**
     * 商机名称
     */
    private String name;
    /**
     * 客户
     */
    private String company_id;
    /**
     * 作战产品
     */
    @JSONField(name="**************")
    private String combat_product;
    /**
     * 预测销量
     */
    @JSONField(name = "**************")
    private Double sales_count;
    /**
     * 开发类型
     */
    @JSONField(name = "**************")
    private String development_type;
    /**
     * 首单金额
     */
    @JSONField(name = "34537687979820")
    private BigDecimal firstOrder_amount;
    /**
     * 开发目标
     */
    @JSONField(name = "34537477819554")
    private String development_target;
    /**
     * erp系统下单名称
     */
    @JSONField(name = "26045635915467")
    private String erpName;
    /**
     * 动态时间
     */
    private String trail_time;
    /**
     * 下单时间
     */
    private String order_time;
    /**
     * 创建时间
     */
    private String create_time;
    /**
     * 更新时间
     */
    private String update_time;
    /**
     * 启用状态
     */
    private Integer enable_flag;
    /**
     * 跟进状态
     */
    private Integer trail_active_flag;
    /**
     * 自定义字段数据
     */
    private Object external_field_data;
    /**
     * ?
     */
    private Integer create_type;
    /**
     * 审核状态
     */
    private Integer approval_status;
    /**
     * 下次跟进时间
     */
    private String next_follow_up_time;
    /**
     * 钉钉pin用户
     */
    private JSONArray pin_user_list;
    /**
     * 删除状态
     */
    private Integer disable_flag;
    /**
     * 标签
     */
    private JSONArray tag;
    /**
     * 自动摘要
     */
    private Integer auto_summary_flag;
    /**
     * 缺少必填字段
     */
    private Boolean missingRequiredFieldFlag;
    /**
     * 缺少必填字段
     */
    private JSONArray missField;
    /**
     * 金额是否可编辑
     */
    private Boolean amount_can_edit_flag;
    /**
     * 金额同步方式
     */
    private String sync_amount_refer_type;
    /**
     * 审批流程
     */
    private JSONArray approval_flow_info;
    /**
     * 部门信息
     */
    private JSONObject department_info;
    /**
     * 阶段停留时长
     */
    private Long stage_stay_time_old;
    /**
     * 阶段停留时长
     */
    private Long stage_stay_time;
    /**
     * 创建人信息
     */
    private JSONObject create_user_info;
    /**
     * 收款信息
     */
    private JSONObject cash_collection_info;
    /**
     * 主负责人信息
     */
    private JSONObject main_user_info;
    /**
     * 协同跟进人信息
     */
    private JSONArray handler_info;
    /**
     * 客户信息
     */
    private JSONObject company;
    /**
     * 跟进信息
     */
    private JSONObject last_trail;
    /**
     * 联系人信息
     */
    private JSONArray customer;
    /**
     * 线索名称
     */
    private String lead_name;
    /**
     * 失败原因名称
     */
    private String fail_type_name;
    /**
     * 失败原因
     */
    private Object fail_stage_info;
    /**
     * 销售流程名称
     */
    private String sale_flow_name;

    private Integer is_pin;

    private Integer lock_flag;
}
