package com.iaa.datacenter.module.buttjoint.dal.dataobject.ekuaibao;

import com.alibaba.fastjson.JSONObject;
import com.anwen.mongo.annotation.ID;
import com.anwen.mongo.annotation.collection.CollectionName;
import com.iaa.datacenter.framework.buttjoint.dataobject.BaseOrderDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@CollectionName("ekuaibao_audit_msg")
public class EkuaibaoApproveMessageDO extends BaseOrderDO {

    @ID
    private String messageId;

    private String action;

    private String actionName;

    private JSONObject userInfo;

    private String flowId;

    private String nodeId;

    private String corporationId;

    private String code;

    private String title;

    private String urgentApproval;

    private String backlogId;

    private JSONObject submitterId;
}
