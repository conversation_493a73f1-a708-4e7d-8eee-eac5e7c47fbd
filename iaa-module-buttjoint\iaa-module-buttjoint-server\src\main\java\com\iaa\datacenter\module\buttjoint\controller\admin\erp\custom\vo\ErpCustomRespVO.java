package com.iaa.datacenter.module.buttjoint.controller.admin.erp.custom.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.iaa.datacenter.framework.common.annotation.WebTableColumn;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ExcelIgnoreUnannotated
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "管理后台 - ERP 项目 Request VO")
public class ErpCustomRespVO {

    @ExcelProperty("编码")
    @WebTableColumn(label = "编码")
    private String code;

    @ExcelProperty("名称")
    @WebTableColumn(label = "名称")
    private String name;
}
