package com.iaa.datacenter.framework.buttjoint.config;

import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * 对接系统配置参数
 */
@Data
public class ButtJointConfig {
    @NotEmpty(message = "客户端基础请求路径不能为空")
    private String url;
    /**
     * 客户端appKey
     */
    private String appKey;
    /**
     * 客户端appSecret
     */
    private String appSecret;
    /**
     * 企业编码
     */
    private String entCode;
    /**
     * 用户编码
     */
    private String userCode;
    /**
     * 用户密码
     */
    private String userPwd;
}
