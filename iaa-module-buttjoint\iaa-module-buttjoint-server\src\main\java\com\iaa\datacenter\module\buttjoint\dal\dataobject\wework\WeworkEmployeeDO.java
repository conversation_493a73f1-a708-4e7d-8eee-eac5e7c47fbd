package com.iaa.datacenter.module.buttjoint.dal.dataobject.wework;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("wework_employee")
public class WeworkEmployeeDO {

    @TableId
    private String weworkId;

    private String name;

    private String phone;

    private LocalDate dateOfEmployment;

    @TableField(exist = false)
    private Integer year;
}
