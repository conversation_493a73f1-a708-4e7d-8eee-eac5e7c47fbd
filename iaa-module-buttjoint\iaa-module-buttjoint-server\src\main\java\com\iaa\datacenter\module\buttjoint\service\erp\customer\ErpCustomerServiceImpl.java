package com.iaa.datacenter.module.buttjoint.service.erp.customer;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.iaa.datacenter.module.buttjoint.api.erp.ErpApi;
import com.iaa.datacenter.module.buttjoint.api.erp.dto.ErpLastOrderDTO;
import com.iaa.datacenter.module.buttjoint.controller.admin.erp.custom.vo.ErpCustomPageReqVO;
import com.iaa.datacenter.module.buttjoint.controller.admin.erp.custom.vo.ErpCustomRespVO;
import com.iaa.datacenter.module.buttjoint.controller.admin.erp.custom.vo.ErpItemRespVO;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.erp.ErpOrderDeliveryDO;
import com.iaa.datacenter.module.buttjoint.dal.mapper.erp.ErpCustomerMapper;
import com.iaa.datacenter.module.buttjoint.dal.mapper.erp.ErpOrderDeliveryMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

@Service
public class ErpCustomerServiceImpl implements ErpCustomerService, ErpApi {

    @Resource
    private ErpCustomerMapper customerMapper;

    @Resource
    private ErpOrderDeliveryMapper erpOrderDeliveryMapper;

    @Override
    public List<ErpCustomRespVO> getCustomerList(ErpCustomPageReqVO pageReqVO) {
        QueryWrapper<ErpCustomPageReqVO> wrapper = new QueryWrapper<>();
        wrapper.like(StrUtil.isNotEmpty(pageReqVO.getCode()),"CBO_Customer.Code", pageReqVO.getCode());
        wrapper.like(StrUtil.isNotEmpty(pageReqVO.getName()),"iif(CBO_Customer.ShortName is null ,CBO_Customer_Trl.Name,CBO_Customer.ShortName)", pageReqVO.getName());
        wrapper.groupBy("CBO_Customer.Code", "iif(CBO_Customer.ShortName is null ,CBO_Customer_Trl.Name,CBO_Customer.ShortName)");
        return customerMapper.getCustomerList(wrapper);
    }

    @Override
    public List<ErpItemRespVO> getItemList(String itemCode) {
        return customerMapper.getItemList(itemCode);
    }

    @Override
    public List<ErpLastOrderDTO> getLastOrder(Collection<String> itemCodes) {
        if(CollUtil.isEmpty(itemCodes)){
            return new ArrayList<>();
        }
        QueryWrapper<ErpLastOrderDTO> wrapper = new QueryWrapper<>();
        wrapper.in("SM_SOLine.ItemInfo_ItemCode",itemCodes);
        return customerMapper.getLastOrder(wrapper);
    }

    @Override
    public BigDecimal getOrderDeliveryRate(String dateType, String sTime) {
        List<ErpOrderDeliveryDO> erpOrderDeliveryDOS = erpOrderDeliveryMapper.selectPage(dateType, sTime);
        if(CollUtil.isEmpty(erpOrderDeliveryDOS)){
            return BigDecimal.ZERO;
        }
        return erpOrderDeliveryDOS.get(0).getDeliveryRate();
    }
}
