package com.iaa.datacenter.module.buttjoint.controller.admin.ekuaibao.dept.vo;

import com.iaa.datacenter.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 部门查询 Request VO")
@Data
public class EkuaibaoDeptPageReqVO extends PageParam {

    @Schema(description = "id")
    private String id;

    @Schema(description = "父部门名称")
    private String parentName;

    @Schema(description = "部门名称")
    private String name;
}
