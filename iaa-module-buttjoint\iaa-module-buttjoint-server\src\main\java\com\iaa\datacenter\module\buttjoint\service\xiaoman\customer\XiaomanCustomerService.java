package com.iaa.datacenter.module.buttjoint.service.xiaoman.customer;

import com.anwen.mongo.service.IService;
import com.iaa.datacenter.framework.common.pojo.PageResult;
import com.iaa.datacenter.module.buttjoint.controller.admin.xiaoman.report.vo.XiaomanCustomerDevelopmentVO;
import com.iaa.datacenter.module.buttjoint.controller.admin.xiaoman.report.vo.XiaomanOpportunityReportPageReqVO;
import com.iaa.datacenter.module.buttjoint.controller.admin.xiaoman.report.vo.XiaomanOpportunityReportRespVO;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.xiaoman.XiaomanCustomerDO;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.xiaoman.XiaomanUserDO;

import java.util.List;
import java.util.Map;

/**
 * 小满客户信息表
 */
public interface XiaomanCustomerService extends IService<XiaomanCustomerDO> {

    /**
     * 获取一段时间的客户信息列表，输出所谓的机会点明细报表
     * @param reqVO
     * @return
     */
    PageResult<XiaomanOpportunityReportRespVO> getOpportunityReportPage(XiaomanOpportunityReportPageReqVO reqVO);

    /**
     * 计算客户信息列表
     * @param contentList
     * @return
     */
    XiaomanOpportunityReportRespVO calculateOpportunityReport(List<XiaomanOpportunityReportRespVO> contentList);

    /**
     * 获取用户Map
     * @param customerList
     * @return
     */
    Map<String, XiaomanUserDO> getUserNameMap(List<XiaomanCustomerDO> customerList);
}
