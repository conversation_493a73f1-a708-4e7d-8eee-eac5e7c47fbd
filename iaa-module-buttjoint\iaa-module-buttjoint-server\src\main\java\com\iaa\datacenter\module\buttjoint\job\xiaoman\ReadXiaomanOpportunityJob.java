package com.iaa.datacenter.module.buttjoint.job.xiaoman;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.iaa.datacenter.framework.buttjoint.constant.ButtJointNameConstants;
import com.iaa.datacenter.framework.buttjoint.service.ButtJointClientService;
import com.iaa.datacenter.framework.quartz.core.handler.JobHandler;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.xiaoman.XiaomanOpportunityDO;
import com.iaa.datacenter.module.buttjoint.framework.constant.XiaomanApiConstant;
import com.iaa.datacenter.module.buttjoint.framework.factory.ButtJointClientFactory;
import com.iaa.datacenter.module.buttjoint.service.xiaoman.customer.XiaomanOpportunityService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Slf4j
@Component
public class ReadXiaomanOpportunityJob implements JobHandler {
    @Resource
    private ButtJointClientFactory buttJointClientFactory;
    @Resource
    private XiaomanOpportunityService xiaomanOpportunityService;
    @Override
    public String execute(String param) throws Exception {
        ButtJointClientService client = buttJointClientFactory.getClient(ButtJointNameConstants.XIAOMAN);
        int start_index = 1,count = 0;
        HashMap<String, Object> query = new HashMap<>();
        query.put("count",100);
        query.put("start_time",DateUtil.offset(DateUtil.date(), DateField.HOUR_OF_DAY,-6).toString("yyyy-MM-dd HH:mm:ss"));
        query.put("end_time", DateUtil.now());
        List<XiaomanOpportunityDO> allOpportunity=new ArrayList<>();
        do {
            query.put("start_index",start_index);
            JSONObject result = client.get(XiaomanApiConstant.OPPORTUNITY_LIST, query);
            if(!result.containsKey("code")){
                log.error("商机数据读取失败："+ JSON.toJSONString(result));
                break;
            }
            if (result.getInteger("code") != 200) break;
            if(!result.getJSONObject("data").containsKey("totalItem")) break;
            if (count == 0) {
                count = result.getJSONObject("data").getInteger("totalItem");
            }
            List<XiaomanOpportunityDO> javaList = result.getJSONObject("data").getJSONArray("list").toJavaList(XiaomanOpportunityDO.class);
            allOpportunity.addAll(javaList);
            start_index++;
        } while (start_index <= (count / 100 + ((count % 100)>0?1:0)));
        if(CollUtil.isNotEmpty(allOpportunity)){
            xiaomanOpportunityService.saveOrUpdateBatch(allOpportunity,true);
        }
        return "商机数据读取成功："+allOpportunity.size()+"条";
    }
}
