package com.iaa.datacenter.module.buttjoint.dal.mapper.erp;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.iaa.datacenter.framework.common.core.KeyValue;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;

@DS("erp")
@Mapper
public interface ErpCustomMapper {

    /**
     * 查询汇率
     * @param formCurrency 币种
     * @param date 日期
     * @return
     */
    BigDecimal getExchangeRate(@Param("formCurrency") String formCurrency,@Param("date") String date);

    /**
     * 查询ERP现金银行付款单是否已存在
     * @param code
     * @return
     */
    KeyValue<String,String> getCmPayBillExists(@Param("code") String code);

    /**
     * 查询ERP付款单是否已存在
     * @param code
     * @return
     */
    KeyValue<String,String> getPayBillExists(@Param("code") String code);

    /**
     * 查询ERP 应付应付单是否已存在
     * @param code
     * @return
     */
    KeyValue<String,String> getApBillExists(@Param("code") String code);

    /**
     * 查询ERP 凭证是否已存在
     * @param code
     * @return
     */
    KeyValue<String,String> getVoucherExists(@Param("code") String code);

    /**
     * 查询ERP 部门
     * @return
     */
    List<KeyValue<String,String>> getDept();

    /**
     * 查询ERP 业务员
     * @return
     */
    List<KeyValue<String,String>> getSeller();

    /**
     * 查询ERP 员工记录
     * @return
     */
    List<KeyValue<String,String>> getEmployee();

    /**
     * 查询ERP 科目
     * @return
     */
    List<KeyValue<String,String>> getSubject();

    /**
     * 查询ERP 客户
     * @return
     */
    List<KeyValue<String,String>> getCustomer(@Param("customers") Collection<String> customers);

    /**
     * 查询ERP 聚水潭Token
     * @return
     */
    List<KeyValue<String,String>> getJstToken();

    /**
     * 更新聚水潭Token
     * @param token
     */
    void updateJstToken(@Param("token") String token);
}
