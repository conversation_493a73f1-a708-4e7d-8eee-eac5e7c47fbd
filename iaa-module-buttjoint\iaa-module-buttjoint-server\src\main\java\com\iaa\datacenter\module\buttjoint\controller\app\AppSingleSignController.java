package com.iaa.datacenter.module.buttjoint.controller.app;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.anwen.mongo.conditions.query.QueryWrapper;
import com.iaa.datacenter.framework.buttjoint.constant.ButtJointNameConstants;
import com.iaa.datacenter.framework.buttjoint.service.ButtJointClientService;
import com.iaa.datacenter.module.buttjoint.controller.app.vo.EkuaibaoSignSaveReqVO;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.ekuaibao.EkuaibaoApproveMessageDO;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.ekuaibao.EkuaibaoStaffsDO;
import com.iaa.datacenter.module.buttjoint.framework.constant.EkuaibaoApiConstant;
import com.iaa.datacenter.module.buttjoint.framework.factory.ButtJointClientFactory;
import com.iaa.datacenter.module.buttjoint.service.ekuaibao.staffs.EkuaibaoStaffsService;
import com.iaa.datacenter.module.system.api.user.AdminUserApi;
import com.iaa.datacenter.module.system.api.user.dto.AdminUserRespDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@Tag(name = "对接单点登录 App")
@RestController
@RequestMapping("/butt-joint/sign")
@Validated
public class AppSingleSignController {
    @Resource
    private AdminUserApi adminUserApi;
    @Resource
    private EkuaibaoStaffsService ekuaibaoStaffsService;
    @Resource
    private ButtJointClientFactory buttJointClientFactory;
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @PostMapping("/ekuaibao/getEkuaibaoUrl")
    @Operation(summary = "合思单点登录")
    @Parameter(name = "approve", description = "合思单点登录", required = true)
    @PermitAll
    public String ekuaibao(@RequestBody EkuaibaoSignSaveReqVO signSaveReqVO){
        HashMap<String,Object> params = new HashMap<>();
        //流程ID
        if (StrUtil.isNotEmpty(signSaveReqVO.getFlowId())) {
            params.put("flowId",signSaveReqVO.getFlowId());
        }
        params.put("pageType",signSaveReqVO.getPageType());
        params.put("expireDate",3600);//一小时后过期
        params.put("isApplet",signSaveReqVO.getIsApplet());
        boolean isOaNull = Objects.isNull(signSaveReqVO.getIsOA());
        if(isOaNull?signSaveReqVO.getIsApplet():!signSaveReqVO.getIsOA()){
            EkuaibaoStaffsDO staffs = ekuaibaoStaffsService.one(new QueryWrapper<EkuaibaoStaffsDO>().eq(EkuaibaoStaffsDO::getCellphone, signSaveReqVO.getLoginName()));
            Assert.notNull(staffs,"用户不存在");
            params.put("uid",staffs.getId());
        }else{
            AdminUserRespDTO userByUsername = adminUserApi.getUserByUsername(signSaveReqVO.getLoginName()).getData();
            Assert.notNull(userByUsername,"用户不存在");
            EkuaibaoStaffsDO staffs = ekuaibaoStaffsService.one(new QueryWrapper<EkuaibaoStaffsDO>().eq(EkuaibaoStaffsDO::getCellphone, userByUsername.getMobile()));
            Assert.notNull(staffs,"用户不存在");
            params.put("uid",staffs.getId());
        }
        String key=signSaveReqVO.getPageType()+ Optional.ofNullable(signSaveReqVO.getFlowId()).orElse("")+signSaveReqVO.getIsApplet()+params.get("uid");
        String url = stringRedisTemplate.opsForValue().get(key);
        if(StrUtil.isNotEmpty(url)) return  url;
        // 开始获取登录链接
        ButtJointClientService client = buttJointClientFactory.getClient(ButtJointNameConstants.EKUAIBAO);
        JSONObject result = client.post(EkuaibaoApiConstant.PROVISIONAL_AUTH, new HashMap<>(), params);
        JSONObject resultObj = result.getJSONObject("value");
        if (resultObj.getString("code").equals("true")) {
            String message = resultObj.getString("message");
            stringRedisTemplate.opsForValue().set(key,message,3600L, TimeUnit.SECONDS);
            return message;
        }
        throw new RuntimeException("请求失败，未获取到登录地址");
    }
}
