package com.iaa.datacenter.module.buttjoint.dal.mapper.backend;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iaa.datacenter.module.buttjoint.controller.admin.xiaoman.permissions.vo.XiaomanViewUserRespVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface BackendUserMapper {

    /**
     * 查询已经可见小满报表的用户列表
     * @param page
     * @param wrapper
     * @return
     */
    Page<XiaomanViewUserRespVO> getXiaomanViewUserPage(@Param("page") Page<XiaomanViewUserRespVO> page, @Param("ew") Wrapper<XiaomanViewUserRespVO> wrapper);
}
