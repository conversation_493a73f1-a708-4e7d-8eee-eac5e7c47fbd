package com.iaa.datacenter.module.buttjoint.dal.dataobject.ekuaibao;

import com.alibaba.fastjson.JSONObject;
import com.anwen.mongo.annotation.ID;
import com.anwen.mongo.annotation.collection.CollectionName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@CollectionName("ekuaibao_invoice")
public class EkuaibaoInvoiceDO {

    /**
     * 发票明细ID
     */
    @ID
    private String id;

    /**
     * 发票ID
     */
    private String masterId;

    private Integer topDate;

    private Integer useCount;

    private Integer totalCount;


    private JSONObject visibility;

    private JSONObject E_税额;

    private Boolean E_是否抵扣;

    private JSONObject E_不计税金额;

    private String E_system_收票状态;

    private String E_system_发票主体_code;

    private JSONObject E_system_发票主体_税额;

    private Boolean E_system_发票主体_验真;

    private JSONObject E_system_发票主体_价税合计;

    private String E_system_发票主体_发票代码;

    private String E_system_发票主体_发票号码;

    private String E_system_发票主体_发票类别;

    private JSONObject E_system_发票主体_发票金额;

    private String E_system_发票明细_code;

    private String E_system_发票明细_name;

    private JSONObject E_system_发票明细_单价;


    private String E_system_发票明细_数量;

    private String E_system_发票明细_税率;

    private JSONObject E_system_发票明细_税额;

    private JSONObject E_system_发票明细_金额;

    private Boolean hasExclusive;
}
