package com.iaa.datacenter.module.buttjoint.controller.admin.red;

import com.iaa.datacenter.framework.common.pojo.CommonResult;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.red.RedFlowerDO;
import com.iaa.datacenter.module.buttjoint.dal.mapper.red.RedFlowerMapper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Tag(name = "管理后台 - 小红花 操作")
@RestController
@RequestMapping("/butt-joint/red/flower")
@Validated
@Slf4j
public class RedFlowerController {

    @Resource
    private RedFlowerMapper redFlowerMapper;

    @GetMapping("/list")
    @Operation(summary = "获取小红花数据")
    public CommonResult<List<RedFlowerDO>> getFlowerList(@RequestParam String dateType,@RequestParam String sTime) {
        return CommonResult.success(redFlowerMapper.selectList(dateType,sTime));
    }

}
