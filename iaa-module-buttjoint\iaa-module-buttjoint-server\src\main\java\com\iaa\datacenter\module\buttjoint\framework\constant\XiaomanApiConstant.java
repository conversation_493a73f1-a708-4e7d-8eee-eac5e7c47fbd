package com.iaa.datacenter.module.buttjoint.framework.constant;

/**
 * 小满接口地址
 */
public interface XiaomanApiConstant {
    /**
     * 首次获取TOKEN
     */
    String GET_TOKEN = "v1/oauth2/access_token";
    /**
     * 刷新TOKEN
     */
    String REFRESH_TOKEN = "v1/oauth2/refresh_token";
    /**
     * 客户列表
     */
    String CUSTOMER = "v1/company/list";
    /**
     * 用户列表
     */
    String USER_LIST = "v1/user/list";
    /**
     * 获取客户状态，客户分组，公海分组，客户来源
     * field=trail_status 客户状态
     * field=group_id 客户分组
     * field=pool_id 公海分组
     * field=origin 客户来源
     */
    String FIELDS_SELECTOR = "v1/company/fields/selector";
    /**
     * 客户信息
     */
    String CUSTOMER_INFO = "v1/company/info";
    /**
     * 客户更新列表
     */
    String CUSTOMER_UPDATE = "v1/company/updates";
    /**
     * 商机列表查询
     */
    String OPPORTUNITY_LIST = "v1/opportunity/list";
}
