<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iaa.datacenter.module.buttjoint.dal.mapper.erp.ErpCustomMapper">

    <select id="getExchangeRate" resultType="java.math.BigDecimal">
        select
            Rate
        from Base_ExchangeRate
        left join Base_Currency FromCurrency on FromCurrency=FromCurrency.ID
        left join Base_Currency ToCurrency on ToCurrency=ToCurrency.ID
        where ToCurrency.Code='C001' and FromCurrency.Code=#{formCurrency} and DATEDIFF(day,Date, #{date})=0
    </select>

    <select id="getCmPayBillExists" resultType="com.iaa.datacenter.framework.common.core.KeyValue">
        select <PERSON>No as 'key', '' as 'value' from CM_CMPayBill where DescFlexField_PrivateDescSeg3=#{code}
    </select>

    <select id="getPayBillExists" resultType="com.iaa.datacenter.framework.common.core.KeyValue">
        select DocNo as 'key', '' as 'value' from AP_PayBillHead where DescFlexField_PrivateDescSeg3=#{code}
    </select>

    <select id="getApBillExists" resultType="com.iaa.datacenter.framework.common.core.KeyValue">
        select DocNo as 'key', '' as 'value' from AP_APBillHead where DescFlexField_PrivateDescSeg3=#{code}
    </select>

    <select id="getVoucherExists" resultType="com.iaa.datacenter.framework.common.core.KeyValue">
        select DocNo as 'key', '' as 'value' from GL_Voucher where DescFlexField_PrivateDescSeg3=#{code}
    </select>

    <select id="getDept" resultType="com.iaa.datacenter.framework.common.core.KeyValue">
        select
            concat(Base_Organization.Code,'-',CBO_Department.Code) as 'key',
            concat(Base_Organization_Trl.Name,'-',CBO_Department_Trl.Name) as 'value'
        from CBO_Department
        left join CBO_Department_Trl on CBO_Department.ID=CBO_Department_Trl.ID
        left join Base_Organization_Trl on Org=Base_Organization_Trl.ID
        left join Base_Organization on Org=Base_Organization.ID
        where CBO_Department.Effective_IsEffective=1
    </select>

    <select id="getSeller" resultType="com.iaa.datacenter.framework.common.core.KeyValue">
        select
            concat(Base_Organization.Code,'-',CBO_Operators.Code) as 'key',
            concat(Base_Organization_Trl.Name,'-',CBO_Operators_Trl.Name) as 'value'
        from CBO_Operators
        left join CBO_Operators_Trl on CBO_Operators.ID=CBO_Operators_Trl.ID
        left join Base_Organization on Org=Base_Organization.ID
        left join Base_Organization_Trl on Base_Organization.ID=Base_Organization_Trl.ID
    </select>

    <select id="getEmployee" resultType="com.iaa.datacenter.framework.common.core.KeyValue">
        select EmployeeCode as 'key',Name as 'value' from CBO_EmployeeArchive
        left join CBO_EmployeeArchive_Trl on CBO_EmployeeArchive.ID=CBO_EmployeeArchive_Trl.ID
    </select>

    <select id="getSubject" resultType="com.iaa.datacenter.framework.common.core.KeyValue">
        select Code as 'key',Name as 'value' from CBO_NaturalAccount
        left join CBO_NaturalAccount_Trl on CBO_NaturalAccount.ID=CBO_NaturalAccount_Trl.ID
        where Code like '5%' or Code like '6%'
    </select>

    <select id="getCustomer" resultType="com.iaa.datacenter.framework.common.core.KeyValue">
        select Code as 'key',Name as 'value' from CBO_Customer
        left join CBO_Customer_Trl on CBO_Customer.ID=CBO_Customer_Trl.ID
        where Code in
        <foreach collection="customers" open="(" separator="," item="customer" close=")">
            #{customer}
        </foreach>
    </select>

    <select id="getJstToken" resultType="com.iaa.datacenter.framework.common.core.KeyValue">
        select app_key as 'key',access_token as 'value' from AAA_JuShuiTan_Token
    </select>

    <update id="updateJstToken">
        update AAA_JuShuiTan_Token set access_token=#{token}
    </update>
</mapper>
