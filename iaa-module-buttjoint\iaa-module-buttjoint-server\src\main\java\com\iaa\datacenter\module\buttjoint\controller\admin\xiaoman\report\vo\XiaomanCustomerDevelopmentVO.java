package com.iaa.datacenter.module.buttjoint.controller.admin.xiaoman.report.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson.JSONObject;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@ExcelIgnoreUnannotated
@Schema(description = "管理后台 - 小满客户开发作战表")
public class XiaomanCustomerDevelopmentVO extends XiaomanCustomerBaseInfoVO{

    @ExcelProperty("开发类型")
    @Schema(description = "开发类型")
    private String developmentType;

    @ExcelProperty("作战产品")
    @Schema(description = "作战产品")
    private String combatProduct;

    @ExcelProperty("开发目标")
    @Schema(description = "开发目标")
    private String developmentTarget;

    @ExcelProperty("预计开发天数")
    @Schema(description = "预计开发天数")
    private Long planDevelopmentDays;

    @ExcelProperty("开始时间")
    @Schema(description = "开始时间")
    private String createTime;

    @ExcelProperty("已开发天数")
    @Schema(description = "已开发天数")
    private Long developmentDays;

    @ExcelProperty("进度")
    @Schema(description = "进度")
    private Integer progress;

    @ExcelProperty("是否超期")
    @Schema(description = "是否超期")
    private Boolean hasOverdue;

    @ExcelProperty("行动进展")
    @Schema(description = "行动进展")
    private String stageInfo;

    @ExcelProperty("结果")
    @Schema(description = "结果")
    private String result;

    @ExcelProperty("输单原因")
    @Schema(description = "输单原因")
    private String failTypeName;

    @ExcelProperty("输单描述")
    @Schema(description = "输单描述")
    private String failRemark;

    @ExcelProperty("实际完成日期")
    @Schema(description = "实际完成日期")
    private String accountDate;

    @ExcelProperty("实际用时（天）")
    @Schema(description = "实际用时（天）")
    private Long accountDays;
}
