package com.iaa.datacenter.module.buttjoint.job.xiaoman;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.iaa.datacenter.framework.buttjoint.constant.ButtJointNameConstants;
import com.iaa.datacenter.framework.buttjoint.service.ButtJointClientService;
import com.iaa.datacenter.framework.quartz.core.handler.JobHandler;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.xiaoman.XiaomanCustomerGroupDO;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.xiaoman.XiaomanUserDO;
import com.iaa.datacenter.module.buttjoint.framework.constant.XiaomanApiConstant;
import com.iaa.datacenter.module.buttjoint.framework.factory.ButtJointClientFactory;
import com.iaa.datacenter.module.buttjoint.service.xiaoman.customer.XiaomanCustomerGroupService;
import com.iaa.datacenter.module.buttjoint.service.xiaoman.user.XiaomanUserService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Slf4j
@Component
public class ReadXiaomanUserJob implements JobHandler {

    @Resource
    private ButtJointClientFactory buttJointClientFactory;
    @Resource
    private XiaomanUserService xiaomanUserService;
    @Resource
    private XiaomanCustomerGroupService xiaomanCustomerGroupService;

    @Override
    public String execute(String param) throws Exception {
        ButtJointClientService xiaoman = buttJointClientFactory.getClient(ButtJointNameConstants.XIAOMAN);
        JSONObject customerGroup = xiaoman.get(XiaomanApiConstant.FIELDS_SELECTOR, new HashMap<String, Object>() {{
            put("field", "group_id");
        }});
        List<XiaomanCustomerGroupDO> groupData = customerGroup.getJSONArray("data").toJavaList(XiaomanCustomerGroupDO.class);
        if(CollUtil.isNotEmpty(groupData))
            xiaomanCustomerGroupService.saveOrUpdateBatch(groupData,true);
        log.info("同步小满客户分组成功,{}条数据",groupData.size());

        JSONObject userList = xiaoman.get(XiaomanApiConstant.USER_LIST, new HashMap<>());
        if(userList.getInteger("code")!=200){
            return "小满用户数据读取失败";
        }
        List<XiaomanUserDO> userData = userList.getJSONArray("data").toJavaList(XiaomanUserDO.class);
        if(CollUtil.isNotEmpty(userData)){
            List<XiaomanUserDO> tempSave = new ArrayList<>();
            for (XiaomanUserDO userDatum : userData) {
                if (!xiaomanUserService.exist(userDatum.getUser_id())) {
                    tempSave.add(userDatum);
                }
            }
            xiaomanUserService.saveOrUpdateBatch(tempSave, true);
        }
        return String.format("小满用户读取成功,供储存%d条记录",userData.size());
    }
}
