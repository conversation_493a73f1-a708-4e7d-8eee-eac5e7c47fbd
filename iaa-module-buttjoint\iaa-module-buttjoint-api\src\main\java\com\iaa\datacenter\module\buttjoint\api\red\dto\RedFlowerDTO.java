package com.iaa.datacenter.module.buttjoint.api.red.dto;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class RedFlowerDTO {
    /**
     * 收花部门
     */
    private String endDept;
    /**
     * 收花人
     */
    private String endUser;
    /**
     * 送花人
     */
    private String startUser;
    /**
     * 送花部门
     */
    private String startDept;
    /**
     * 内容
     */
    private String content;
    /**
     * 事件
     */
    private String title;
    /**
     * 类型
     */
    private String type;
    /**
     * 红花数量
     */
    private Integer countRed;
    /**
     * 黄花数量
     */
    private Integer countBlack;
    /**
     * 时间
     */
    private LocalDateTime dateTime;
}
