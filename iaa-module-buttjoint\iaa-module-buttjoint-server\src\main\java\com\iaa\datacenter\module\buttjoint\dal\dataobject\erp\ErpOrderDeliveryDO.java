package com.iaa.datacenter.module.buttjoint.dal.dataobject.erp;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class ErpOrderDeliveryDO {
    /**
     * 销售订单号
     */
    private String salesOrderNo;
    /**
     * 料号
     */
    private String itemCode;
    /**
     * 所属月份
     */
    private String deliveryMonth;
    /**
     * 下单日期
     */
    private String orderDate;
    /**
     * 预计交期
     */
    private String deliveryDate;
    /**
     * 销售数量
     */
    private BigDecimal orderQty;
    /**
     * 累计完成三数量
     */
    private BigDecimal totalCompletionQty;
    /**
     * 最后报工日期
     */
    private String lastCompletionDate;
    /**
     * 订单状态
     */
    private String orderStatus;
    /**
     * 交付率
     */
    private BigDecimal deliveryRate;
}
