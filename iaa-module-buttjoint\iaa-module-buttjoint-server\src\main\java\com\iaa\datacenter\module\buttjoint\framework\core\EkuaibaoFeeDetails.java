package com.iaa.datacenter.module.buttjoint.framework.core;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;

/**
 * 合思费用明细
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EkuaibaoFeeDetails  implements Serializable {
    /**
     * 转单类型
     */
    private String type;
    /**
     * 组织
     */
    private String org;
    /**
     * 供应商
     */
    private String supplier;
    /**
     * 客户
     */
    private String customer;
    private String customerId;
    /**
     * 付款日期
     */
    private String payDate;
    /**
     * 单号
     */
    private String code;
    /**
     * 提交人
     */
    private String user;
    /**
     * 备注
     */
    private String remark;
    /**
     * 是否预付款
     */
    private Boolean hasPayment;
    /**
     * 是否充值
     */
    private Boolean hasRecharge;
    /**
     * 单据标题
     */
    private String title;
    /**
     * 费用明细行
     */
    private List<EkuaibaoFeeDetailsLine> lines;

}
