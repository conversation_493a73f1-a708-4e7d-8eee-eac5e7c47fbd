<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iaa.datacenter.module.buttjoint.dal.mapper.erp.ErpProjectMapper">

    <select id="page" resultType="com.iaa.datacenter.module.buttjoint.dal.dataobject.erp.ErpProjectDO">
        select
            CBO_Project.Code code,
            concat(CBO_Project.Code,'（',CBO_Project_Trl.Name,')') name
        from CBO_Project
                 left join CBO_Project_Trl on CBO_Project.ID=CBO_Project_Trl.ID
        ${ew.getCustomSqlSegment}
    </select>
</mapper>
