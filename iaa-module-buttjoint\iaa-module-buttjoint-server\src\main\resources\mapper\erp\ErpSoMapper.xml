<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iaa.datacenter.module.buttjoint.dal.mapper.erp.ErpSoMapper">

    <select id="getCustomerYearSales" resultType="com.iaa.datacenter.module.buttjoint.dal.dataobject.erp.ErpCustomerYearSalesDO">
        select
            CBO_Customer.Code as customerCode,
            CBO_Customer_Trl.Name as customerName,
            Year(SM_SO.BusinessDate) as year,
            sum(SM_SOShipLine.TotalMoneyFC) as totalSales
        from SM_SO
            left join SM_SOLine on SO = SM_SO.ID
            left join CBO_Customer on OrderBy_Customer = CBO_Customer.ID
            left join CBO_Customer_Trl on OrderBy_Customer = CBO_Customer_Trl.ID and SysMLFlag = 'zh-CN'
            left join SM_SOShipLine on SOLine=SM_SOLine.ID
        where SM_SOLine.Status>=3
            and CBO_Customer.Code in
            <foreach collection="customers" open="(" item="customer" separator="," close=")">
                #{customer}
            </foreach>
        group by CBO_Customer.Code,CBO_Customer_Trl.Name,Year(SM_SO.BusinessDate)
    </select>

    <select id="getCustomerMonthAndWeekSales" resultType="com.iaa.datacenter.module.buttjoint.dal.dataobject.erp.ErpCustomerMonthAndWeekSalesDO">
        select
        CBO_Customer.Code               as customerCode,
        CBO_Customer_Trl.Name           as customerName,
        DATEPART(YEAR, SM_SO.BusinessDate) as businessYear,
        RIGHT('0' + CONVERT(VARCHAR(2), DATEPART(MONTH, SM_SO.BusinessDate)), 2) as businessMonth,
        CASE
        WHEN DAY(SM_SO.BusinessDate) &lt;= 7 THEN 1
        WHEN DAY(SM_SO.BusinessDate) &lt;= 15 THEN 2
        WHEN DAY(SM_SO.BusinessDate) &lt;= 22 THEN 3
        ELSE 4
        END as businessWeek,
        SUM(SM_SOLine.OrderByQtyTU) as totalQty,
        SUM(SM_SOLine.TotalMoneyFC) as totalSales
        from SM_SO
        left join SM_SOLine on SO = SM_SO.ID
        left join CBO_Customer on OrderBy_Customer = CBO_Customer.ID
        left join CBO_Customer_Trl on OrderBy_Customer = CBO_Customer_Trl.ID and SysMLFlag = 'zh-CN'
        left join SM_SOShipLine on SOLine = SM_SOLine.ID
        where SM_SOLine.Status >= 3
        and CBO_Customer.Code in
        <foreach collection="customers" open="(" item="customer" separator="," close=")">
            #{customer}
        </foreach>
        and SM_SO.BusinessDate between #{dateRange[0]} and #{dateRange[1]}
        group by
        CBO_Customer.Code,
        CBO_Customer_Trl.Name,
        DATEPART(YEAR, SM_SO.BusinessDate),
        RIGHT('0' + CONVERT(VARCHAR(2), DATEPART(MONTH, SM_SO.BusinessDate)), 2),
        CASE
        WHEN DAY(SM_SO.BusinessDate) &lt;= 7 THEN 1
        WHEN DAY(SM_SO.BusinessDate) &lt;= 15 THEN 2
        WHEN DAY(SM_SO.BusinessDate) &lt;= 22 THEN 3
        ELSE 4
        END
        order by
        CBO_Customer.Code,
        CBO_Customer_Trl.Name,
        businessYear,
        businessMonth,
        businessWeek;
    </select>

</mapper>
