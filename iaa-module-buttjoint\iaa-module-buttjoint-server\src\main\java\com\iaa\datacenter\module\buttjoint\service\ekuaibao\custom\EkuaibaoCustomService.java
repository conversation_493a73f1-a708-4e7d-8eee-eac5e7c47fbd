package com.iaa.datacenter.module.buttjoint.service.ekuaibao.custom;

import com.anwen.mongo.service.IService;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.ekuaibao.EkuaibaoCustomDO;

/**
 * 合思自定义档案数据操作类
 */
public interface EkuaibaoCustomService extends IService<EkuaibaoCustomDO> {

    /**
     * 推送数据到合思自定义档案
     * @param dimensionId 合思自定义档案分类ID
     * @param code
     * @param name
     * @return
     */
    void sendDataToCustom(String dimensionId,String code,String name);
}
