package com.iaa.datacenter.module.buttjoint.service.erp.project;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iaa.datacenter.framework.common.pojo.PageResult;
import com.iaa.datacenter.module.buttjoint.controller.admin.erp.custom.vo.ErpCustomPageReqVO;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.ekuaibao.EkuaibaoCustomDO;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.erp.ErpProjectDO;
import com.iaa.datacenter.module.buttjoint.dal.mapper.erp.ErpProjectMapper;
import com.iaa.datacenter.module.buttjoint.service.ekuaibao.custom.EkuaibaoCustomService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.iaa.datacenter.framework.common.exception.util.ServiceExceptionUtil.exception;

@Service
public class ErpProjectServiceImpl implements ErpProjectService{

    @Resource
    private ErpProjectMapper erpProjectMapper;
    @Resource
    private EkuaibaoCustomService ekuaibaoCustomService;
    @Override
    public PageResult<ErpProjectDO> page(ErpCustomPageReqVO pageReqVO) {
        List<EkuaibaoCustomDO> successProjectList = ekuaibaoCustomService.list(
                new com.anwen.mongo.conditions.query.QueryWrapper<EkuaibaoCustomDO>()
                        .eq(EkuaibaoCustomDO::getDimensionId, "ID01AdKeqenyyz:项目")
                        .eq(EkuaibaoCustomDO::getActive, true)
        );

        QueryWrapper<ErpCustomPageReqVO> wrapper = new QueryWrapper<>();
        if(CollUtil.isNotEmpty(successProjectList)){
            wrapper.notIn("CBO_Project.Code",successProjectList.stream().map(EkuaibaoCustomDO::getCode).toList());
        }
        wrapper.eq("Effective_IsEffective",1);
        wrapper.like(StrUtil.isNotEmpty(pageReqVO.getCode()),"CBO_Project.Code",pageReqVO.getCode());
        wrapper.like(StrUtil.isNotEmpty(pageReqVO.getName()),"concat(CBO_Project.Code,'（',CBO_Project_Trl.Name,')')",pageReqVO.getName());
        wrapper.orderByAsc("CBO_Project.Code");
        IPage<ErpProjectDO> page = erpProjectMapper.page(
                new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize()),
                wrapper
        );
        return new PageResult<>(page.getRecords(),page.getTotal());
    }

}
