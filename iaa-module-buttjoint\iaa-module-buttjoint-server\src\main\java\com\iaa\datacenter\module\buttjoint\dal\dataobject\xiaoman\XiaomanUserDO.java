package com.iaa.datacenter.module.buttjoint.dal.dataobject.xiaoman;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.anwen.mongo.annotation.ID;
import com.anwen.mongo.annotation.collection.CollectionField;
import com.anwen.mongo.annotation.collection.CollectionName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@CollectionName("xiaoman_user")
public class XiaomanUserDO {

    /**
     * 用户ID
     */
    @ID
    private String user_id;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 用户昵称
     */
    private String nickname;
    /**
     * 姓
     */
    private String family_name;
    /**
     * 名
     */
    private String second_name;
    /**
     * 性别
     */
    private Integer gender;
    /**
     * 职位
     */
    private String position;
    /**
     * 是否启用
     */
    @CollectionField
    private Boolean enable_flag;
    /**
     * 手机
     */
    private String user_mobile;
    /**
     * AMES邮箱
     */
    private String ames_email;
    /**
     * 员工编号
     */
    private String employee_no;
    /**
     * 外部邮箱
     */
    private String external_email;
    /**
     * 外部手机
     */
    private String external_mobile;
    /**
     * 外部传真
     */
    private String external_fax;
    /**
     * 外部地址
     */
    private String external_address;
    /**
     * 部门名称
     */
    private String department_name;
    /**
     * 部门ID
     */
    private Long department_id;
    /**
     * 部门
     */
    private JSONArray departments;
    /**
     * 全名
     */
    private String full_name;
    /**
     * 外部其他
     */
    private String external_other;
    /**
     * 父部门ID
     */
    private Long parent_id;
    /**
     * 中文姓名
     */
    private String chinese_name;
}
