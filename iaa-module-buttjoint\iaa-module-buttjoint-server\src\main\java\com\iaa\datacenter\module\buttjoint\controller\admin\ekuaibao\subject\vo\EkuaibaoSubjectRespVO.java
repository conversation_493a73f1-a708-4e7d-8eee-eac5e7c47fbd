package com.iaa.datacenter.module.buttjoint.controller.admin.ekuaibao.subject.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.iaa.datacenter.framework.common.annotation.WebTableColumn;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
@ExcelIgnoreUnannotated
@Schema(description = "管理后台 - 合思科目 Request VO")
public class EkuaibaoSubjectRespVO {

    @ExcelProperty("科目ID")
    @WebTableColumn(label="科目ID",noSearch = true)
    @NotNull(message="科目ID不能为空")
    private String id;

    @ExcelProperty("费用编码")
    @WebTableColumn(label="费用编码")
    private String code;

    @ExcelProperty("费用类型")
    @WebTableColumn(label="费用类型")
    private String name;

    @ExcelProperty("管理类科目")
    @WebTableColumn(label="管理类科目",noSearch = true)
    private String managementCode;

    @ExcelProperty("销售类科目")
    @WebTableColumn(label="销售类科目",noSearch = true)
    private String salesCode;

    @ExcelProperty("制造类科目")
    @WebTableColumn(label="制造类科目",noSearch = true)
    private String manufactureCode;

    @ExcelProperty("研发类科目")
    @WebTableColumn(label="研发类科目",noSearch = true)
    private String developmentCode;

}
