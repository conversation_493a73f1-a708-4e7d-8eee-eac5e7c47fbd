package com.iaa.datacenter.module.buttjoint.framework.core;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 合思发票明细
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EkuaibaoFeeInvoice  implements Serializable {
    /**
     * 是否专用
     */
    private boolean hasSpecial;
    /**
     * 发票金额
     */
    private BigDecimal amount;
    /**
     * 税额
     */
    private BigDecimal taxAmount;
    /**
     * 税号
     */
    private String taxCode;
    /**
     * 税率
     */
    private BigDecimal rate;

}
