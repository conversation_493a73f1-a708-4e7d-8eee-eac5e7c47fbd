package com.iaa.datacenter.module.buttjoint.dal.dataobject.wework;

import com.anwen.mongo.annotation.ID;
import com.anwen.mongo.annotation.collection.CollectionName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@CollectionName("wework_user")
public class WeworkUserDO {

    @ID
    private String id;

    private String phone;
}
