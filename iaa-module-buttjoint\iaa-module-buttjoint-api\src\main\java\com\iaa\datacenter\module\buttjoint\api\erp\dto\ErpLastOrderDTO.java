package com.iaa.datacenter.module.buttjoint.api.erp.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * erp最后下单对象
 */
@Data
public class ErpLastOrderDTO {
    /**
     * 品号
     */
    private String itemCode;
    /**
     * 品名
     */
    private String itemName;
    /**
     * 规格
     */
    private String spec;
    /**
     * 业务日期
     */
    private LocalDate businessDate;
    /**
     * 订单数据
     */
    private BigDecimal orderQty;
}
