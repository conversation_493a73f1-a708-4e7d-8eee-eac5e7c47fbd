package com.iaa.datacenter.framework.common.annotation;

import java.lang.annotation.*;

@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Inherited
public @interface WebTableColumn {
    /**
     * 列名
     * @return
     */
     String label() default "";

    /**
     * 是否忽略
     * @return
     */
     boolean exists() default false;
    /**
     * 列宽
     * @return
     */
     int width() default -1;

    /**
     * 对齐方式
     * @return
     */
     String align() default "center" ;

    /**
     * 字典
     * @return
     */
     String dict() default "";

    /**
     * 不参与搜索
     * @return
     */
    boolean noSearch() default false;

    /**
     * 是否日期类型
     * @return
     */
    boolean date() default  false;
}
