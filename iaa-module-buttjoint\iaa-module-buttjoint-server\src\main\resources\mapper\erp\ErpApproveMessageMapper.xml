<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iaa.datacenter.module.buttjoint.dal.mapper.erp.ErpApproveMessageMapper">

    <select id="getById" resultType="com.iaa.datacenter.module.buttjoint.dal.dataobject.erp.ErpApproveMessageDO">
        select Cust_WorkflowMessage.ID as id,
               Title                   as title,
               Content                 as content,
               Wechat                  as wechat,
               UserID                  as userId,
               MessageID               as messageId,
               IsApproved              as isApproved,
               Cust_WorkflowMessage.CreateOn as createOn
        from Cust_WorkflowMessage
                 left join Cust_CertificationUser on CurrentName = UserID
        where Cust_WorkflowMessage.ID = #{id}
    </select>

    <select id="list" resultType="com.iaa.datacenter.module.buttjoint.dal.dataobject.erp.ErpApproveMessageDO">
        select Cust_WorkflowMessage.ID as id,
               Title                   as title,
               Content                 as content,
               Wechat                  as wechat,
               UserID                  as userId,
               MessageID               as messageId,
               Cust_WorkflowMessage.CreateOn as createOn,
               Cust_WorkflowMessage.TaskId as taskId,
               A.Operation as operation,
               B.StateInfo as stateInfo,
               A.TackState as tackState,
               D.State as state
        from Cust_WorkflowMessage
                 left join Cust_CertificationUser on CurrentName = UserID
                 inner join CS_Workflow_ProcessTrack   A on A.ID=MessageID
                 inner JOIN CS_Workflow_WaitingUser C ON C.ID=A.WaitingUser
                 inner JOIN CS_Workflow_FlowState B ON C.FlowState=B.ID
                 inner join  CS_Workflow_FlowInstance d on B.FlowInstance=D.ID
        where D.ModifiedOn between #{startTime} and #{endTime}
    </select>

</mapper>
