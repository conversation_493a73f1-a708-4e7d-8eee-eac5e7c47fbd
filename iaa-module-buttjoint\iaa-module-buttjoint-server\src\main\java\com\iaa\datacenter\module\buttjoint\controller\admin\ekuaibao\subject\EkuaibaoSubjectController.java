package com.iaa.datacenter.module.buttjoint.controller.admin.ekuaibao.subject;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.anwen.mongo.conditions.query.QueryWrapper;
import com.anwen.mongo.conditions.update.UpdateWrapper;
import com.iaa.datacenter.framework.common.core.KeyValue;
import com.iaa.datacenter.framework.common.pojo.CommonResult;
import com.iaa.datacenter.framework.common.pojo.PageResult;
import com.iaa.datacenter.framework.common.pojo.WebTableColumn;
import com.iaa.datacenter.framework.common.util.object.BeanUtils;
import com.iaa.datacenter.framework.common.util.object.WebColumnUtils;
import com.iaa.datacenter.module.buttjoint.controller.admin.ekuaibao.subject.vo.EkuaibaoSubjectPageReqVO;
import com.iaa.datacenter.module.buttjoint.controller.admin.ekuaibao.subject.vo.EkuaibaoSubjectRespVO;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.ekuaibao.EkuaibaoCustomDO;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.ekuaibao.EkuaibaoFeeTypeDO;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.ekuaibao.EkuaibaoSubjectDO;
import com.iaa.datacenter.module.buttjoint.dal.mapper.erp.ErpCustomMapper;
import com.iaa.datacenter.module.buttjoint.service.ekuaibao.feetype.EkuaibaoFeeTypeService;
import com.iaa.datacenter.module.buttjoint.service.ekuaibao.subject.EkuaibaoSubjectService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.iaa.datacenter.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 合思部门维护")
@RestController
@RequestMapping("/butt-joint/ekuaibao/subject")
@Validated
@Slf4j
public class EkuaibaoSubjectController {
    @Resource
    private EkuaibaoSubjectService ekuaibaoSubjectService;
    @Resource
    private EkuaibaoFeeTypeService ekuaibaoFeeTypeService;
    @Resource
    private ErpCustomMapper erpCustomMapper;
    @GetMapping("/get-columns")
    @Operation(summary = "获取合思部门列信息")
    @PreAuthorize("@ss.hasPermission('ekuaibao:subject:list')")
    public CommonResult<List<WebTableColumn>> getColumns(){
        return success(WebColumnUtils.parseTableColumn(EkuaibaoSubjectRespVO.class));
    }

    @GetMapping("/get-erp-all-subject")
    @Operation(summary = "获取U9所有科目信息")
    @PreAuthorize("@ss.hasPermission('ekuaibao:subject:list')")
    public CommonResult<List<KeyValue<String,String>>> getErpAllSubject(){
        return success(erpCustomMapper.getSubject());
    }

    @GetMapping("/page")
    @Operation(summary = "获取合思部门数据")
    @PreAuthorize("@ss.hasPermission('ekuaibao:subject:list')")
    public CommonResult<PageResult<EkuaibaoSubjectRespVO>> page(@Valid EkuaibaoSubjectPageReqVO reqVO){
        QueryWrapper<EkuaibaoSubjectDO> wrapper = new QueryWrapper<>();
        wrapper.like(StrUtil.isNotEmpty(reqVO.getName()),EkuaibaoSubjectDO::getName,reqVO.getName());
        // 如果存在父部门名称，则查询子部门
        if(StrUtil.isNotEmpty(reqVO.getCode())){
            List<EkuaibaoFeeTypeDO> list = ekuaibaoFeeTypeService.list(new QueryWrapper<EkuaibaoFeeTypeDO>().like(EkuaibaoFeeTypeDO::getCode, reqVO.getCode()));
            wrapper.in(CollUtil.isNotEmpty(list),EkuaibaoSubjectDO::getId,list.stream().map(EkuaibaoFeeTypeDO::getId).toList());
        }
        com.anwen.mongo.model.PageResult<EkuaibaoSubjectDO> page = ekuaibaoSubjectService.page(wrapper, reqVO.getPageNo(), reqVO.getPageSize());
        page.getContentData().forEach(item->{
            EkuaibaoFeeTypeDO subjectDO = ekuaibaoFeeTypeService.getById(item.getId());
            item.setCode(subjectDO.getCode());
        });
        return success(new PageResult<>(BeanUtils.toBean(page.getContentData(), EkuaibaoSubjectRespVO.class),page.getTotalSize()));
    }

    @PostMapping("/save")
    @Operation(summary = "更新合思客户数据")
    @PreAuthorize("@ss.hasPermission('ekuaibao:subject:list')")
    public CommonResult<Boolean> save(@Valid @RequestBody EkuaibaoSubjectRespVO saveVO){
        return success(ekuaibaoSubjectService.update(new UpdateWrapper<EkuaibaoSubjectDO>()
                .eq(EkuaibaoSubjectDO::getId,saveVO.getId())
                .set(EkuaibaoSubjectDO::getManagementCode,saveVO.getManagementCode())
                .set(EkuaibaoSubjectDO::getSalesCode,saveVO.getSalesCode())
                .set(EkuaibaoSubjectDO::getDevelopmentCode,saveVO.getDevelopmentCode())
                .set(EkuaibaoSubjectDO::getManufactureCode,saveVO.getManufactureCode()))
        );
    }
}
