package com.iaa.datacenter.module.buttjoint.dal.mapper.plm;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iaa.datacenter.framework.common.core.KeyValue;
import com.iaa.datacenter.module.buttjoint.controller.admin.plm.vo.AttributeTableColumnRespVO;
import com.iaa.datacenter.module.buttjoint.controller.admin.plm.vo.ItemCategoryRespVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

@DS("plm")
@Mapper
public interface AttributeMapper {

    @Select("""
            select
                CTYPEID as id,
                CPARENTID as parentId,
                CTYPENAME as name,
                CJCTYPEKEYID as code
            from BOM_083
            where CJCTYPEKEYID like '1%' and CISCHILD = 0
            order by CJCTYPEKEYID
            """)
    List<ItemCategoryRespVO> getItemCategoryList();


    @Select("""
            select
                BOM_156.VALUE as prop,
                BOM_050.CHINESENAME as label,
                iif(BOM_050.INPUTTYPE ='2','dict','input') as type,
                BOM_050.TABLENAME as sourceTable
            from BOM_156
            join BOM_050 on BOM_156.VALUE = BOM_050.COLUMNNAME
            where BOM_156.INPUTTYPE='MATCOL' and BOM_156.MATERIALTYPE = #{id} 
            order by BOM_156.CORDER
            """)
    List<AttributeTableColumnRespVO> getAttributeList(String id);

    @Select("""
            select
                COlNAME as 'key',
                iif(LISTVALUE='','空',LISTVALUE) as value
            from sys_035
            ${ew.getCustomSqlSegment}
            """)
    List<KeyValue<String,String>> getAttributeDictList(@Param("ew") Wrapper<?> wrapper);

    @Select("""
            select
                BOM_027.PARTID as itemCode,
                BOM_027.PARTVAR as itemVar,
                BOM_027.CHINANAME as itemName,
                BOM_027.MODEL as model,
                ${query}
            from BOM_027
            join BOM_028 on BOM_027.PARTID = BOM_028.PARTID and BOM_027.PARTVAR = BOM_028.PARTVAR
            ${ew.getCustomSqlSegment}
            """)
    Page<Map<String,String>> getItemList(@Param("query")String query, @Param("page") Page<?> page,@Param("ew") Wrapper<?> wrapper);
}
