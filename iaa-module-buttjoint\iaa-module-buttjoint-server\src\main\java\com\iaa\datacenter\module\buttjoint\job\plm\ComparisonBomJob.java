package com.iaa.datacenter.module.buttjoint.job.plm;

import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.iaa.datacenter.framework.common.enums.UserTypeEnum;
import com.iaa.datacenter.framework.mybatis.core.dataobject.BaseDO;
import com.iaa.datacenter.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.iaa.datacenter.framework.quartz.core.handler.JobHandler;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.backend.EngBomComparisonDO;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.backend.EngBomComparisonRuleDO;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.plm.PlmBomTopDO;
import com.iaa.datacenter.module.buttjoint.dal.mapper.backend.EngBomComparisonMapper;
import com.iaa.datacenter.module.buttjoint.dal.mapper.backend.EngBomComparisonRuleMapper;
import com.iaa.datacenter.module.buttjoint.dal.mapper.erp.ErpBomMapper;
import com.iaa.datacenter.module.buttjoint.dal.mapper.plm.BomMapper;
import com.iaa.datacenter.module.system.api.social.SocialClientApi;
import com.iaa.datacenter.module.system.api.social.SocialUserApi;
import com.iaa.datacenter.module.system.api.social.dto.SocialUserRespDTO;
import com.iaa.datacenter.module.system.api.user.AdminUserApi;
import com.iaa.datacenter.module.system.enums.social.SocialTypeEnum;
import jakarta.annotation.Resource;
import me.chanjar.weixin.cp.api.WxCpMessageService;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.api.impl.WxCpServiceImpl;
import me.chanjar.weixin.cp.bean.message.WxCpMessage;
import me.chanjar.weixin.cp.config.impl.WxCpDefaultConfigImpl;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class ComparisonBomJob implements JobHandler {

    @Resource
    private BomMapper bomMapper;
    @Resource
    private ErpBomMapper erpBomMapper;
    @Resource
    private EngBomComparisonMapper engBomComparisonMapper;
    @Resource
    private EngBomComparisonRuleMapper engBomComparisonRuleMapper;
    @Resource
    private SocialUserApi socialUserApi;

    @Override
    @DSTransactional
    public String execute(String param) throws Exception {
        EngBomComparisonRuleDO engBomComparisonRuleDO = engBomComparisonRuleMapper.selectOne(new LambdaQueryWrapperX<EngBomComparisonRuleDO>().eq(BaseDO::getDeleted, false));
        // 获取昨日归档的BOM数据
        LocalDate now = LocalDate.now().minusDays(1);
        QueryWrapper<?> bomWrapper = new QueryWrapper<>();
        bomWrapper.eq("BOMSTATE", "frozen")
                .eq("PARTFIXEDBACK4", "1")
                .between("BOM_054.CFROZENTIME", now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + " 00:00:00", now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + " 23:59:59")
                .orderByDesc("BOM_054.BOMACHIVETIME");
        if (Objects.nonNull(engBomComparisonRuleDO) && Objects.nonNull(engBomComparisonRuleDO.getWatchType())) {
            String[] ruleType = engBomComparisonRuleDO.getWatchType().split(",");

            bomWrapper.and(p -> {
                for (String rule : ruleType) {
                    p.likeRight("BOM_027.PARTID", rule).or();
                }
            });

        }
        List<PlmBomTopDO> bomTopList = bomMapper.selectBomTop(bomWrapper);

        List<EngBomComparisonDO> engBomComparisonList = new ArrayList<>();
        // 遍历比对两系统间数据不一致部分
        for (PlmBomTopDO plmBomTopDO : bomTopList) {
            EngBomComparisonDO engBomComparison = new EngBomComparisonDO();
            engBomComparison.setBomName(plmBomTopDO.getBomName());
            engBomComparison.setFrozenTime(plmBomTopDO.getFrozenTime());
            engBomComparison.setDiffMessage("");

            engBomComparisonList.add(engBomComparison);

            // 获取两系统中当前BOM的物料列表
            List<String> plmBomItemList = bomMapper.selectBomItemList(plmBomTopDO.getBomName());
            List<String> erpBomItemList = erpBomMapper.selectBomItemList(plmBomTopDO.getItemCode());

            // 查找PLM中独有的物料
            List<String> onlyInPlm = new ArrayList<>(plmBomItemList);
            onlyInPlm.removeAll(erpBomItemList);
            if (!onlyInPlm.isEmpty()) {
                engBomComparison.setDiffMessage(engBomComparison.getDiffMessage() + "PLM中独有物料：\n");
                for (String item : onlyInPlm) {
                    String[] itemColumn = item.split("-");
                    engBomComparison.setDiffMessage(engBomComparison.getDiffMessage() + "第" + itemColumn[2] + "层" + itemColumn[0] + "下的" + itemColumn[1] + "\n");
                }
            }
            // 查找ERP中独有的物料
            List<String> onlyInErp = new ArrayList<>(erpBomItemList);
            onlyInErp.removeAll(plmBomItemList);
            if (!onlyInErp.isEmpty()) {
                engBomComparison.setDiffMessage(engBomComparison.getDiffMessage() + "ERP中独有物料：\n");
                for (String item : onlyInErp) {
                    String[] itemColumn = item.split("-");
                    engBomComparison.setDiffMessage(engBomComparison.getDiffMessage() + "第" + itemColumn[2] + "层" + itemColumn[0] + "下的" + itemColumn[1] + "\n");
                }
            }

            int maxSize = Math.max(plmBomItemList.size(), erpBomItemList.size());
            for (int i = 0; i < maxSize; i++) {
                String plmItem = i < plmBomItemList.size() ? plmBomItemList.get(i) : "NULL";
                String erpItem = i < erpBomItemList.size() ? erpBomItemList.get(i) : "NULL";

                if (!plmItem.equals(erpItem)) {
                    String[] itemColumn = plmItem.split("-");
                    engBomComparison.setDiffMessage(engBomComparison.getDiffMessage() + "PLM第" + itemColumn[2] + "层" + itemColumn[0] + "下的" + itemColumn[1] + "与ERP结构不一致\n");

                }
            }

            PlmBomTopDO erpBomTop = erpBomMapper.selectBomTop(plmBomTopDO.getItemCode());
            if (Objects.isNull(erpBomTop)) continue;
            if (!plmBomTopDO.getPackagingComplete().equals(erpBomTop.getPackagingComplete())) {
                engBomComparison.setDiffMessage(engBomComparison.getDiffMessage() + "PLM的包装完成状态与ERP不一致\n");
            }
            if (!plmBomTopDO.getPanelComplete().equals(erpBomTop.getPanelComplete())) {
                engBomComparison.setDiffMessage(engBomComparison.getDiffMessage() + "PLM的面板完成状态与ERP不一致\n");
            }
            if (!plmBomTopDO.getProgramComplete().equals(erpBomTop.getProgramComplete())) {
                engBomComparison.setDiffMessage(engBomComparison.getDiffMessage() + "PLM的程序完成状态与ERP不一致\n");
            }
            if (!plmBomTopDO.getOtherComplete().equals(erpBomTop.getOtherComplete())) {
                engBomComparison.setDiffMessage(engBomComparison.getDiffMessage() + "PLM的其它完成状态与ERP不一致\n");
            }
        }
        List<EngBomComparisonDO> list = engBomComparisonList.stream().filter(item -> StrUtil.isNotEmpty(item.getDiffMessage())).toList();
        if (!list.isEmpty()) {
            engBomComparisonMapper.delete(new LambdaQueryWrapperX<EngBomComparisonDO>().between(EngBomComparisonDO::getFrozenTime, now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + " 00:00:00", now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + " 23:59:59"));
            engBomComparisonMapper.insertBatch(list);
        }
        if (Objects.nonNull(engBomComparisonRuleDO) && Objects.nonNull(engBomComparisonRuleDO.getPushPersonnel())) {
            WxCpService cpService = new WxCpServiceImpl();
            WxCpDefaultConfigImpl config = new WxCpDefaultConfigImpl();
            config.setCorpId("ww82c87150c4c5a9ba");
            config.setAgentId(1000036);
            config.setCorpSecret("ozm7ctF4pMY0YAbzRcRCjtTY31v1s3nk5t7XuxZyLPc");
            cpService.setWxCpConfigStorage(config);
            List<String> weworkUserIds = new ArrayList<>();
            for (Long userId : engBomComparisonRuleDO.getPushPersonnel()) {
                SocialUserRespDTO socialUser = socialUserApi.getSocialUserByUserId(UserTypeEnum.ADMIN.getValue(), userId, SocialTypeEnum.WECHAT_ENTERPRISE.getType());
                weworkUserIds.add(socialUser.getOpenid());
            }
            StringBuilder sb = new StringBuilder();
            sb.append("检测昨日归档BOM共").append(engBomComparisonList.size()).append("条，有差异部分共").append(list.size()).append("条\n");
            if (list.isEmpty()) {
                sb.append("昨日BOM已全部导出至ERP");
            } else {
                sb.append("请前往：https://sj.iaa360.cn:13141/technology/BomComparison").append(" 查看详情");
            }
            WxCpMessage message = WxCpMessage.TEXT()
                    .toUser(String.join("|", weworkUserIds))
                    .content(sb.toString())
                    .build();
            WxCpMessageService messageService = cpService.getMessageService();
            messageService.send(message);
        }
        return "执行成功";
    }
}
