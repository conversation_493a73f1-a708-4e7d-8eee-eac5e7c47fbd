package com.iaa.datacenter.module.buttjoint.service.landray.todo;

import com.iaa.datacenter.module.buttjoint.dal.dataobject.ekuaibao.EkuaibaoApproveMessageDO;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.erp.ErpApproveMessageDO;

/**
 * 蓝凌OA待办接口
 */
public interface LandrayTodoService {

    /**
     * 合思待办发送到蓝凌
     * @param messageDO
     */
    EkuaibaoApproveMessageDO sendToDo(EkuaibaoApproveMessageDO messageDO);

    /**
     * ERP审批待办发送到蓝凌
     * @param messageDO
     * @return
     */
    ErpApproveMessageDO sendToDo(ErpApproveMessageDO messageDO);
}
