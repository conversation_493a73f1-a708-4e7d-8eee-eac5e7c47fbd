package com.iaa.datacenter.module.buttjoint.service.erp.customer;

import com.iaa.datacenter.module.buttjoint.controller.admin.erp.custom.vo.ErpCustomPageReqVO;
import com.iaa.datacenter.module.buttjoint.controller.admin.erp.custom.vo.ErpCustomRespVO;
import com.iaa.datacenter.module.buttjoint.controller.admin.erp.custom.vo.ErpItemRespVO;

import java.util.List;

public interface ErpCustomerService {

    /**
     * 获取客户列表
     * @param pageReqVO
     * @return
     */
    List<ErpCustomRespVO>  getCustomerList(ErpCustomPageReqVO pageReqVO);

    /**
     * 获取料品列表
     * @param itemCode
     * @return
     */
    List<ErpItemRespVO> getItemList(String itemCode);
}
