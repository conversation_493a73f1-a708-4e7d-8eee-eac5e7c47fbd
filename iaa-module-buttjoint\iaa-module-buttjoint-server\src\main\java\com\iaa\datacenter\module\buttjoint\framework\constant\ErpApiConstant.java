package com.iaa.datacenter.module.buttjoint.framework.constant;

/**
 * erp api 枚举
 */
public interface ErpApiConstant {
    //创建现金银行收付款单据
    String CMR_RCV_PAY_BILL="CMRcvPayBill/Create";
    //提交现金银行收款单
    String CMR_RCV_PAY_BILL_SUBMIT="CMRcvPayBill/Submit";
    //审核现金银行收款单
    String CMR_RCV_PAY_BILL_AUDIT="CMRcvPayBill/Approve";
    //审核现金银行收款单
    String CMR_RCV_PAY_BILL_UN_AUDIT="CMRcvPayBill/UnApprove";
    //新增付款单
    String PAY_BILL="PayBill/Create";
    //提交付款单
    String PAY_BILL_SUBMIT="PayBill/Submit";
    //审核付款单
    String PAY_BILL_APPROVE="PayBill/Approve";
    //弃审付款单
    String PAY_BILL_UNAPPROVE="PayBill/UnApprove";
    //删除付款单
    String PAY_BILL_DELETE="PayBill/Delete";
    //创建收款单
    String REC_BILL_CREATE="RecBill/Create";
    //提交收款单
    String REC_BILL_SUBMIT="RecBill/Submit";
    //审核收款单
    String REC_BILL_APPROVE="RecBill/Approve";
    //弃审收款单
    String REC_BILL_UN_APPROVE="RecBill/UnApprove";

    String AP_BILL_CREATE="APBill/Create";

    String AP_BILL_SUBMIT="APBill/Submit";

    String AP_BILL_APPROVE="APBill/Approve";

    String VOUCHER_CREATE="Voucher/Create";

    String VOUCHER_DELETE="Voucher/Delete";

    String APBILL_DELETE="APBill/Delete";

    String APBILL_UN_APPROVE="APBill/UnApprove";
}
