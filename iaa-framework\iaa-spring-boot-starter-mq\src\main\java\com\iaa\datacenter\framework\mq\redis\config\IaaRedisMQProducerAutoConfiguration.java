package com.iaa.datacenter.framework.mq.redis.config;

import com.iaa.datacenter.framework.mq.redis.core.RedisMQTemplate;
import com.iaa.datacenter.framework.mq.redis.core.interceptor.RedisMessageInterceptor;
import com.iaa.datacenter.framework.redis.config.IaaRedisAutoConfiguration;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.List;

/**
 * Redis 消息队列 Producer 配置类
 *
 * <AUTHOR>
 */
@Slf4j
@AutoConfiguration(after = IaaRedisAutoConfiguration.class)
public class IaaRedisMQProducerAutoConfiguration {

    @Bean
    public RedisMQTemplate redisMQTemplate(StringRedisTemplate redisTemplate,
                                           List<RedisMessageInterceptor> interceptors) {
        RedisMQTemplate redisMQTemplate = new RedisMQTemplate(redisTemplate);
        // 添加拦截器
        interceptors.forEach(redisMQTemplate::addInterceptor);
        return redisMQTemplate;
    }

}
