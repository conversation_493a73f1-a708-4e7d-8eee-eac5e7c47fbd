package com.iaa.datacenter.framework.buttjoint.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

import java.util.HashMap;
import java.util.Map;

/**
 * 对接系统的基础API URL
 */
@ConfigurationProperties(prefix = "iaa.butt-joint")
@Data
@Validated
public class ButtJointProperties {

    private Map<String,ButtJointConfig> type=new HashMap<>();
}
