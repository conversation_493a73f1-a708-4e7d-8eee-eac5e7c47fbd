package com.iaa.datacenter.module.buttjoint.service.erp.custom;

import com.iaa.datacenter.framework.common.core.KeyValue;
import com.iaa.datacenter.module.buttjoint.dal.mapper.erp.ErpCustomMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ErpCustomServiceImpl implements ErpCustomService{
    @Resource
    private ErpCustomMapper erpCustomMapper;
    @Override
    public void updateJstToken(String token) {
        erpCustomMapper.updateJstToken(token);
    }

    @Override
    public List<KeyValue<String, String>> getJstToken() {
        return erpCustomMapper.getJstToken();
    }
}
