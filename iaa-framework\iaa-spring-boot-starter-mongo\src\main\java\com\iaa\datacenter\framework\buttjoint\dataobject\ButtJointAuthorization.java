package com.iaa.datacenter.framework.buttjoint.dataobject;

import com.alibaba.fastjson.JSONObject;
import com.anwen.mongo.annotation.ID;
import com.anwen.mongo.annotation.collection.CollectionName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@CollectionName("integration_authorization")
public class ButtJointAuthorization {

    /**
     * ID
     */
    @ID
    private String id;

    /**
     * 授权信息
     */
    private JSONObject authorization;

    /**
     * 过期时间
     */
    private Long expireTime;

}
