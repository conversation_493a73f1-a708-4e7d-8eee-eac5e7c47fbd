<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.iaa.datacenter</groupId>
        <artifactId>iaa-framework</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>iaa-spring-boot-starter-security</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>
        1. security：用户的认证、权限的校验，实现「谁」可以做「什么事」
        2. operatelog：操作日志，实现「谁」在「什么时间」对「什么」做了「什么事」
    </description>
    <url>https://github.com/YunaiV/ruoyi-vue-pro</url>

    <dependencies>
        <dependency>
            <groupId>com.iaa.datacenter</groupId>
            <artifactId>iaa-common</artifactId>
        </dependency>

        <!-- Spring 核心 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>

        <!-- Web 相关 -->
        <dependency>
            <groupId>com.iaa.datacenter</groupId>
            <artifactId>iaa-spring-boot-starter-web</artifactId>
        </dependency>
        <!-- spring boot 配置所需依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>

        <!-- RPC 远程调用相关 -->
        <dependency>
            <groupId>com.iaa.datacenter</groupId>
            <artifactId>iaa-spring-boot-starter-rpc</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- 工具类相关 -->
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>

        <dependency>
            <!-- Spring Boot 通用操作日志组件，基于注解实现 -->
            <!-- 此组件解决的问题是：「谁」在「什么时间」对「什么」做了「什么事」 -->
            <groupId>io.github.mouzt</groupId>
            <artifactId>bizlog-sdk</artifactId>
        </dependency>

    </dependencies>

</project>
