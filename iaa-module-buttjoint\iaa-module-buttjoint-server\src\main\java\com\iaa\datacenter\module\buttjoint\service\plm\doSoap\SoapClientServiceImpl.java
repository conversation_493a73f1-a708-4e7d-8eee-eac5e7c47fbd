package com.iaa.datacenter.module.buttjoint.service.plm.doSoap;

import cn.hutool.http.webservice.SoapClient;
import com.iaa.datacenter.framework.common.exception.ErrorCode;
import com.iaa.datacenter.module.buttjoint.controller.admin.plm.vo.SoapClientRespVO;
import com.iaa.datacenter.module.infra.api.config.ConfigApi;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.xml.transform.OutputKeys;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import javax.xml.xpath.XPath;
import javax.xml.xpath.XPathFactory;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import org.w3c.dom.Document;
import org.xml.sax.InputSource;
import java.io.StringReader;
import java.io.StringWriter;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;

import static com.iaa.datacenter.framework.common.exception.util.ServiceExceptionUtil.exception;

@Service
@Slf4j
public class SoapClientServiceImpl implements SoapClientService {

    private static final int CONNECT_TIMEOUT = 100000; // 连接超时：100秒
    private static final int RECEIVE_TIMEOUT = 300000; // 接收超时：300秒

    @Resource
    private ConfigApi configApi;
    @Override
    public String getDocPathOrWx(SoapClientRespVO soapClientRespVO) throws Exception{
        //从配置参数中获取 plmWsdlUrl、prefix（预览URL）
        soapClientRespVO.setPlmWsdlUrl(configApi.getConfigValueByKey("url.plm.drawings").getData());
        soapClientRespVO.setPrefix(configApi.getConfigValueByKey("url.plm.preview").getData());
        // 参数校验
        validateParam(soapClientRespVO.getPlmWsdlUrl(), "plmWsdlUrl（调用Url）");
        validateParam(soapClientRespVO.getDocId(), "docId（文档编号）");
        validateParam(soapClientRespVO.getDocVer(), "docVer（文档版本）");
        validateParam(soapClientRespVO.getUuid(), "uuid（用户ID）");
        validateParam(soapClientRespVO.getPrefix(),"prefix（返回路径前缀）");
        log.info("开始调用PLM {}接口：文档ID={}, 版本={}, 用户ID={}",soapClientRespVO.getMethodName(),
                soapClientRespVO.getDocId(), soapClientRespVO.getDocVer(), soapClientRespVO.getUuid());

        try {
            // 构建 SOAP 请求体
            String soapRequestBody = buildSoapRequestBody(soapClientRespVO);

            // 获取实际的 WebService 端点 URL（可能与 WSDL URL 不同）
            String endpointUrl = getWebServiceEndpoint(soapClientRespVO.getPlmWsdlUrl());

            // 使用 Hutool 发送 HTTP 请求
            String response = cn.hutool.http.HttpRequest.post(endpointUrl)
                    .header("Content-Type", "text/xml; charset=utf-8")
                    .header("SOAPAction", "") // 根据实际服务要求设置
                    .timeout(RECEIVE_TIMEOUT)
                    .body(soapRequestBody)
                    .execute()
                    .body();

            if (response == null || response.isEmpty()) {
                throw new RuntimeException("PLM getDocPath接口返回为空：文档ID=" + soapClientRespVO.getDocId());
            }

            // 检查是否返回了 HTML 错误页面
            if (response.contains("<html") || response.contains("<HTML")) {
                log.error("PLM 接口返回了 HTML 页面而非 XML 响应: {}", response);
                throw new RuntimeException("PLM getDocPath接口调用失败，返回了错误页面");
            }

            // 提取 SOAP 响应中的内容
            String responseBody = extractSoapBody(response);


            // 从响应中提取实际的 XML 内容
            String actualXmlContent = extractActualXmlContent(responseBody);

            // 格式化 XML 响应
            String docListXml = formatXml(actualXmlContent);
            log.info("PLM getDocPath接口返回：{}", docListXml);

            // 解析 XML 为 Document 对象
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document document = builder.parse(new InputSource(new StringReader(docListXml)));
            XPath xPath = XPathFactory.newInstance().newXPath();
            String eName = xPath.evaluate("/docpathlist/docpath/filename/ename", document);

            // 返回下载文件路径
            return soapClientRespVO.getPrefix() + eName;
        } catch (Exception e) {
            log.error("PLM getDocPath接口调用失败，原因：{}", e.getMessage(), e);
            throw exception(new ErrorCode(500, "PLM getDocPath接口调用失败，原因：" + e.getMessage()));
        }
    }

    @Override
    public void destoryOrWx(SoapClientRespVO soapClientRespVO) throws Exception {
        soapClientRespVO.setPlmWsdlUrl(configApi.getConfigValueByKey("url.plm.drawings").getData());
        validateParam(soapClientRespVO.getPlmWsdlUrl(), "调用Url");
        validateParam(soapClientRespVO.getMethodName(), "方法名");
        validateParam(soapClientRespVO.getUuid(), "uuid（用户ID）");

        log.info("开始调用PLM {}接口： 用户ID={}", soapClientRespVO.getMethodName(), soapClientRespVO.getUuid());

        try {
            // 构建 SOAP 请求体
            String soapRequestBody = buildDestroySoapRequestBody(soapClientRespVO);

            // 获取实际的 WebService 端点 URL
            String endpointUrl = getWebServiceEndpoint(soapClientRespVO.getPlmWsdlUrl());

            // 使用 Hutool 发送 HTTP 请求
            cn.hutool.http.HttpResponse response = cn.hutool.http.HttpRequest.post(endpointUrl)
                    .header("Content-Type", "text/xml; charset=utf-8")
                    .header("SOAPAction", "") // 根据实际服务要求设置
                    .timeout(RECEIVE_TIMEOUT)
                    .body(soapRequestBody)
                    .execute();

            // 检查响应状态
            if (response.getStatus() >= 400) {
                log.error("PLM 接口调用失败，HTTP状态码: {}, 响应: {}", response.getStatus(), response.body());
                throw new RuntimeException("PLM 接口调用失败，HTTP状态码: " + response.getStatus());
            }
        } catch (Exception e) {
            log.error("PLM 接口调用删除接口失败，原因：{}", e.getMessage(), e);
            throw exception(new ErrorCode(500, "PLM 接口调用删除接口失败：用户ID=" + soapClientRespVO.getUuid()));
        }
    }


    // 构建 SOAP 请求体
    private String buildSoapRequestBody(SoapClientRespVO soapClientRespVO) {
        StringBuilder soapRequest = new StringBuilder();
        soapRequest.append("<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:tem=\"http://tempuri.org/\">")
                .append("<soapenv:Header/>")
                .append("<soapenv:Body>")
                .append("<tem:").append(soapClientRespVO.getMethodName()).append(">")
                .append("<tem:docid>").append(soapClientRespVO.getDocId() != null ? soapClientRespVO.getDocId() : "").append("</tem:docid>")
                .append("<tem:docver>").append(soapClientRespVO.getDocVer() != null ? soapClientRespVO.getDocVer() : "").append("</tem:docver>")
                .append("<tem:docname>").append(soapClientRespVO.getDocName() != null ? soapClientRespVO.getDocName() : "").append("</tem:docname>")
                .append("<tem:format>").append(soapClientRespVO.getFormat() != null ? soapClientRespVO.getFormat() : "").append("</tem:format>")
                .append("<tem:uuid>").append(soapClientRespVO.getUuid() != null ? soapClientRespVO.getUuid() : "").append("</tem:uuid>")
                .append("</tem:").append(soapClientRespVO.getMethodName()).append(">")
                .append("</soapenv:Body>")
                .append("</soapenv:Envelope>");
        return soapRequest.toString();
    }

    // 构建删除操作的 SOAP 请求体
    private String buildDestroySoapRequestBody(SoapClientRespVO soapClientRespVO) {
        StringBuilder soapRequest = new StringBuilder();
        soapRequest.append("<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:tem=\"http://tempuri.org/\">")
                .append("<soapenv:Header/>")
                .append("<soapenv:Body>")
                .append("<tem:").append(soapClientRespVO.getMethodName()).append(">")
                .append("<tem:uuid>").append(soapClientRespVO.getUuid() != null ? soapClientRespVO.getUuid() : "").append("</tem:uuid>")
                .append("</tem:").append(soapClientRespVO.getMethodName()).append(">")
                .append("</soapenv:Body>")
                .append("</soapenv:Envelope>");
        return soapRequest.toString();
    }

    // 提取 SOAP 响应体内容
    private String extractSoapBody(String soapResponse) {
        if (soapResponse == null || soapResponse.isEmpty()) {
            return soapResponse;
        }

        // 查找 <soap:Body> 标签
        int start = soapResponse.indexOf("<soap:Body>");
        int end = soapResponse.indexOf("</soap:Body>");

        if (start != -1 && end != -1) {
            return soapResponse.substring(start + 11, end).trim();
        }

        // 查找 <soapenv:Body> 标签
        start = soapResponse.indexOf("<soapenv:Body>");
        end = soapResponse.indexOf("</soapenv:Body>");

        if (start != -1 && end != -1) {
            return soapResponse.substring(start + 14, end).trim();
        }

        // 查找 <s:Body> 标签 (另一种常见格式)
        start = soapResponse.indexOf("<s:Body>");
        end = soapResponse.indexOf("</s:Body>");

        if (start != -1 && end != -1) {
            return soapResponse.substring(start + 8, end).trim();
        }

        // 如果没有找到 SOAP Body，返回原始响应
        return soapResponse;
    }

    // 获取 WebService 端点 URL（可能与 WSDL URL 不同）
    private String getWebServiceEndpoint(String wsdlUrl) {
        // 通常 WebService 的实际端点 URL 与 WSDL URL 不同
        // 这里需要根据实际的服务地址进行调整
        // 如果 WSDL URL 是 http://example.com/service?wsdl
        // 实际端点可能是 http://example.com/service

        if (wsdlUrl != null && wsdlUrl.endsWith("?wsdl")) {
            return wsdlUrl.substring(0, wsdlUrl.length() - 5); // 移除 ?wsdl 后缀
        }

        return wsdlUrl;
    }
    /**
     * 参数非空校验
     */
    private static void validateParam(String paramValue, String paramName) {
        if (paramValue == null || paramValue.trim().isEmpty()) {
            throw exception(new ErrorCode(500,"参数不能为空：" + paramName));
        }
    }

    // 从 SOAP 响应中提取实际的 XML 内容
    private String extractActualXmlContent(String soapResponse) throws Exception {
        if (soapResponse == null || soapResponse.isEmpty()) {
            return soapResponse;
        }

        // 使用 XPath 提取 <ns1:out> 标签中的内容
        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
        DocumentBuilder builder = factory.newDocumentBuilder();
        Document document = builder.parse(new InputSource(new StringReader(soapResponse)));
        XPath xPath = XPathFactory.newInstance().newXPath();

        // 尝试不同的命名空间前缀
        String[] xPathExpressions = {
                "//ns1:out",
                "//out",
                "//*[local-name()='out']"
        };

        for (String expression : xPathExpressions) {
            try {
                String result = xPath.evaluate(expression, document);
                if (result != null && !result.isEmpty()) {
                    // 解码 XML 实体
                    return decodeXmlEntities(result);
                }
            } catch (Exception e) {
                log.debug("尝试 XPath 表达式 {} 失败: {}", expression, e.getMessage());
            }
        }

        // 如果 XPath 方式失败，使用字符串方式提取
        return extractActualXmlContentFromString(soapResponse);
    }

    // 解码 XML 实体
    private String decodeXmlEntities(String xml) {
        if (xml == null || xml.isEmpty()) {
            return xml;
        }

        return xml.replace("&lt;", "<")
                .replace("&gt;", ">")
                .replace("&amp;", "&")
                .replace("&quot;", "\"")
                .replace("&apos;", "'");
    }

    // 使用字符串方式提取实际的 XML 内容
    private String extractActualXmlContentFromString(String soapResponse) {
        // 查找 <ns1:out> 标签
        int start = soapResponse.indexOf("<ns1:out>");
        int end = soapResponse.indexOf("</ns1:out>");

        if (start != -1 && end != -1) {
            String content = soapResponse.substring(start + 10, end);
            return decodeXmlEntities(content);
        }

        // 查找 <out> 标签
        start = soapResponse.indexOf("<out>");
        end = soapResponse.indexOf("</out>");

        if (start != -1 && end != -1) {
            String content = soapResponse.substring(start + 5, end);
            return decodeXmlEntities(content);
        }

        return soapResponse;
    }

    /**
     * 格式化XML字符串（缩进4字符，保留GBK编码，便于解析）
     */
    private static String formatXml(String rawXml) throws Exception {
        if (rawXml == null || rawXml.trim().isEmpty()) {
            return "<?xml version=\"1.0\" encoding=\"GBK\"?>\n<docpathlist></docpathlist>";
        }

        try {
            // 首先尝试解析原始 XML
            DocumentBuilderFactory docFactory = DocumentBuilderFactory.newInstance();
            DocumentBuilder docBuilder = docFactory.newDocumentBuilder();
            Document doc = docBuilder.parse(new InputSource(new StringReader(rawXml)));

            TransformerFactory transformerFactory = TransformerFactory.newInstance();
            Transformer transformer = transformerFactory.newTransformer();
            transformer.setOutputProperty(OutputKeys.INDENT, "yes");
            transformer.setOutputProperty(OutputKeys.ENCODING, "GBK"); // 适配PLM接口的GBK编码
            transformer.setOutputProperty("{http://xml.apache.org/xslt}indent-amount", "4");

            StringWriter writer = new StringWriter();
            transformer.transform(new DOMSource(doc), new StreamResult(writer));
            return writer.toString();
        } catch (Exception e) {
            // 如果解析失败，返回原始 XML（可能是已经格式化的）
            log.warn("XML 解析失败，返回原始内容: {}", e.getMessage());
            return rawXml;
        }
    }


    // 使用反射方式创建 SoapClient，避免类加载问题
    private SoapClient createSoapClient(String url) {
        try {
            // 使用反射调用 SoapClient.create() 方法
            Class<?> soapClientClass = Class.forName("cn.hutool.http.webservice.SoapClient");
            java.lang.reflect.Method createMethod = soapClientClass.getMethod("create", String.class);
            return (SoapClient) createMethod.invoke(null, url);
        } catch (Exception e) {
            log.error("创建 SoapClient 失败: {}", e.getMessage(), e);
            return null;
        }
    }

    // 使用反射方式添加参数
    private void addSoapParam(SoapClient client, String key, Object value) {
        try {
            java.lang.reflect.Method setParamMethod = client.getClass().getMethod("setParam", String.class, Object.class);
            setParamMethod.invoke(client, key, value);
        } catch (Exception e) {
            log.warn("添加 SOAP 参数失败: key={}, value={}", key, value, e);
        }
    }

    // 使用反射方式发送请求
    private String sendSoapRequest(SoapClient client) {
        try {
            java.lang.reflect.Method sendMethod = client.getClass().getMethod("send", boolean.class);
            return (String) sendMethod.invoke(client, false);
        } catch (Exception e) {
            log.error("发送 SOAP 请求失败: {}", e.getMessage(), e);
            try {
                // 尝试另一种方式
                java.lang.reflect.Method toStringMethod = client.getClass().getMethod("toString");
                return (String) toStringMethod.invoke(client);
            } catch (Exception ex) {
                log.error("发送 SOAP 请求失败（备用方式）: {}", ex.getMessage(), ex);
                return null;
            }
        }
    }


}
