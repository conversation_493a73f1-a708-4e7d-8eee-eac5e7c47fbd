package com.iaa.datacenter.module.buttjoint.controller.admin.ekuaibao.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Schema(description = "管理后台 - 订单行查询 Request VO")
@Data
public class EkuaibaoOrderItemRespVO {

    @Schema(description = "费用类型")
    private String feeTypeName;

    @Schema(description = "金额")
    private BigDecimal amount;

    @Schema(description = "核销明细")
    private String writeOffCode;
}
