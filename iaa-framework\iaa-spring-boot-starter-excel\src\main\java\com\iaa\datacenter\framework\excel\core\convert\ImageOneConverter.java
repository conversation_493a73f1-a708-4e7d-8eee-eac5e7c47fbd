package com.iaa.datacenter.framework.excel.core.convert;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.converters.WriteConverterContext;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.data.ImageData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.util.IoUtils;
import lombok.extern.slf4j.Slf4j;

import java.net.URL;
import java.util.ArrayList;
import java.util.List;

@Slf4j
public class ImageOneConverter implements Converter<String> {

    @Override
    public WriteCellData<?> convertToExcelData(WriteConverterContext<String> context) throws Exception {
        List<ImageData> imageDataList = new ArrayList<>();
            try {
                URL imageUrl = new URL(context.getValue());
                byte[] byteArray = IoUtils.toByteArray(imageUrl.openConnection().getInputStream());
                ImageData imageData = new ImageData();
                imageData.setImage(byteArray);
                imageDataList.add(imageData);
            } catch (Exception e) {
                log.error("图片加载失败，URL: {}", context.getValue(), e); // 增加URL日志以便排查
            }

        WriteCellData<?> writeCellData = new WriteCellData<>();
        if (!imageDataList.isEmpty()) {
            // 存在图片时，设置类型为IMAGE
            writeCellData.setImageDataList(imageDataList);
            // 无图片时，设置为STRING类型或忽略类型设置
            writeCellData.setType(CellDataTypeEnum.EMPTY); // 或者直接不设置类型
        }else{
            return new WriteCellData<String>("");
        }

        return writeCellData;
    }

}
