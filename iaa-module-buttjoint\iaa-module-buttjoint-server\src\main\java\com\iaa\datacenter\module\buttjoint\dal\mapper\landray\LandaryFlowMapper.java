package com.iaa.datacenter.module.buttjoint.dal.mapper.landray;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iaa.datacenter.module.buttjoint.api.ekp.dto.LandrayFlowDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
@DS("ekp")
public interface LandaryFlowMapper {

    /**
     * 分页获取流程列表
     * @param wrapper
     * @return
     */
    @Select("""
            SELECT
                km_review_main.fd_id AS flowId,
                km_review_template.fd_name AS flowTemplate,
                km_review_main.fd_number AS flowNumber,
                km_review_main.doc_subject AS flowName,
                sys_org_element.fd_name AS flowCreator,
                km_review_main.doc_create_time AS flowCreateTime,
                km_review_main.extend_data_xml as flowDataXml,
                km_review_main.doc_publish_time AS flowPublishTime,
                CASE
                    WHEN km_review_main.doc_status = '20' THEN '审批中'
                    WHEN km_review_main.doc_status = '30' THEN '审批完成'
                    WHEN km_review_main.doc_status = '31' THEN '已反馈'
                    ELSE ''
                END AS flowStatus,
                CASE
                    WHEN LEFT(km_review_template.fd_name,4) = '0601' THEN 2
                    WHEN LEFT(km_review_template.fd_name,4) = '0602' THEN 1
                    WHEN LEFT(km_review_template.fd_name,4) = '0603' THEN 2
                    WHEN LEFT(km_review_template.fd_name,4) = '0604' THEN 2
                    WHEN LEFT(km_review_template.fd_name,4) = '0605' THEN 3
                    WHEN LEFT(km_review_template.fd_name,4) = '0606' THEN 3
                    ELSE 0
                END AS flowType
            from km_review_main
            join km_review_template on fd_template_id = km_review_template.fd_id
            join sys_org_element  on km_review_main.doc_creator_id = sys_org_element.fd_id
            ${ew.getCustomSqlSegment}
            """)
//    where km_review_template.fd_category_id='17b0abf5718267facc812234e2d9f5d6'
//    and km_review_main.doc_create_time > '2025-03-31'
//    and km_review_main.doc_delete_flag = 0
//    and km_review_main.doc_status not in ('00','10','11','40')
    List<LandrayFlowDTO> selectFlowList(@Param("ew") Wrapper<LandrayFlowDTO> wrapper);
}
