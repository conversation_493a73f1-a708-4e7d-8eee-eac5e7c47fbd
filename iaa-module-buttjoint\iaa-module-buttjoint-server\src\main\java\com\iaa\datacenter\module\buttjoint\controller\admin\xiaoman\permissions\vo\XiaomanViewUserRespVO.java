package com.iaa.datacenter.module.buttjoint.controller.admin.xiaoman.permissions.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "小满可查看报表权限用户")
public class XiaomanViewUserRespVO {

    @Schema(description = "用户ID")
    private Integer id;

    @Schema(description = "用户账号")
    private String username;

    @Schema(description = "用户昵称")
    private String nickname;

}
