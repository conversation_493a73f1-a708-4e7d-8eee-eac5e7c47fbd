package com.iaa.datacenter.framework.excel.core.convert;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.converters.WriteConverterContext;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.data.ImageData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.util.IoUtils;
import lombok.extern.slf4j.Slf4j;

import java.net.URL;
import java.util.ArrayList;
import java.util.List;

import static com.google.common.io.Files.getFileExtension;

@Slf4j
public class ImageConverter implements Converter<Object> {

    @Override
    public WriteCellData<?> convertToExcelData(WriteConverterContext<Object> context) throws Exception {
        List<ImageData> imageDataList = new ArrayList<>();
        // 处理不同类型的输入值
        Object value = context.getValue();
        List<String> urlList = new ArrayList<>();
        if (value == null) {
            return new WriteCellData<>("");
        }

        if (value instanceof String) {
            // 单个URL字符串
            urlList.add((String) value);
        } else if (value instanceof List) {
            // URL字符串列表
            List<?> list = (List<?>) value;
            for (Object item : list) {
                if (item instanceof String) {
                    urlList.add((String) item);
                }
            }
        }
        for (String url : urlList) {
            try {
                URL imageUrl = new URL(url);
                byte[] byteArray = IoUtils.toByteArray(imageUrl.openConnection().getInputStream());
                ImageData imageData = new ImageData();
                imageData.setImage(byteArray);

                // 根据URL或文件内容判断图片类型
                String fileExtension = getFileExtension(url).toLowerCase();
                switch (fileExtension) {
                    case "jpg":
                    case "jpeg":
                        imageData.setImageType(ImageData.ImageType.PICTURE_TYPE_JPEG);
                        break;
                    case "png":
                        imageData.setImageType(ImageData.ImageType.PICTURE_TYPE_PNG);
                        break;
                    case "bmp":
                        imageData.setImageType(ImageData.ImageType.PICTURE_TYPE_DIB);
                        break;
                    case "gif":
                        imageData.setImageType(ImageData.ImageType.PICTURE_TYPE_WMF);
                        break;
                    default:
                        // 如果无法判断类型，可以尝试从字节流判断或设置默认类型
                        imageData.setImageType(ImageData.ImageType.PICTURE_TYPE_PNG); // 默认设置为PNG
                        break;
                }
                imageDataList.add(imageData);
            } catch (Exception e) {
                log.error("图片加载失败，URL: {}", url, e); // 增加URL日志以便排查
            }
        }
        WriteCellData<?> writeCellData = new WriteCellData<>();
        if (!imageDataList.isEmpty()) {
            // 存在图片时，设置类型为IMAGE
            writeCellData.setImageDataList(imageDataList);
            writeCellData.setType(CellDataTypeEnum.EMPTY);
//            writeCellData.setType(CellDataTypeEnum.STRING); // 或者直接不设置类型
        }else{
            return new WriteCellData<String>("");
        }

        return writeCellData;
    }

}
