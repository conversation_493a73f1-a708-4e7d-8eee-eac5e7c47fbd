package com.iaa.datacenter.module.buttjoint.job.xiaoman;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.iaa.datacenter.framework.buttjoint.constant.ButtJointNameConstants;
import com.iaa.datacenter.framework.buttjoint.service.ButtJointClientService;
import com.iaa.datacenter.framework.quartz.core.handler.JobHandler;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.xiaoman.XiaomanCustomerDO;
import com.iaa.datacenter.module.buttjoint.framework.constant.XiaomanApiConstant;
import com.iaa.datacenter.module.buttjoint.framework.factory.ButtJointClientFactory;
import com.iaa.datacenter.module.buttjoint.service.xiaoman.customer.XiaomanCustomerService;
import com.jayway.jsonpath.DocumentContext;
import com.jayway.jsonpath.JsonPath;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;

@Component
public class ReadXiaomanCustomerJob implements JobHandler {

    @Resource
    private ButtJointClientFactory buttJointClientFactory;
    @Resource
    private XiaomanCustomerService xiaomanCustomerService;

    @Override
    public String execute(String param) throws Exception {
        ButtJointClientService client = buttJointClientFactory.getClient(ButtJointNameConstants.XIAOMAN);
        HashMap<String, Object> query = new HashMap<>();
        query.put("count", 100);
        query.put("removed", 1);
        query.put("all", 0);
        query.put("end_time", DateUtil.now());
        query.put("start_time", DateUtil.offset(DateUtil.date(), DateField.HOUR_OF_DAY, -6).toString("yyyy-MM-dd HH:mm:ss"));
        int i = 1, count = 0;
        JSONArray allCustomer = new JSONArray();
        do {
            query.put("start_index", i);
            JSONObject result = client.get(XiaomanApiConstant.CUSTOMER_UPDATE, query);
            if(result.getInteger("code")==null) break;
            if (result.getInteger("code") != 200) break;
            if (count == 0) {
                count = Optional.ofNullable(result.getJSONObject("data").getInteger("totalItem")).orElse(0);
            }
            allCustomer.addAll(result.getJSONObject("data").getJSONArray("list"));
            i++;
        } while (i <= (count / 100 + ((count % 100) > 0 ? 1 : 0)));
        List<XiaomanCustomerDO> customerInfoList = new ArrayList<>();
        for (Object xiaomanCustomerDO : allCustomer) {
            DocumentContext parse = JsonPath.parse(xiaomanCustomerDO);
            XiaomanCustomerDO data = ReadCustomerById(parse.read("$.company_id"));
            if (data == null) continue;
            customerInfoList.add(data);
            if (customerInfoList.size() == 100) {
                xiaomanCustomerService.saveOrUpdateBatch(customerInfoList, true);
                customerInfoList.clear();
            }
        }
        if(customerInfoList.size()>0){
            xiaomanCustomerService.saveOrUpdateBatch(customerInfoList, true);
        }
        return "客户读取成功，共" + allCustomer.size() + "条";
    }

    /**
     * 读取单个小满客户信息
     * @param companyId
     * @return
     */
    public XiaomanCustomerDO ReadCustomerById(String companyId){
        ButtJointClientService client = buttJointClientFactory.getClient(ButtJointNameConstants.XIAOMAN);
        JSONObject result = client.get(XiaomanApiConstant.CUSTOMER_INFO, new HashMap<>() {{
            put("company_id", companyId);
            put("format", 1);
        }});
        JSONObject dataObj = result.getJSONObject("data");
        if (dataObj == null) return null;
        // 反序列化
        return dataObj.toJavaObject(XiaomanCustomerDO.class);
    }
}
