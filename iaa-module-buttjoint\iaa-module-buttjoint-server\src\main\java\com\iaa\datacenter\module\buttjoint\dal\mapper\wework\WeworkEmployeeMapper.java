package com.iaa.datacenter.module.buttjoint.dal.mapper.wework;

import com.iaa.datacenter.framework.mybatis.core.mapper.BaseMapperX;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.wework.WeworkEmployeeDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface WeworkEmployeeMapper extends BaseMapperX<WeworkEmployeeDO> {

    @Select("SELECT \n" +
            "    wework_id AS weworkId,\n" +
            "    name,\n" +
            "    TIMESTAMPDIFF(YEAR, date_of_employment, CURDATE()) AS year\n" +
            "FROM wework_employee\n" +
            "WHERE \n" +
            "    TIMESTAMPDIFF(YEAR, date_of_employment, CURDATE()) >= 1\n" +
            "    AND (\n" +
            "        -- 普通情况：月份和日期匹配\n" +
            "        (MONTH(date_of_employment) = MONTH(CURDATE()) \n" +
            "         AND DAY(date_of_employment) = DAY(CURDATE()))\n" +
            "        -- 特殊情况：2月29日的纪念日处理（非闰年时视为3月1日）\n" +
            "        OR \n" +
            "        (\n" +
            "            MONTH(date_of_employment) = 2 \n" +
            "            AND DAY(date_of_employment) = 29 \n" +
            "            AND MONTH(CURDATE()) = 3 \n" +
            "            AND DAY(CURDATE()) = 1 \n" +
            "            AND LAST_DAY(DATE_SUB(CURDATE(), INTERVAL 1 MONTH)) = '2800-02-28'\n" +
            "        )\n" +
            "    );\n")
    List<WeworkEmployeeDO> selectAnniversary();
}
