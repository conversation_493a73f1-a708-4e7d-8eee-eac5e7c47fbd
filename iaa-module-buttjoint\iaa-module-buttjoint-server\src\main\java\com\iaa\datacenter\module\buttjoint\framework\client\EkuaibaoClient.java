package com.iaa.datacenter.module.buttjoint.framework.client;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.net.url.UrlBuilder;
import cn.hutool.http.*;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.iaa.datacenter.framework.buttjoint.annotation.ClientIdentifier;
import com.iaa.datacenter.framework.buttjoint.config.ButtJointConfig;
import com.iaa.datacenter.framework.buttjoint.config.ButtJointProperties;
import com.iaa.datacenter.framework.buttjoint.constant.ButtJointNameConstants;
import com.iaa.datacenter.framework.buttjoint.dataobject.ButtJointAuthorization;
import com.iaa.datacenter.framework.buttjoint.service.ButtJointClientService;
import com.iaa.datacenter.framework.common.util.json.JsonUtils;
import com.iaa.datacenter.module.buttjoint.service.buttjointauth.ButtJointAuthService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Service
@ClientIdentifier(name=ButtJointNameConstants.EKUAIBAO)
public class EkuaibaoClient implements ButtJointClientService {
    private static final long EXPIRATION_THRESHOLD = 1000 * 60 * 10; // 10 minutes before actual expiration

    @Resource
    private ButtJointProperties buttJointProperties;
    @Resource
    private ButtJointAuthService buttJointAuthService;

    @Override
    public JSONObject auth() {

        ButtJointAuthorization auth = buttJointAuthService.getById(ButtJointNameConstants.EKUAIBAO);
        if(Objects.nonNull(auth)){
            // 判断是否过期
            if(auth.getExpireTime()-EXPIRATION_THRESHOLD>System.currentTimeMillis()){
                return auth.getAuthorization();
            }else{
                return refresh().getAuthorization();
            }
        }else{
            return refresh().getAuthorization();
        }
    }

    /**
     * 刷新token
     * @return
     */
    private ButtJointAuthorization refresh(){
        ButtJointConfig ekuaibaoConfig = buttJointProperties.getType().get(ButtJointNameConstants.EKUAIBAO);
        JSONObject token = getToken(ekuaibaoConfig);
        JSONObject tokenValue = token.getJSONObject("value");

        ButtJointAuthorization auth = ButtJointAuthorization.builder()
                .id(ButtJointNameConstants.EKUAIBAO)
                .authorization(tokenValue)
                .expireTime(tokenValue.getLong("expireTime"))
                .build();

        Boolean exist = buttJointAuthService.exist(auth.getId());
        if(exist){
            buttJointAuthService.updateById(auth);
        }else{
            buttJointAuthService.save(auth);
        }
        return auth;
    }

    /**
     * 获取授权
     * @param ekuaibaoConfig
     * @return
     */
    private JSONObject getToken(ButtJointConfig ekuaibaoConfig){
        HashMap<String, Object> params = new HashMap<>();
        params.put("appKey",ekuaibaoConfig.getAppKey());
        params.put("appSecurity",ekuaibaoConfig.getAppSecret());
        try (HttpResponse response = HttpUtil.createPost(ekuaibaoConfig.getUrl()+"v1/auth/getAccessToken")
                .contentType(ContentType.JSON.getValue())
                .body(JsonUtils.toJsonString(params))
                .execute()){
            return JsonUtils.parseObject(response.body(),JSONObject.class);
        }
    }

    @Override
    public JSONObject auth(String Code) {
        return null;
    }

    @Override
    public JSONObject readObj(String url, Method type, Map<String, Object> query, Map<String, Object> body,String ...code) {
        JSONObject auth = auth();
        query.put("accessToken",auth.getString("accessToken"));
        HttpRequest request = HttpUtil.createRequest(type, HttpUtil.urlWithFormUrlEncoded(buttJointProperties.getType().get(ButtJointNameConstants.EKUAIBAO).getUrl()+url,query, StandardCharsets.UTF_8));
        if(CollUtil.isNotEmpty(body)){
            request.contentType(ContentType.JSON.getValue()).body(JSON.toJSONString(body));
        }
        try(HttpResponse response = request.execute()){
            return JsonUtils.parseObject(response.body(),JSONObject.class);
        }
    }

    @Override
    public JSONObject readObj(String url, Method type, Map<String, Object> query, ArrayList<Object> body, String... code) {
        return null;
    }
}
