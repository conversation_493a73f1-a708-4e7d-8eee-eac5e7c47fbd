package com.iaa.datacenter.module.buttjoint.controller.admin.plm.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ExcelIgnoreUnannotated
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "管理后台 - PLM 物料分类 VO")
public class ItemCategoryRespVO {

    @Schema(description = "分类编号")
    private Long id;

    @Schema(description = "分类父编号")
    private Long parentId;

    @Schema(description = "分类名称")
    private String name;

    @Schema(description = "分类编码")
    private String code;

}
