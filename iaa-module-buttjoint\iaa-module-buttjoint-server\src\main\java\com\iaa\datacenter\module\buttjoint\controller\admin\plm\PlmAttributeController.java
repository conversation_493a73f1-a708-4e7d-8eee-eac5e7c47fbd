package com.iaa.datacenter.module.buttjoint.controller.admin.plm;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iaa.datacenter.framework.common.core.KeyValue;
import com.iaa.datacenter.framework.common.pojo.CommonResult;
import com.iaa.datacenter.framework.common.pojo.PageResult;
import com.iaa.datacenter.module.buttjoint.controller.admin.plm.utils.IpUtils;
import com.iaa.datacenter.module.buttjoint.controller.admin.plm.vo.AttributeTableColumnRespVO;
import com.iaa.datacenter.module.buttjoint.controller.admin.plm.vo.ItemCategoryRespVO;
import com.iaa.datacenter.module.buttjoint.controller.admin.plm.vo.SoapClientRespVO;
import com.iaa.datacenter.module.buttjoint.dal.mapper.plm.AttributeMapper;
import com.iaa.datacenter.module.buttjoint.service.plm.doSoap.SoapClientService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

@Tag(name = "管理后台 - PLM 属性")
@RestController
@RequestMapping("/butt-joint/plm/attribute")
@Validated
@Slf4j
public class PlmAttributeController {

    @Resource
    private AttributeMapper attributeMapper;

    @Resource
    private SoapClientService soapClientService;



    @GetMapping("/category")
    @Operation(summary = "获取物料分类")
    public CommonResult<List<ItemCategoryRespVO>> getCategoryList() {
        return CommonResult.success(attributeMapper.getItemCategoryList());
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取物料属性")
    public CommonResult<List<AttributeTableColumnRespVO>> getAttributeList(@PathVariable String id) {
        return CommonResult.success(attributeMapper.getAttributeList(id));
    }

    @PostMapping("/dict")
    @Operation(summary = "获取属性字典")
    public CommonResult<List<KeyValue<String, String>>> getAttributeDict(@RequestBody Dict queryReqVO) {
        List<AttributeTableColumnRespVO> attribute = attributeMapper.getAttributeList(queryReqVO.getStr("id"));
        StringBuilder sb = new StringBuilder();
        for (AttributeTableColumnRespVO column : attribute) {
            sb.append(column.getSourceTable()).append(".").append(column.getProp()).append(",");
        }
        if (sb.toString().endsWith(",")) {
            sb.deleteCharAt(sb.length() - 1);
        }
        QueryWrapper<?> wrapper = new QueryWrapper<>();
        if (queryReqVO.containsKey("code")) {
            wrapper.likeRight("BOM_027.PARTID", queryReqVO.getStr("code"));
        }
        wrapper.likeRight(StrUtil.isNotEmpty(queryReqVO.getStr("itemCode")), "BOM_027.PARTID", queryReqVO.getStr("itemCode"));
        wrapper.likeRight(StrUtil.isNotEmpty(queryReqVO.getStr("itemVar")), "BOM_027.PARTVAR", queryReqVO.getStr("itemVar"));
        wrapper.like(StrUtil.isNotEmpty(queryReqVO.getStr("itemName")), "BOM_027.CHINANAME", queryReqVO.getStr("itemName"));
        wrapper.like(StrUtil.isNotEmpty(queryReqVO.getStr("model")), "BOM_027.MODEL", queryReqVO.getStr("model"));
        for (AttributeTableColumnRespVO column : attribute) {
            if (!queryReqVO.containsKey(column.getProp())) {
                continue;
            }
            if (column.getType().equals("dict")) {
                ArrayList<String> list = toStringArrayList(queryReqVO.get(column.getProp()));
                if (list.contains("")){
                    wrapper.and(p->p.in(CollUtil.isNotEmpty(list), column.getSourceTable() + "." + column.getProp(), list).or().isNull(column.getSourceTable() + "." + column.getProp()));
                }else{
                    wrapper.in(CollUtil.isNotEmpty(list), column.getSourceTable() + "." + column.getProp(), list);
                }
            } else {
                wrapper.like(StrUtil.isNotEmpty(queryReqVO.getStr(column.getProp())), column.getSourceTable() + "." + column.getProp(), queryReqVO.getStr(column.getProp()));
            }
        }
        wrapper.eq("BOM_027.PARTFIXEDBACK4",1)
                .eq("BOM_027.PARTSTATE",1)
                .eq("BOM_027.INPUTERP",1);
        wrapper.orderByAsc("BOM_027.PARTID");
        Page<Map<String, String>> itemList = attributeMapper.getItemList(sb.toString(), new Page<>(queryReqVO.getLong("pageNo"), -1), wrapper);

        List<KeyValue<String, String>> result = new ArrayList<>();
        for (AttributeTableColumnRespVO column : attribute) {
            if(!column.getType().equals("dict")) continue;
            Set<String> filterList = itemList.getRecords().stream().map(map -> {
                String tempValue = map.get(column.getProp());
                return StrUtil.isEmpty(tempValue)||StrUtil.isBlank(tempValue)?"空":tempValue;
            }).collect(Collectors.toSet());
            for (String value : filterList) {
                KeyValue<String, String> item = new KeyValue<>();
                item.setKey(column.getProp());
                item.setValue(value);
                result.add(item);
            }
        }
        return CommonResult.success(result);
    }

    @PostMapping("/getDocPathOrWx")
    @Operation(summary = "获取PLM文件预览路径")
    public CommonResult<String> getDocPathOrWx(@RequestBody SoapClientRespVO soapClientRespVO, HttpServletRequest request) throws Exception {
        String clientIp = IpUtils.getClientIp(request);
        log.info("客户端IP：{}", clientIp);
        return CommonResult.success(soapClientService.getDocPathOrWx(soapClientRespVO));
    }

    @PostMapping("/destoryOrWx")
    @Operation(summary = "删除用户在PDM服务器上临时下载文件夹")
    public CommonResult<Boolean> destoryOrWx(@RequestBody SoapClientRespVO soapClientRespVO) throws Exception {
        soapClientService.destoryOrWx(soapClientRespVO);
        return CommonResult.success(true);
    }

    @PostMapping("/item-list")
    @Operation(summary = "获取属性字典")
    public CommonResult<PageResult<Map<String, String>>> getItemList(@RequestBody Dict queryReqVO) {
        List<AttributeTableColumnRespVO> attribute = attributeMapper.getAttributeList(queryReqVO.getStr("id"));
        StringBuilder sb = new StringBuilder();
        for (AttributeTableColumnRespVO column : attribute) {
            sb.append(column.getSourceTable()).append(".").append(column.getProp()).append(",");
        }
        if (sb.toString().endsWith(",")) {
            sb.deleteCharAt(sb.length() - 1);
        }
        QueryWrapper<?> wrapper = new QueryWrapper<>();
        if (queryReqVO.containsKey("code")) {
            wrapper.likeRight("BOM_027.PARTID", queryReqVO.getStr("code"));
        }
        wrapper.likeRight(StrUtil.isNotEmpty(queryReqVO.getStr("itemCode")), "BOM_027.PARTID", queryReqVO.getStr("itemCode"));
        wrapper.likeRight(StrUtil.isNotEmpty(queryReqVO.getStr("itemVar")), "BOM_027.PARTVAR", queryReqVO.getStr("itemVar"));
        wrapper.like(StrUtil.isNotEmpty(queryReqVO.getStr("itemName")), "BOM_027.CHINANAME", queryReqVO.getStr("itemName"));
        wrapper.like(StrUtil.isNotEmpty(queryReqVO.getStr("model")), "BOM_027.MODEL", queryReqVO.getStr("model"));
        for (AttributeTableColumnRespVO column : attribute) {
            if (!queryReqVO.containsKey(column.getProp())) {
                continue;
            }
            if (column.getType().equals("dict")) {
                ArrayList<String> list = toStringArrayList(queryReqVO.get(column.getProp()));
                if (list.contains("")){
                    wrapper.and(p->p.in(CollUtil.isNotEmpty(list), column.getSourceTable() + "." + column.getProp(), list).or().isNull(column.getSourceTable() + "." + column.getProp()));
                }else{
                    wrapper.in(CollUtil.isNotEmpty(list), column.getSourceTable() + "." + column.getProp(), list);
                }

            } else {
                wrapper.like(StrUtil.isNotEmpty(queryReqVO.getStr(column.getProp())), column.getSourceTable() + "." + column.getProp(), queryReqVO.getStr(column.getProp()));
            }
        }
        wrapper.eq("BOM_027.PARTFIXEDBACK4",1)
                .eq("BOM_027.PARTSTATE",1)
                .eq("BOM_027.INPUTERP",1);
        wrapper.orderByAsc("BOM_027.PARTID");
        Page<Map<String, String>> itemList = attributeMapper.getItemList(sb.toString(), new Page<>(queryReqVO.getLong("pageNo"), queryReqVO.getLong("pageSize")), wrapper);
        return CommonResult.success(new PageResult<>(itemList.getRecords(), itemList.getTotal()));
    }

    private ArrayList<String> toStringArrayList(Object obj) {
        if (Objects.isNull(obj)) return new ArrayList<>();
        try {
            // 如果已经是ArrayList<String>类型
            if (obj instanceof ArrayList<?>) {
                ArrayList<?> list = (ArrayList<?>) obj;
                return list.stream()
                        .map(item -> item == null ? "" : item.toString())
                        .collect(Collectors.toCollection(ArrayList::new));
            }

            // 如果是数组类型
            if (obj.getClass().isArray()) {
                return Arrays.stream((Object[]) obj)
                        .map(item -> item == null ? "" : item.toString())
                        .collect(Collectors.toCollection(ArrayList::new));
            }

            // 如果是Collection类型
            if (obj instanceof Collection<?>) {
                return ((Collection<?>) obj).stream()
                        .map(item -> item == null ? "" : item.toString())
                        .collect(Collectors.toCollection(ArrayList::new));
            }


            // 其他类型，将toString()结果作为单个元素
            ArrayList<String> result = new ArrayList<>();
            result.add(obj.toString());
            return result;
        } catch (Exception e) {
            log.error("Convert to ArrayList<String> failed for object: {}", obj, e);
            return new ArrayList<>();
        }
    }
}
