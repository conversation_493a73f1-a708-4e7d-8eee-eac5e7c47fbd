package com.iaa.datacenter.framework.common.util.object;

import com.iaa.datacenter.framework.common.annotation.WebTableColumn;
import com.iaa.datacenter.framework.common.enums.ColumnAlignEnum;
import com.iaa.datacenter.framework.common.util.collection.CollectionUtils;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 表格列获取工具类
 */
public class WebColumnUtils {
    /**
     * 数字类型
     */
    private static final List<Class<?>> NUMBER_CLASSES = List.of(Byte.class, Short.class, Integer.class, Long.class, Float.class, Double.class, BigDecimal.class);
    /**
     * 转换POJO对象为前端表格列
     * @param clazz
     * @return
     */
    public static List<com.iaa.datacenter.framework.common.pojo.WebTableColumn> parseTableColumn(Class<?> clazz){
        Field[] fields = clazz.getDeclaredFields();
        return CollectionUtils.convertList(fields, field -> {
            com.iaa.datacenter.framework.common.pojo.WebTableColumn column = new com.iaa.datacenter.framework.common.pojo.WebTableColumn();
            if (field.isAnnotationPresent(WebTableColumn.class)) {
                WebTableColumn tableColumn = field.getAnnotation(WebTableColumn.class);
                if (tableColumn.exists()) return null;
                column.setLabel(tableColumn.label());
                column.setWidth(tableColumn.width());
                column.setAlign(tableColumn.align());
                column.setDict(tableColumn.dict());
                column.setNoSearch(tableColumn.noSearch());
                column.setDate(tableColumn.date());
                column.setNumber(NUMBER_CLASSES.contains(field.getType()));
            } else {
                column.setLabel("请填写列名");
                column.setWidth(-1);// -1 代表自动宽度
                column.setAlign(ColumnAlignEnum.CENTER.getValue());
                column.setNoSearch(true);
            }
            column.setProp(field.getName());
            return column;
        }).stream().filter(Objects::nonNull).collect(Collectors.toList());
    }
}
