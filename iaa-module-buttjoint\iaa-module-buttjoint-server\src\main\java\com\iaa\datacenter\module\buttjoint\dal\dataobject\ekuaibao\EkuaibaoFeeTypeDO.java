package com.iaa.datacenter.module.buttjoint.dal.dataobject.ekuaibao;

import com.anwen.mongo.annotation.ID;
import com.anwen.mongo.annotation.collection.CollectionField;
import com.anwen.mongo.annotation.collection.CollectionLogic;
import com.anwen.mongo.annotation.collection.CollectionName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 合思费用类型
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@CollectionName("ekuaibao_fee_type")
public class EkuaibaoFeeTypeDO {

    @ID
    private String id;
    /**
     * 费用名称
     */
    private String name;
    /**
     * 父ID
     */
    private String parentId;
    /**
     * 父名称
     */
    @CollectionField(exist = false)
    private String parentName;
    /**
     * 是否启用
     */
    private Boolean active;
    /**
     * 编码
     */
    private String code;
}
