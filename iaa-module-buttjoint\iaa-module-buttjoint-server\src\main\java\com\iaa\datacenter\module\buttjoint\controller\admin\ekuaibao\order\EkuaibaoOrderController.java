package com.iaa.datacenter.module.buttjoint.controller.admin.ekuaibao.order;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSON;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.anwen.mongo.conditions.query.QueryWrapper;
import com.anwen.mongo.conditions.update.UpdateWrapper;
import com.iaa.datacenter.framework.buttjoint.dataobject.BaseOrderDO;
import com.iaa.datacenter.framework.common.pojo.CommonResult;
import com.iaa.datacenter.framework.common.pojo.PageResult;
import com.iaa.datacenter.framework.common.pojo.WebTableColumn;
import com.iaa.datacenter.framework.common.util.object.BeanUtils;
import com.iaa.datacenter.framework.common.util.object.WebColumnUtils;
import com.iaa.datacenter.framework.idempotent.core.annotation.Idempotent;
import com.iaa.datacenter.framework.idempotent.core.keyresolver.impl.DefaultIdempotentKeyResolver;
import com.iaa.datacenter.framework.idempotent.core.keyresolver.impl.UserIdempotentKeyResolver;
import com.iaa.datacenter.module.buttjoint.controller.admin.ekuaibao.order.vo.EkuaibaoOrderItemRespVO;
import com.iaa.datacenter.module.buttjoint.controller.admin.ekuaibao.order.vo.EkuaibaoOrderPageReqVO;
import com.iaa.datacenter.module.buttjoint.controller.admin.ekuaibao.order.vo.EkuaibaoOrderRespVO;
import com.iaa.datacenter.module.buttjoint.controller.admin.ekuaibao.subject.vo.EkuaibaoSubjectRespVO;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.ekuaibao.EkuaibaoCustomDO;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.ekuaibao.EkuaibaoOrderDO;
import com.iaa.datacenter.module.buttjoint.framework.utils.EkuaibaoFC;
import com.iaa.datacenter.module.buttjoint.job.ekuaibao.ReadOrderJob;
import com.iaa.datacenter.module.buttjoint.job.erp.EkuaibaoOrderSendErpJob;
import com.iaa.datacenter.module.buttjoint.service.ekuaibao.custom.EkuaibaoCustomService;
import com.iaa.datacenter.module.buttjoint.service.ekuaibao.order.EkuaibaoOrderService;
import com.iaa.datacenter.module.buttjoint.service.ekuaibao.staffs.EkuaibaoStaffsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

import static com.iaa.datacenter.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 合思单据显示")
@RestController
@RequestMapping("/butt-joint/ekuaibao/order")
@Validated
@Slf4j
public class EkuaibaoOrderController {
    @Resource
    private EkuaibaoOrderService ekuaibaoOrderService;
    @Resource
    private EkuaibaoCustomService ekuaibaoCustomService;
    @Resource
    private ReadOrderJob readOrderJob;
    @Resource
    private EkuaibaoOrderSendErpJob ekuaibaoOrderSendErpJob;

    @GetMapping("/get-columns")
    @Operation(summary = "获取合思单据列信息")
    @PreAuthorize("@ss.hasPermission('ekuaibao:order:list')")
    public CommonResult<List<WebTableColumn>> getColumns(){
        return success(WebColumnUtils.parseTableColumn(EkuaibaoOrderRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获取合思单据数据")
    @PreAuthorize("@ss.hasPermission('ekuaibao:order:list')")
    public CommonResult<PageResult<EkuaibaoOrderRespVO>> page(EkuaibaoOrderPageReqVO reqVO){
        QueryWrapper<EkuaibaoOrderDO> wrapper = new QueryWrapper<>();
        wrapper.eq(StrUtil.isNotEmpty(reqVO.getFormType()),EkuaibaoOrderDO::getFormType,reqVO.getFormType())
                .eq(StrUtil.isNotEmpty(reqVO.getState()),EkuaibaoOrderDO::getState,reqVO.getState())
                .eq(Objects.nonNull(reqVO.getOrderStatus()), EkuaibaoOrderDO::getOrderStatus,reqVO.getOrderStatus())
                .like(StrUtil.isNotEmpty(reqVO.getPayCode()),EkuaibaoOrderDO::getPayCode,reqVO.getPayCode())
                .like(StrUtil.isNotEmpty(reqVO.getApCode()),EkuaibaoOrderDO::getApCode,reqVO.getApCode())
                .like(StrUtil.isNotEmpty(reqVO.getVoucherCode()),EkuaibaoOrderDO::getVoucherCode,reqVO.getVoucherCode());

        wrapper.like(StrUtil.isNotEmpty(reqVO.getCode()),"form.code",reqVO.getCode());

        if(StrUtil.isNotEmpty(reqVO.getOrg())){
            EkuaibaoCustomDO one = ekuaibaoCustomService.one(new QueryWrapper<EkuaibaoCustomDO>().eq(EkuaibaoCustomDO::getDimensionId, "ID01AdKeqenyyz:法人实体").eq(EkuaibaoCustomDO::getCode, reqVO.getOrg()));
            wrapper.eq("form.u_费用主体",one.getId());
        }

        if(Objects.nonNull(reqVO.getPayDate())){
            wrapper.gte("form.payDate",reqVO.getPayDate()[0].atZone(ZoneId.systemDefault()).toInstant().getEpochSecond()*1000)
                    .lte("form.payDate",reqVO.getPayDate()[1].atZone(ZoneId.systemDefault()).toInstant().getEpochSecond()*1000);
        }
        wrapper.orderByDesc(EkuaibaoOrderDO::getUpdateTime);
        com.anwen.mongo.model.PageResult<EkuaibaoOrderDO> result = ekuaibaoOrderService.page(wrapper, reqVO.getPageNo(), reqVO.getPageSize());
        List<EkuaibaoOrderRespVO> orders=new ArrayList<>();
        result.getContentData().forEach(item->{
            EkuaibaoOrderRespVO bean = BeanUtils.toBean(item, EkuaibaoOrderRespVO.class);
            bean.setCode(item.getForm().getString("code"));
            bean.setOrg(EkuaibaoFC.getCustomCode(
                            Optional.ofNullable(item.getForm().getString("u_费用主体"))
                                    .orElse(item.getForm().getString("法人实体"))));
            bean.setPayDate(
                    Objects.nonNull(item.getForm().getLong("payDate"))
                            ?DateUtil.date(item.getForm().getLong("payDate")).toString("yyyy-MM-dd HH:mm:ss")
                            :""
            );
            bean.setCreateBy(EkuaibaoFC.getStaffName(item.getForm().getString("submitterId")));
            bean.setDeptName(EkuaibaoFC.getCustomName(item.getForm().getString("u_费用部门")));

            if (item.getForm().containsKey("details")) {
                bean.setFeeItems(new ArrayList<>());
                item.getForm().getJSONArray("details").forEach(detail -> {
                    EkuaibaoOrderItemRespVO detailObj =new EkuaibaoOrderItemRespVO();
                    List<String> fee=new ArrayList<>();
                    JSON parse = JSONUtil.parse(detail);
                    EkuaibaoFC.getFeeNames(fee, parse.getByPath("feeTypeId").toString());
                    detailObj.setFeeTypeName(String.join("\\",CollUtil.reverse(fee)));
                    detailObj.setAmount(new BigDecimal(parse.getByPath("feeTypeForm.amount.standard").toString()));
                    bean.getFeeItems().add(detailObj);
                });
            }

            if(item.getForm().containsKey("writtenOffRecords")){
                bean.setWriteOffItems(new ArrayList<>());
                item.getForm().getJSONArray("writtenOffRecords").forEach(detail -> {
                    EkuaibaoOrderItemRespVO detailObj =new EkuaibaoOrderItemRespVO();
                    JSON parse = JSONUtil.parse(detail);
                    EkuaibaoOrderDO order = ekuaibaoOrderService.getById(parse.getByPath("loanId").toString());
                    if(Objects.isNull(order)) return;
                    detailObj.setWriteOffCode(order.getForm().getString("code"));
                    detailObj.setAmount(new BigDecimal(parse.getByPath("amount").toString()));
                    bean.getWriteOffItems().add(detailObj);
                });
            }
            orders.add(bean);
        });

        return success(new PageResult<>(orders, result.getTotalSize()));
    }

    @Idempotent(timeout = 3, message = "正在获取中，请勿重复点击",keyResolver= UserIdempotentKeyResolver.class)
    @GetMapping("/update-order/{orderId}")
    @Operation(summary = "更新合思单据数据")
    @PreAuthorize("@ss.hasPermission('ekuaibao:order:list')")
    public CommonResult<Boolean> updateOrder(@PathVariable String orderId){
        return success(readOrderJob.readOneOrder(orderId));
    }

    @Idempotent(timeout = 3, message = "同步中，请勿重复点击",keyResolver= UserIdempotentKeyResolver.class)
    @GetMapping("/sync-order/{orderId}")
    @Operation(summary = "同步合思单据数据到U9")
    @PreAuthorize("@ss.hasPermission('ekuaibao:order:list')")
    public CommonResult<Boolean> syncOrder(@PathVariable String orderId) throws Exception {
        ekuaibaoOrderSendErpJob.execute(orderId);
        return success(true);
    }

    @Idempotent(timeout = 3, message = "下载中，请勿重复点击",keyResolver= UserIdempotentKeyResolver.class)
    @GetMapping("/download-order/{code}")
    @Operation(summary = "同步合思单据数据到U9")
    @PreAuthorize("@ss.hasPermission('ekuaibao:order:list')")
    public CommonResult<Boolean> downloadOrder(@PathVariable String code){
        Boolean flag = readOrderJob.readOrderByCode(code);
        return success(flag);
    }

    @GetMapping("/un-sync-order/{orderId}")
    @Operation(summary = "不同步合思单据数据到U9")
    @PreAuthorize("@ss.hasPermission('ekuaibao:order:list')")
    public CommonResult<Boolean> unSyncOrder(@PathVariable String orderId){
        return success(ekuaibaoOrderService.update(new UpdateWrapper<EkuaibaoOrderDO>().eq(EkuaibaoOrderDO::getId,orderId).set(EkuaibaoOrderDO::getOrderStatus,3)));
    }

}
