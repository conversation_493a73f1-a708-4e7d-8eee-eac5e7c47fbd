package com.iaa.datacenter.module.buttjoint.dal.mapper.erp;

import cn.hutool.core.date.DateTime;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.erp.ErpApproveMessageDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DS("erp")
@Mapper
public interface ErpApproveMessageMapper {

    /**
     * 查询ERP审核消息列表
     *
     * @param id
     * @return
     */
    ErpApproveMessageDO getById(@Param("id") Integer id);

    /**
     * 获取所有的ERP审核消息
     * @return
     */
    List<ErpApproveMessageDO> list(@Param("startTime")String startTime,@Param("endTime")String endTime);
}
