package com.iaa.datacenter.module.buttjoint.controller.admin.xiaoman.report.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.iaa.datacenter.framework.common.core.KeyValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;
import java.util.Map;
import java.util.List;

@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@ExcelIgnoreUnannotated
@Schema(description = "管理后台 - 小满滚动订单表")
public class XiaomanOrderRespVO extends XiaomanCustomerBaseInfoVO{

    @ExcelProperty("近三年销售额")
    @Schema(description = "近三年销售额")
    private Map<String,Double> yearOrderMap;

    @ExcelProperty("竟对情况")
    @Schema(description = "竟对情况")
    private String corrival;

//    @ExcelProperty("单价")
//    @Schema(description = "单价")
//    private Double unitPrice;

    @ExcelProperty("预测2024年销售额")
    @Schema(description = "预测2024年销售额")
    private Double tztfourPredictionSales;

    @ExcelProperty("2024年达标情况")
    @Schema(description = "2024年达标情况")
    private Double tztfourReachingStandard;

    @ExcelProperty("预测2025年销售额")
    @Schema(description = "预测2025年销售额")
    private Double tztfivePredictionSales;

    @ExcelProperty("2025年达标情况")
    @Schema(description = "2025年达标情况")
    private Double tztfiveReachingStandard;

    @Schema(description = "实际销售情况，key ：月份")
    Map<String,XiaomanOrderTotalVO> orderTotalList;

    @Data
    @Builder
    @Schema(description = "管理后台 小满客户ERP统计信息表")
    public static class XiaomanOrderTotalVO {

        @ExcelProperty("预测销量")
        @Schema(description = "预测销量")
        private Integer forecastQty;

        @ExcelProperty("预测金额")
        @Schema(description = "预测金额")
        private Double forecastAmount;

        @ExcelProperty("第一周销售额")
        @Schema(description = "第一周销售额")
        private Double oneWeekAmount;

        @ExcelProperty("第二周销售额")
        @Schema(description = "第二周销售额")
        private Double twoWeekAmount;

        @ExcelProperty("第三周销售额")
        @Schema(description = "第三周销售额")
        private Double threeWeekAmount;

        @ExcelProperty("第四周销售额")
        @Schema(description = "第四周销售额")
        private Double fourWeekAmount;

        @ExcelProperty("总销量")
        @Schema(description = "总销量")
        private Integer totalQty;

        @ExcelProperty("总销售额")
        @Schema(description = "总销售额")
        private Double totalAmount;
    }

    @ExcelProperty("回款信息")
    @Schema(description = "回款信息 key月份")
    private Map<String,Double> paymentMap;
}
