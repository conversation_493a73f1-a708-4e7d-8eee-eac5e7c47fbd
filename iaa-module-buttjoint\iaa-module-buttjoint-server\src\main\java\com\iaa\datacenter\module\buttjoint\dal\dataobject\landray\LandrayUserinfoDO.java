package com.iaa.datacenter.module.buttjoint.dal.dataobject.landray;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LandrayUserinfoDO {
    //id
    private String id;
    //性别 F：女 M：男
    private String sex;
    //编号
    private String no;
    //姓名
    private String name;
    //手机号
    private String mobileNo;
    //登录名
    private String loginName;
    //是否离职
    private Boolean isAvailable;
    //父部门
    private String parent;
    //岗位信息
    private List<String> posts;
    //当前部门领导
    private String thisLeader;
}
