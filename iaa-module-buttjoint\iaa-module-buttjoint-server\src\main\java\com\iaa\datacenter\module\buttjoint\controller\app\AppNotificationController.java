package com.iaa.datacenter.module.buttjoint.controller.app;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.iaa.datacenter.framework.buttjoint.constant.ButtJointNameConstants;
import com.iaa.datacenter.framework.buttjoint.service.ButtJointClientService;
import com.iaa.datacenter.module.buttjoint.controller.app.vo.EkuaibaoPayeeSaveReqVO;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.ekuaibao.EkuaibaoApproveMessageDO;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.ekuaibao.EkuaibaoRepaymentDO;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.erp.ErpApproveMessageDO;
import com.iaa.datacenter.module.buttjoint.dal.mapper.erp.ErpApproveMessageMapper;
import com.iaa.datacenter.module.buttjoint.framework.constant.EkuaibaoApiConstant;
import com.iaa.datacenter.module.buttjoint.framework.factory.ButtJointClientFactory;
import com.iaa.datacenter.module.buttjoint.service.ekuaibao.message.EkuaibaoApproveMessageService;
import com.iaa.datacenter.module.buttjoint.service.ekuaibao.repayment.EkuaibaoRepaymentService;
import com.iaa.datacenter.module.buttjoint.service.erp.approve.ErpApproveMessageService;
import com.iaa.datacenter.module.buttjoint.service.landray.todo.LandrayTodoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import org.springframework.context.annotation.Lazy;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.HashMap;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

@Tag(name = "对接消息 App - 通知")
@RestController
@RequestMapping("/butt-joint/notification")
@Validated
public class AppNotificationController {

    @Resource
    private EkuaibaoApproveMessageService ekuaibaoApproveMessageService;
    @Resource
    private LandrayTodoService landrayTodoService;
    @Resource
    @Lazy
    private ButtJointClientFactory buttJointClientFactory;
    @Resource
    private EkuaibaoRepaymentService ekuaibaoRepaymentService;
    @Resource
    private ErpApproveMessageMapper erpApproveMessageMapper;
    @Resource
    private ErpApproveMessageService erpApproveMessageService;

    @PostMapping("/ekuaibao/approve")
    @Operation(summary = "合思审批通知")
    @Parameter(name = "approve", description = "合思审批通知", required = true)
    @PermitAll
    public String approve(@RequestBody EkuaibaoApproveMessageDO messageDO){
        if(Objects.isNull(messageDO)){
            throw new RuntimeException("参数错误");
        }
        if(!messageDO.getCorporationId().equals("ID01AdKeqenyyz")){
            throw new RuntimeException("公司校验不正确");
        }
        if(StrUtil.isEmpty(messageDO.getUrgentApproval())){
            messageDO.setUrgentApproval("false");
        }
        EkuaibaoApproveMessageDO ekuaibaoApproveMessageDO = landrayTodoService.sendToDo(messageDO);
        boolean flag = ekuaibaoApproveMessageService.saveOrUpdate(ekuaibaoApproveMessageDO, true);
        return String.format("{\"code\":\"%d\"}", flag ? 1 : 0);
    }
    @PostMapping("/ekuaibao/payee")
    @Operation(summary = "合思还款通知")
    @Parameter(name = "payee", description = "合思还款通知", required = true)
    @PermitAll
    public CompletableFuture<String> payee(@RequestBody EkuaibaoPayeeSaveReqVO payee){
        if(!payee.getCorporationId().equals("ID01AdKeqenyyz")){
            throw new RuntimeException("公司校验不正确");
        }
        // 立即返回HTTP响应，表示已接收到通知
        CompletableFuture<String> responseFuture = new CompletableFuture<>();
        responseFuture.complete("{\"code\":\"1\"}");
        // 启动状态检查循环
        //开始读取单据
        ButtJointClientService ekaibao = buttJointClientFactory.getClient(ButtJointNameConstants.EKUAIBAO);
        CompletableFuture<Void> checkStatusAndReadDocument = CompletableFuture.runAsync(() -> {
            int count = 0;
            while (true) {
                try {
                    if(count>=4)break;
                    JSONObject object = ekaibao.get(EkuaibaoApiConstant.REPAYMENT_RECORD + List.of(payee.getRecordId()),new HashMap<>());
                    JSONArray items = object.getJSONArray("items");
                    if(items.size()==0){
                        ThreadUtil.safeSleep(1000);
                        count++;
                        continue;
                    }
                    JSONObject repayment=items.getJSONObject(0);
                    EkuaibaoRepaymentDO ekuaibaoRepayment = repayment.toJavaObject(EkuaibaoRepaymentDO.class);
                    ekuaibaoRepayment.setCurrentId(payee.getRecordId()+ekuaibaoRepayment.getChargeAgainstMark());
                    ekuaibaoRepayment.setRecordId(payee.getRecordId());
                    ekuaibaoRepayment.setOrderId(payee.getFlowId());
                    ekuaibaoRepayment.setOrderStatus(0);
                    ekuaibaoRepayment.setAccountInfo(payee.getAccountInfo());
                    ekuaibaoRepaymentService.saveOrUpdate(ekuaibaoRepayment,true);
                    break;
                } catch (RuntimeException e) {
                    // 处理异常，可能需要记录日志或重试
                    break;
                }
            }
        });

        // 返回HTTP响应，不会等待状态检查和读取单据的逻辑完成
        return responseFuture;
    }

    /**
     * todo 注意，该接口被ERP数据库触发调用，详见U9C 数据库103 存储过程 HttpGetRequest 及 Cust_WorkflowMessage表的触发器
     * @param id
     * @param isApproved
     * @return
     */
    @GetMapping("/erp/approve/{id}/{isApproved}")
    @Operation(summary = "ERP审批通知")
    @Parameter(name = "approve", description = "ERP审批通知", required = true)
    @PermitAll
    public String approve(@PathVariable Integer id,@PathVariable Boolean isApproved){
        CompletableFuture<Void> checkStatusAndReadDocument = CompletableFuture.runAsync(() -> {
            int count = 0;
            while (true) {
                try {
                    if(count>=4)break;
                    ErpApproveMessageDO message = erpApproveMessageMapper.getById(id);
                    if(Objects.isNull(message)){
                        ThreadUtil.safeSleep(1000);
                        count++;
                        continue;
                    }
                    ErpApproveMessageDO result = landrayTodoService.sendToDo(message);
                    erpApproveMessageService.saveOrUpdate(result, true);
                    break;
                }catch (RuntimeException e) {
                    // 处理异常，可能需要记录日志或重试
                    break;
                }
            }
        });
        return "{\"code\":\"1\"}";
    }
}
