package com.iaa.datacenter.module.buttjoint.dal.mapper.erp;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.erp.ErpItemShipDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;

/**
 * ERP 出库 Mapper
 */
@DS("erp")
@Mapper
public interface ErpShipMapper {

    /**
     * 查询各客户出货top 5 统计
     * @param timestamp 时间范围
     * @param type 1：按香氛机 2：按精油
     * @param customers 客户集合
     * @return
     */
    @Transactional
    List<ErpItemShipDO> getItemShip(@Param("timestamp") String[] timestamp,
                                    @Param("type") Integer type,
                                    @Param("customers") Collection<String> customers,
                                    @Param("model") String model);
}
