package com.iaa.datacenter.module.buttjoint.dal.dataobject.ekuaibao;

import com.anwen.mongo.annotation.ID;
import com.anwen.mongo.annotation.collection.CollectionField;
import com.anwen.mongo.annotation.collection.CollectionName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@CollectionName("ekuaibao_subject")
public class EkuaibaoSubjectDO {
    @ID
    private String id;

    private String name;

    private String managementCode;

    private String salesCode;

    private String manufactureCode;

    private String developmentCode;

    private String type;

    @CollectionField(exist = false)
    private String code;
}
