package com.iaa.datacenter.module.buttjoint.controller.admin.ekuaibao.order.vo;

import com.iaa.datacenter.framework.common.annotation.WebTableColumn;
import com.iaa.datacenter.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.iaa.datacenter.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 订单查询 Request VO")
@Data
public class EkuaibaoOrderPageReqVO extends PageParam {

    @Schema(description = "单据编码")
    private String code;

    @Schema(description = "单据类型")
    private String formType;

    @Schema(description = "单据状态")
    private String state;

    @Schema(description = "同步状态")
    private Integer orderStatus;

    @Schema(description = "组织")
    private String org;

    @Schema(description = "支付时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] payDate;

    @Schema(description = "付款单号")
    private String payCode;

    @Schema(description = "费用报销发票")
    private String apCode;

    @Schema(description = "凭证编号")
    private String voucherCode;

    @Schema(description = "同步消息")
    private String errorMsg;
    
}
