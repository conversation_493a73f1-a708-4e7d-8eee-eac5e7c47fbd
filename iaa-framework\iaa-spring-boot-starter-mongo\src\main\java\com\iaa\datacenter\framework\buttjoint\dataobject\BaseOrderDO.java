package com.iaa.datacenter.framework.buttjoint.dataobject;

import com.anwen.mongo.annotation.collection.CollectionField;
import com.anwen.mongo.enums.FieldFill;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 单据基础对象
 */
@Data
public abstract class BaseOrderDO {

    /**
     * 订单状态
     */
    @CollectionField(fill = FieldFill.INSERT)
    private Integer orderStatus=0;

    /**
     * 错误消息
     */
    private String errorMsg;

    /**
     * 更新时间
     */
    @CollectionField(fill = FieldFill.INSERT_UPDATE)
    private Date dateTime;
}
