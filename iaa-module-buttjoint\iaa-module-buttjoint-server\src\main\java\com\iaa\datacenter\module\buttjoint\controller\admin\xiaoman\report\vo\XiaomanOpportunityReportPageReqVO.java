package com.iaa.datacenter.module.buttjoint.controller.admin.xiaoman.report.vo;

import com.iaa.datacenter.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Data
public class XiaomanOpportunityReportPageReqVO extends PageParam {

    @Schema(description = "客户更新时间")
    @NotNull(message = "客户更新时间范围不能为空")
    @NotEmpty(message = "客户更新时间范围不能为空")
    private String[] customerTime;

    @Schema(description = "销售时间")
    @NotNull(message = "销售数据时间范围不能为空")
    @NotEmpty(message = "销售数据时间范围不能为空")
    private String[] salesTime;

    @Schema(description = "业务员")
    private List<String> salesperson;

    @Schema(description = "组别")
    private List<Long> departmentName;

    @Schema(description = "客户名称")
    private String customerName;

    @Schema(description = "国家/地区")
    private String countryName;

    @Schema(description = "洲/省")
    private String regionOrProvince;

    @Schema(description = "城市")
    private String city;

    @Schema(description = "客户画像")
    private List<String> customerPortraits;

    @Schema(description = "客户性质")
    private String nature;

    @Schema(description = "客户官网")
    private String homepage;

    @Schema(description = "客户营收规模评估")
    private List<String> revenueScale;

    @Schema(description = "香氛产品规模评估")
    private List<String> fragranceRevenueScale;

    @Schema(description = "竟对机器描述")
    private String untoMachineRemark;

    @Schema(description = "竟对精油描述")
    private String untoEssentialOilRemark;

    @Schema(description = "相关性描述")
    private String relevantProductRemark;


    @Schema(description = "机器型号")
    private String machine;

    @Schema(description = "香型")
    private String oil;
}

