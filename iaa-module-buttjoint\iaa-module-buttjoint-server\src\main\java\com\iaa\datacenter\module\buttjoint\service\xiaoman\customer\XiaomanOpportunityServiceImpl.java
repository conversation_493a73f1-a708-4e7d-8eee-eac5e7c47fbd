package com.iaa.datacenter.module.buttjoint.service.xiaoman.customer;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.anwen.mongo.conditions.query.QueryChainWrapper;
import com.anwen.mongo.conditions.query.QueryWrapper;
import com.anwen.mongo.service.impl.ServiceImpl;
import com.iaa.datacenter.framework.common.pojo.PageResult;
import com.iaa.datacenter.framework.common.util.collection.CollectionUtils;
import com.iaa.datacenter.framework.web.core.util.WebFrameworkUtils;
import com.iaa.datacenter.module.buttjoint.controller.admin.xiaoman.report.vo.XiaomanCustomerDevelopmentPageReqVO;
import com.iaa.datacenter.module.buttjoint.controller.admin.xiaoman.report.vo.XiaomanCustomerDevelopmentVO;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.xiaoman.XiaomanCustomerDO;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.xiaoman.XiaomanOpportunityDO;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.xiaoman.XiaomanUserDO;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.xiaoman.XiaomanViewAuthorityDO;
import com.iaa.datacenter.module.buttjoint.job.xiaoman.ReadXiaomanCustomerJob;
import com.iaa.datacenter.module.buttjoint.service.xiaoman.user.XiaomanUserService;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class XiaomanOpportunityServiceImpl extends ServiceImpl<XiaomanOpportunityDO> implements XiaomanOpportunityService {

    @Resource
    private XiaomanCustomerService xiaomanCustomerService;

    @Resource
    private XiaomanUserService xiaomanUserService;

    @Resource
    @Lazy
    private ReadXiaomanCustomerJob readXiaomanCustomerJob;

    @Resource
    private XiaomanViewAuthorityService xiaomanViewAuthorityService;

    @Override
    public PageResult<XiaomanCustomerDevelopmentVO> getCustomerDevelopmentPage(XiaomanCustomerDevelopmentPageReqVO pageReqVO) {
        // 分页查询商机数据
        QueryWrapper<XiaomanOpportunityDO> wrapper = new QueryWrapper<>();
        if (CollUtil.isEmpty(pageReqVO.getSalesperson())&&CollUtil.isEmpty(pageReqVO.getDepartmentName())&&WebFrameworkUtils.getLoginUserId() != 1) {
            XiaomanViewAuthorityDO authority = xiaomanViewAuthorityService.getById(WebFrameworkUtils.getLoginUserId());
            if (authority == null) {
                return new PageResult<>(new ArrayList<>(), 0L);
            }
            wrapper.in(XiaomanOpportunityDO::getUser_id, authority.getXiaoman_user_ids().stream()
                    .map(Long::parseLong)
                    .collect(Collectors.toList()));
        }
        wrapper.in(CollUtil.isNotEmpty(pageReqVO.getSalesperson()), XiaomanOpportunityDO::getUser_id, pageReqVO.getSalesperson());
        if (CollUtil.isNotEmpty(pageReqVO.getDepartmentName())) {
            List<XiaomanUserDO> list = xiaomanUserService.list(new QueryWrapper<XiaomanUserDO>().in(XiaomanUserDO::getDepartment_id, pageReqVO.getDepartmentName()));
            wrapper.in(XiaomanOpportunityDO::getUser_id, list.stream().map(user -> Long.valueOf(user.getUser_id())).collect(Collectors.toSet()));
        }
        wrapper.like(StrUtil.isNotEmpty(pageReqVO.getCustomerName()), XiaomanOpportunityDO::getName, pageReqVO.getCustomerName());
        if (StrUtil.isNotEmpty(pageReqVO.getCountryName())) {
            List<XiaomanCustomerDO> list = xiaomanCustomerService.list(new QueryWrapper<XiaomanCustomerDO>().like(XiaomanCustomerDO::getCountry_name, pageReqVO.getCountryName()));
            wrapper.in(XiaomanOpportunityDO::getCompany_id, list.stream().map(XiaomanCustomerDO::getCompany_id).collect(Collectors.toSet()));
        }
        if (StrUtil.isNotEmpty(pageReqVO.getRegionOrProvince())) {
            List<XiaomanCustomerDO> list = xiaomanCustomerService.list(new QueryWrapper<XiaomanCustomerDO>().or(
                    p -> p.like(XiaomanCustomerDO::getRegion, pageReqVO.getRegionOrProvince()).like(XiaomanCustomerDO::getProvince, pageReqVO.getRegionOrProvince())
            ));
            wrapper.in(XiaomanOpportunityDO::getCompany_id, list.stream().map(XiaomanCustomerDO::getCompany_id).collect(Collectors.toSet()));
        }
        if (StrUtil.isNotEmpty(pageReqVO.getCity())) {
            List<XiaomanCustomerDO> list = xiaomanCustomerService.list(new QueryWrapper<XiaomanCustomerDO>().like(XiaomanCustomerDO::getCity, pageReqVO.getCity()));
            wrapper.in(XiaomanOpportunityDO::getCompany_id, list.stream().map(XiaomanCustomerDO::getCompany_id).collect(Collectors.toSet()));
        }
        if (CollUtil.isNotEmpty(pageReqVO.getCustomerPortraits())) {
            List<XiaomanCustomerDO> list = xiaomanCustomerService.list(new QueryWrapper<XiaomanCustomerDO>().in(XiaomanCustomerDO::getCustomer_portraits, pageReqVO.getCustomerPortraits()));
            wrapper.in(XiaomanOpportunityDO::getCompany_id, list.stream().map(XiaomanCustomerDO::getCompany_id).collect(Collectors.toSet()));
        }
        if (StrUtil.isNotEmpty(pageReqVO.getNature())) {
            List<XiaomanCustomerDO> list = xiaomanCustomerService.list(new QueryWrapper<XiaomanCustomerDO>().like(XiaomanCustomerDO::getNature, pageReqVO.getNature()));
            wrapper.in(XiaomanOpportunityDO::getCompany_id, list.stream().map(XiaomanCustomerDO::getCompany_id).collect(Collectors.toSet()));
        }
        if (StrUtil.isNotEmpty(pageReqVO.getHomepage())) {
            List<XiaomanCustomerDO> list = xiaomanCustomerService.list(new QueryWrapper<XiaomanCustomerDO>().like(XiaomanCustomerDO::getHomepage, pageReqVO.getHomepage()));
            wrapper.in(XiaomanOpportunityDO::getCompany_id, list.stream().map(XiaomanCustomerDO::getCompany_id).collect(Collectors.toSet()));
        }
        if (CollUtil.isNotEmpty(pageReqVO.getRevenueScale())) {
            List<XiaomanCustomerDO> list = xiaomanCustomerService.list(new QueryWrapper<XiaomanCustomerDO>().in(XiaomanCustomerDO::getRevenue_scale, pageReqVO.getRevenueScale()));
            wrapper.in(XiaomanOpportunityDO::getCompany_id, list.stream().map(XiaomanCustomerDO::getCompany_id).collect(Collectors.toSet()));
        }
        if (CollUtil.isNotEmpty(pageReqVO.getFragranceRevenueScale())) {
            List<XiaomanCustomerDO> list = xiaomanCustomerService.list(new QueryWrapper<XiaomanCustomerDO>().in(XiaomanCustomerDO::getFragrance_revenue_scale, pageReqVO.getFragranceRevenueScale()));
            wrapper.in(XiaomanOpportunityDO::getCompany_id, list.stream().map(XiaomanCustomerDO::getCompany_id).collect(Collectors.toSet()));
        }
        if(CollUtil.isNotEmpty(pageReqVO.getTrailStatus())){
            List<XiaomanCustomerDO> list = xiaomanCustomerService.list(new QueryWrapper<XiaomanCustomerDO>().in(XiaomanCustomerDO::getTrail_status, pageReqVO.getTrailStatus()));
            wrapper.in(XiaomanOpportunityDO::getCompany_id, list.stream().map(XiaomanCustomerDO::getCompany_id).collect(Collectors.toSet()));
        }
        wrapper.in(CollUtil.isNotEmpty(pageReqVO.getDevelopmentType()), "external_field_data.**************", pageReqVO.getDevelopmentType());
        wrapper.in(CollUtil.isNotEmpty(pageReqVO.getStageInfo()), "stage_info.name", pageReqVO.getStageInfo());
        if (StrUtil.isNotEmpty(pageReqVO.getCombatProduct())) {
            wrapper.or(
                    p -> p.like("external_field_data.**************", pageReqVO.getCombatProduct()).like(XiaomanOpportunityDO::getRemark, pageReqVO.getCombatProduct())
            );
        }
        wrapper.between(XiaomanOpportunityDO::getCreate_time, pageReqVO.getOpportunityTime()[0], pageReqVO.getOpportunityTime()[1], true);
        wrapper.orderByDesc(XiaomanOpportunityDO::getUpdate_time);
        com.anwen.mongo.model.PageResult<XiaomanOpportunityDO> page = new com.anwen.mongo.model.PageResult<>();
        if(pageReqVO.getPageSize()==-1){
            List<XiaomanOpportunityDO> list = list(wrapper);
            page.setContentData(list);
            page.setTotalSize(list.size());
        }else{
            page = super.page(wrapper, pageReqVO.getPageNo(), pageReqVO.getPageSize());
        }
        // 查询商机对应的客户信息
        Set<String> companyIds = page.getContentData().stream().map(XiaomanOpportunityDO::getCompany_id).collect(Collectors.toSet());
        List<XiaomanCustomerDO> customerList = xiaomanCustomerService.getByIds(companyIds);
        Map<String, XiaomanCustomerDO> customerMap = customerList.stream().collect(Collectors.toMap(XiaomanCustomerDO::getCompany_id, p -> p));
        //获取用户数据
        Map<String, XiaomanUserDO> userNameMap = getUserNameMap(page.getContentData());
        // 拼接数据
        List<XiaomanCustomerDevelopmentVO> list = page.getContentData().stream().map(
                item -> buildCustomerDevelopmentVO(item, customerMap.get(item.getCompany_id()), userNameMap)
        ).toList();
        return new PageResult<>(list, page.getTotalSize());
    }

    private XiaomanCustomerDevelopmentVO buildCustomerDevelopmentVO(
            XiaomanOpportunityDO opportunityDO,
            XiaomanCustomerDO customerDO,
            Map<String, XiaomanUserDO> userNameMap) {
        if (customerDO == null) {
            XiaomanCustomerDO xiaomanCustomerDO = readXiaomanCustomerJob.ReadCustomerById(opportunityDO.getCompany_id());
            customerDO = xiaomanCustomerDO;
            xiaomanCustomerService.saveOrUpdate(xiaomanCustomerDO, true);
        }
        List<XiaomanUserDO> users = CollectionUtils.convertList(opportunityDO.getUser_id(), item -> userNameMap.getOrDefault(item.toString(), new XiaomanUserDO()));
        XiaomanCustomerDevelopmentVO result = XiaomanCustomerDevelopmentVO.builder()
                .salesperson(users.stream().map(XiaomanUserDO::getChinese_name).toList())
                .departmentName(CollUtil.isEmpty(users) ? "" : users.get(0).getDepartment_name())
                .customerName(opportunityDO.getName())
                .countryName(customerDO.getCountry_name())
                .regionOrProvince(Optional.ofNullable(customerDO.getProvince()).orElse(customerDO.getRegion()))
                .city(customerDO.getCity())
                .customerPortraits(customerDO.getCustomer_portraits())
                .nature(customerDO.getNature())
                .trailStatus(customerDO.getTrail_status())
                .homepage(customerDO.getHomepage())
                .revenueScale(customerDO.getRevenue_scale())
                .fragranceRevenueScale(customerDO.getFragrance_revenue_scale())
                .estimatedAnnualSales(customerDO.getEstimated_annual_sales())
                .planDevelopmentDays(DateUtil.betweenDay(DateUtil.parseDate(opportunityDO.getCreate_time()), DateUtil.parseDate(opportunityDO.getAccount_date()), true))
                .createTime(opportunityDO.getCreate_time())
                .progress(0)
                .stageInfo(opportunityDO.getStage_info().getString("name"))
                .failTypeName(opportunityDO.getFail_type_name())
                .failRemark(opportunityDO.getFail_remark())
                .build();

        if (JSON.toJSONString(opportunityDO.getExternal_field_data()).startsWith("{")) {
            JSONObject jsonObject = JSONObject.parseObject(JSON.toJSONString(opportunityDO.getExternal_field_data()));
            result.setCombatProduct(StrUtil.isEmpty(jsonObject.getString("**************")) ? opportunityDO.getRemark() : jsonObject.getString("**************"));
            result.setDevelopmentType(jsonObject.getString("**************"));
            result.setDevelopmentTarget(jsonObject.getString("**************"));
        }
        if (List.of("赢单", "输单").contains(opportunityDO.getStage_type_name())) {
            result.setResult(opportunityDO.getStage_type_name());
            result.setAccountDate(opportunityDO.getTrail_time());
            result.setAccountDays(DateUtil.betweenDay(DateUtil.parseDate(opportunityDO.getCreate_time()), DateUtil.parseDate(opportunityDO.getTrail_time()), true));
            result.setDevelopmentDays(DateUtil.betweenDay(DateUtil.parseDate(opportunityDO.getCreate_time()), DateUtil.parseDate(result.getAccountDate()), true));
            result.setProgress(100);
            result.setHasOverdue(result.getAccountDays() > result.getPlanDevelopmentDays());
        } else {
            result.setDevelopmentDays(DateUtil.betweenDay(DateUtil.parseDate(opportunityDO.getCreate_time()), DateUtil.date(), true));
            int divisor = result.getPlanDevelopmentDays().intValue();
            if (divisor == 0) {
                divisor = 1;
            }
            int progress = (int) ((1.0 * result.getDevelopmentDays().intValue() / divisor) * 100);
            result.setProgress(Math.min(progress, 99));
            result.setHasOverdue(result.getDevelopmentDays() > result.getPlanDevelopmentDays());
        }
        return result;
    }

    public Map<String, XiaomanUserDO> getUserNameMap(List<XiaomanOpportunityDO> dataList) {
        Set<String> userIds = dataList.stream()
                .flatMap(opportunityDO -> {
                    JSONArray userIdArray = opportunityDO.getUser_id();
                    return userIdArray.stream(); // 将 JSONArray 转换为 List，再转换为 Stream
                })
                .map(Object::toString) // 将 Object 转换为 String
                .collect(Collectors.toSet()); // 收集到 Set 中进行去重
        List<XiaomanUserDO> userList = xiaomanUserService.list(new QueryWrapper<XiaomanUserDO>().in(XiaomanUserDO::getUser_id, userIds));
        return userList.stream().collect(Collectors.toMap(XiaomanUserDO::getUser_id, p -> p));
    }
}
