<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.iaa.datacenter</groupId>
        <artifactId>iaa-framework</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>iaa-spring-boot-starter-mongo</artifactId>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <!-- Web 相关 -->
        <dependency>
            <groupId>com.iaa.datacenter</groupId>
            <artifactId>iaa-spring-boot-starter-web</artifactId>
            <scope>provided</scope> <!-- 设置为 provided，只有 OncePerRequestFilter 使用到 -->
        </dependency>

        <dependency>
            <groupId>com.gitee.anwena</groupId>
            <artifactId>mongo-plus-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jayway.jsonpath</groupId>
            <artifactId>json-path</artifactId>
        </dependency>
    </dependencies>
</project>
