package com.iaa.datacenter.module.buttjoint.dal.mapper.erp;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.plm.PlmBomTopDO;
import lombok.Data;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
@DS("erp")
public interface ErpBomMapper {

    @Select("""
            select
                concat(parentCode,'-',childCode,'-',level)
            from dbo.GetBOMTreeByParentCodeWithFilter(#{itemCode}, 1002211090000331, 10,0)
            order by concat(parentCode,'-',childCode,'-',level)
            """)
    List<String> selectBomItemList(String itemCode);

    @Select("""
            select
                CBO_ItemMaster.Code as item_code,
                iif(CBO_BOMMaster.DescFlexField_PrivateDescSeg1='True','是','否') as packaging_complete,
                iif(CBO_BOMMaster.DescFlexField_PrivateDescSeg2='True','是','否') as panel_complete,
                iif(CBO_BOMMaster.DescFlexField_PrivateDescSeg3='True','是','否') as program_complete,
                iif(CBO_BOMMaster.DescFlexField_PrivateDescSeg4='True','是','否') as other_complete
            from CBO_BOMMaster
            join CBO_ItemMaster on CBO_BOMMaster.ItemMaster = CBO_ItemMaster.ID
            where CBO_BOMMaster.Org=1002211090000331 and CBO_ItemMaster.Code=#{itemCode}
            """)
    PlmBomTopDO selectBomTop(String itemCode);
}
