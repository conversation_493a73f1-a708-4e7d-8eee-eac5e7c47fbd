package com.iaa.datacenter.module.buttjoint.api.ekp.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

@Data
public class LandrayFlowDTO {
    /**
     * 流程ID
     */
    private String flowId;
    /**
     * 流程模板
     */
    private String flowTemplate;
    /**
     * 流程编码
     */
    private String flowNumber;
    /**
     * 流程名称
     */
    private String flowName;
    /**
     * 流程发起人
     */
    private String flowCreator;
    /**
     * 流程发起时间
     */
    private Date flowCreateTime;
    /**
     * 流程文件xml
     */
    private String flowDataXml;
    /**
     * 负责人
     */
    private Integer flowType;
    /**
     * 流程结束时间
     */
    private Date flowPublishTime;
    /**
     * 流程状态
     */
    private String flowStatus;
}
