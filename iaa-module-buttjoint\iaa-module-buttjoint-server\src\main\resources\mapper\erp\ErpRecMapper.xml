<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iaa.datacenter.module.buttjoint.dal.mapper.erp.ErpRecMapper">

    <select id="getCustomerRcv" resultType="com.iaa.datacenter.module.buttjoint.dal.dataobject.erp.ErpCustomerRcvDO">
        select
        CBO_Customer.Code as customerCode,
        DATEPART(YEAR, AR_RecBillHead.BusinessDate) as businessYear,
        RIGHT('0' + CONVERT(VARCHAR(2), DATEPART(MONTH, AR_RecBillHead.BusinessDate)), 2) as businessMonth,
        sum(AR_RecBillUseLine.Money_FCMoney) as totalMoney
        from AR_RecBillHead
        left join AR_RecBillLine on RecBillHead=AR_RecBillHead.ID
        left join CBO_Customer on Cust_Customer = CBO_Customer.ID
        left join AR_RecBillUseLine on RecBillLine = AR_RecBillLine.ID
        where DocumentType='1002211180000182' and AR_RecBillUseLine.Cust_Customer &lt;&gt; '' and AR_RecBillUseLine.Cust_Customer is not null
        and CBO_Customer.Code in
        <foreach collection="customers" open="(" item="customer" separator="," close=")">
            #{customer}
        </foreach>
        and AR_RecBillHead.BusinessDate between #{dateRange[0]} and #{dateRange[1]}
        group by CBO_Customer.Code,
        DATEPART(YEAR, AR_RecBillHead.BusinessDate),
        RIGHT('0' + CONVERT(VARCHAR(2), DATEPART(MONTH, AR_RecBillHead.BusinessDate)), 2)
    </select>

</mapper>
