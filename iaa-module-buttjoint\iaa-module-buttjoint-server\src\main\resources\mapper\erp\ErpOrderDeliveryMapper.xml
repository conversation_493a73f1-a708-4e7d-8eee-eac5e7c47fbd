<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iaa.datacenter.module.buttjoint.dal.mapper.erp.ErpOrderDeliveryMapper">

    <select id="selectPage" resultType="com.iaa.datacenter.module.buttjoint.dal.dataobject.erp.ErpOrderDeliveryDO">
        with ss as (
        select
        ss.DocNo as docNo,
        ssl.ItemInfo_ItemCode as itemCode,
        sssl.DemandType as demand,
        ssl.OrderByQtyTU as qty,
        cast(ss.BusinessDate as date) as businessDate,
        sssl.PlanDate as planDate
        from SM_SO ss
        left join SM_SOLine ssl on ss.ID = ssl.SO
        left join SM_SOShipLine sssl on sssl.SOLine = ssl.ID
        where sssl.DemandType != -1
        ),
        completion as (
        select
        ss.docNo as salesOrderNo,
        ss.businessDate as orderDate,
        ss.itemCode as itemCode,
        ss.planDate as deliveryDate,
        ss.qty as orderQty,
        mm.DocNo as workOrderNo,
        ccadl.CompleteQty as completionQty,
        sum(ccadl.CompleteQty) over (partition by ss.docNo, ss.itemCode) as totalCompletionQty,
        max(ccad.BusinessDate) over (partition by ss.docNo, ss.itemCode) as lastCompletionDate
        from Complete_CompleteApplyDoc ccad
        left join Complete_CompleteApplyDocLine ccadl on ccadl.CompleteApplyDoc = ccad.ID
        left join MO_MO mm on ccadl.MO = mm.ID
        join ss on mm.DemandCode = ss.demand
        where 1=1
        <if test="dateType!=null and sTime!=null">
            <choose>
                <when test="dateType == 'year'">
                    and DATEDIFF(year,ccad.BusinessDate,#{sTime}) = 0
                </when>
                <when test="dateType == 'month'">
                    and DATEDIFF(month,ccad.BusinessDate,#{sTime}) = 0
                </when>
            </choose>
        </if>
        ),
        delayed_orders as (
        select
        salesOrderNo,
        itemCode,
        orderDate,
        deliveryDate,
        orderQty,
        totalCompletionQty,
        lastCompletionDate,
        case
        -- 条件 1: 累计完工数量等于订单数量，判断最后完工时间是否延期
        when totalCompletionQty = orderQty and lastCompletionDate &lt;= deliveryDate then 0 -- 正常
        when totalCompletionQty = orderQty and lastCompletionDate > deliveryDate and datediff(day, deliveryDate, lastCompletionDate) > 1 then 1 -- 延期
        -- 条件 2: 累计完工数量不等于订单数量，最后完工时间大于交期
        when totalCompletionQty &lt;&gt; orderQty and lastCompletionDate > deliveryDate then 1 -- 延期
        -- 条件 3: 累计完工数量不等于订单数量，最后完工时间小于交期
        when totalCompletionQty &lt;&gt; orderQty and lastCompletionDate &lt; deliveryDate then 0 -- 忽略
        else 0 -- 默认正常
        end as isDelayed,
        format(lastCompletionDate, 'yyyy-MM') as deliveryMonth
        from completion
        ),
        final_orders as (
        select
        salesOrderNo,
        max(deliveryMonth) as deliveryMonth,
        max(isDelayed) as isDelayed -- 如果同一订单的不同料号有延期，则整个订单算延期
        from delayed_orders
        group by salesOrderNo
        ),
        delivery_rate as (
        select
        deliveryMonth,
        count(case when isDelayed = 0 then 1 end) * 1.0 / count(*) as deliveryRate -- 交付率计算
        from final_orders
        group by deliveryMonth
        )
        select
        d.salesOrderNo,
        d.itemCode,
        d.deliveryMonth,
        d.orderDate,
        cast(d.deliveryDate as date) as deliveryDate,
        d.orderQty,
        d.totalCompletionQty,
        d.lastCompletionDate,
        case when f.isDelayed = 1 then '延期' else '正常' end as orderStatus,
        round(dr.deliveryRate*100,2) deliveryRate,
        COUNT(*) OVER () AS TotalCount
        from delayed_orders d
        join final_orders f on d.salesOrderNo = f.salesOrderNo
        left join delivery_rate dr on d.deliveryMonth = dr.deliveryMonth
        order by d.deliveryMonth,d.salesOrderNo, d.itemCode
    </select>

</mapper>
