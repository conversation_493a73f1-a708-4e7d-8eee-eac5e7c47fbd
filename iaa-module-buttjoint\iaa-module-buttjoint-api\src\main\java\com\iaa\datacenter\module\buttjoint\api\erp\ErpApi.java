package com.iaa.datacenter.module.buttjoint.api.erp;

import com.iaa.datacenter.module.buttjoint.api.erp.dto.ErpLastOrderDTO;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
public interface ErpApi {

    /**
     * 根据品号获取最后一单成交
     * @param itemCodes
     * @return
     */
    List<ErpLastOrderDTO> getLastOrder(Collection<String> itemCodes);

    /**
     * 获取订单交付率
     * @param dateType
     * @param sTime
     * @return
     */
    BigDecimal getOrderDeliveryRate(String dateType,String sTime);
}
