package com.iaa.datacenter.module.buttjoint.controller.admin.xiaoman.report.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.iaa.datacenter.framework.excel.core.convert.ListStringConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class XiaomanCustomerBaseInfoVO {
    @ExcelProperty(value = "业务员",converter = ListStringConvert.class,index = 0)
    @Schema(description = "业务员")
    private List<String> salesperson;

    @ExcelProperty(value = "客户所属组别",index = 1)
    @Schema(description = "客户所属组别")
    private String departmentName;

    @ExcelProperty(value = "客户名称",index = 2)
    @Schema(description = "客户名称")
    private String customerName;

    @ExcelProperty(value = "国家/地区",index = 3)
    @Schema(description = "国家/地区")
    private String countryName;

    @ExcelProperty(value = "洲/省",index = 4)
    @Schema(description = "洲/省")
    private String regionOrProvince;

    @ExcelProperty(value = "城市",index = 5)
    @Schema(description = "城市")
    private String city;

    @ExcelProperty(value = "客户画像",index = 6)
    @Schema(description = "客户画像")
    private String customerPortraits;

    @ExcelProperty(value = "客户性质",index = 7)
    @Schema(description = "客户性质")
    private String nature;

    @ExcelProperty(value = "客户阶段",index = 8)
    @Schema(description = "客户阶段")
    private String trailStatus;

    @ExcelProperty(value = "客户官网",index = 9)
    @Schema(description = "客户官网")
    private String homepage;

    @ExcelProperty(value = "客户营收规模",index = 10)
    @Schema(description = "客户营收规模")
    private String revenueScale;

    @ExcelProperty(value = "香氛产品规模评估",index = 11)
    @Schema(description = "香氛产品规模评估")
    private String fragranceRevenueScale;

    @ExcelProperty(value = "预估年销售额(万元)",index = 12)
    @Schema(description = "预估年销售额(万元)")
    private String estimatedAnnualSales;
}
