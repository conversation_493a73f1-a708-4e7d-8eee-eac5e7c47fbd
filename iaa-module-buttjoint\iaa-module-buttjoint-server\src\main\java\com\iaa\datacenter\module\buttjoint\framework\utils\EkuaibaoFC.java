package com.iaa.datacenter.module.buttjoint.framework.utils;

import cn.hutool.core.util.StrUtil;
import com.anwen.mongo.conditions.query.QueryWrapper;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.ekuaibao.*;
import com.iaa.datacenter.module.buttjoint.dal.mapper.erp.ErpCustomMapper;
import com.iaa.datacenter.module.buttjoint.service.ekuaibao.account.EkuaibaoPayService;
import com.iaa.datacenter.module.buttjoint.service.ekuaibao.currency.EkuaibaoCurrencyService;
import com.iaa.datacenter.module.buttjoint.service.ekuaibao.custom.EkuaibaoCustomService;
import com.iaa.datacenter.module.buttjoint.service.ekuaibao.feetype.EkuaibaoFeeTypeService;
import com.iaa.datacenter.module.buttjoint.service.ekuaibao.invoice.EkuaibaoInvoiceService;
import com.iaa.datacenter.module.buttjoint.service.ekuaibao.order.EkuaibaoOrderService;
import com.iaa.datacenter.module.buttjoint.service.ekuaibao.staffs.EkuaibaoStaffsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 这是一个 工具类，用于调用自定义的接口
 */
@Component
public class EkuaibaoFC {

    private static EkuaibaoCustomService ekuaibaoCustomService;
    private static EkuaibaoCurrencyService ekuaibaoCurrencyService;
    private static ErpCustomMapper erpCustomMapper;
    private static EkuaibaoPayService ekuaibaoPayService;
    private static EkuaibaoStaffsService ekuaibaoStaffsService;
    private static EkuaibaoFeeTypeService ekuaibaoFeeTypeService;
    private static EkuaibaoInvoiceService ekuaibaoInvoiceService;
    public static EkuaibaoOrderService ekuaibaoOrderService;

    @Autowired
    public EkuaibaoFC(EkuaibaoCustomService ekuaibaoCustomService,
                      EkuaibaoCurrencyService ekuaibaoCurrencyService,
                      ErpCustomMapper erpCustomMapper,
                      EkuaibaoPayService ekuaibaoPayService,
                      EkuaibaoStaffsService ekuaibaoStaffsService,
                      EkuaibaoFeeTypeService ekuaibaoFeeTypeService,
                      EkuaibaoInvoiceService ekuaibaoInvoiceService,
                      EkuaibaoOrderService ekuaibaoOrderService){
        EkuaibaoFC.ekuaibaoCustomService=ekuaibaoCustomService;
        EkuaibaoFC.ekuaibaoCurrencyService=ekuaibaoCurrencyService;
        EkuaibaoFC.erpCustomMapper=erpCustomMapper;
        EkuaibaoFC.ekuaibaoPayService=ekuaibaoPayService;
        EkuaibaoFC.ekuaibaoStaffsService=ekuaibaoStaffsService;
        EkuaibaoFC.ekuaibaoFeeTypeService=ekuaibaoFeeTypeService;
        EkuaibaoFC.ekuaibaoInvoiceService=ekuaibaoInvoiceService;
        EkuaibaoFC.ekuaibaoOrderService=ekuaibaoOrderService;
    }

    /**
     * 获取自定义档案的编码
     * @param customId
     * @return
     */
    public static String getCustomCode(String customId){
        if (StrUtil.isEmpty(customId)) return null;
        return EkuaibaoFC.ekuaibaoCustomService.getById(customId).getCode();
    }

    /**
     * 获取自定义档案名称
     * @param customId
     * @return
     */
    public static String getCustomName(String customId){
        if (StrUtil.isEmpty(customId)) return null;
        return EkuaibaoFC.ekuaibaoCustomService.getById(customId).getName();
    }

    /**
     * 获取币种编码
     * @return
     */
    public static String getCurrencyCode(String numCode){
        if(StrUtil.isEmpty(numCode))return null;
        if(numCode.equals("156")) return "C001";
        return EkuaibaoFC.ekuaibaoCurrencyService.one(new QueryWrapper<EkuaibaoCurrencyDO>().eq(EkuaibaoCurrencyDO::getNumCode,numCode)).getU9cCode();
    }

    /**
     * 获取自定部门
     * @param customId
     * @param org
     * @return
     */
    public static String getCustomDept(String customId,String org) throws Exception {
        if (StrUtil.isEmpty(customId)) return null;
        EkuaibaoCustomDO dept = EkuaibaoFC.ekuaibaoCustomService.getById(customId);

        for (String deptCode : Optional.ofNullable(dept.getU9cCode()).orElse(new ArrayList<>())) {
            if(deptCode.startsWith(org)){
                return deptCode.replace(org+"-","");
            }
        }
        throw new Exception("组织"+org+"下未找到对应的部门");
    }

    /**
     * 获取U9当日汇率
     * @param formCurrency
     * @param date
     * @return
     */
    public static BigDecimal getExchangeRate(String formCurrency,String date){
        if (StrUtil.isEmpty(formCurrency)) return null;
        if(formCurrency.equals("C001")) return BigDecimal.ONE;
        return erpCustomMapper.getExchangeRate(formCurrency,date);
    }

    /**
     * 获取付款账号
     * @param payId
     * @return
     */
    public static EkuaibaoPayDO getPayDO(String payId){
        if (StrUtil.isEmpty(payId)) return null;
        return ekuaibaoPayService.getById(payId);
    }

    /**
     * 获取用户
     * @param staffId
     * @param org
     * @return
     */
    public static String getStaffCode(String staffId,String org){
        if (StrUtil.isEmpty(staffId)) return null;
        EkuaibaoStaffsDO staff = EkuaibaoFC.ekuaibaoStaffsService.getById(staffId);
        for (String user : Optional.ofNullable(staff.getErpSellerCode()).orElse(new ArrayList<>())) {
            if(user.startsWith(org)){
                return user.replace(org+"-","");
            }
        }
        return null;
    }

    /**
     * 获取用户的ERP 用户关系
     * @param staffId
     * @return
     */
    public static String getStaffUserCode(String staffId){
        if(StrUtil.isEmpty(staffId)) return null;
        EkuaibaoStaffsDO staff = EkuaibaoFC.ekuaibaoStaffsService.getById(staffId);
        return staff.getErpUserCode();
    }

    /**
     * 获取用户名称
     * @param staffId
     * @return
     */
    public static String getStaffName(String staffId){
        if (StrUtil.isEmpty(staffId)) return null;
        EkuaibaoStaffsDO user = EkuaibaoFC.ekuaibaoStaffsService.getById(staffId);
        return user.getName();
    }

    /**
     * 获取档案所有名称
     * @param names
     * @param customId
     */
    public static void getCustomNames(List<String> names,String customId){
        if (StrUtil.isEmpty(customId)) return;
        EkuaibaoCustomDO custom = EkuaibaoFC.ekuaibaoCustomService.getById(customId);
        names.add(custom.getName());
        if(StrUtil.isNotEmpty(custom.getParentId())){
            getCustomNames(names,custom.getParentId());
        }
    }

    /**
     * 获取档案所有的ID
     * @param deptIds
     * @param deptId
     */
    public static void getCustomIds(List<String> deptIds,String deptId){
        if (StrUtil.isEmpty(deptId)) return;
        EkuaibaoCustomDO byId = EkuaibaoFC.ekuaibaoCustomService.getById(deptId);
        deptIds.add(byId.getId());
        if(StrUtil.isNotEmpty(byId.getParentId())){
            getCustomIds(deptIds,byId.getParentId());
        }
    }

    /**
     * 获取费用类型的层级关系
     * @param feeNames
     * @param feeId
     */
    public static void getFeeNames(List<String> feeNames,String feeId){
        if (StrUtil.isEmpty(feeId)) return;
        EkuaibaoFeeTypeDO byId = EkuaibaoFC.ekuaibaoFeeTypeService.getById(feeId);
        feeNames.add(byId.getName());
        if(StrUtil.isNotEmpty(byId.getParentId())){
            getFeeNames(feeNames,byId.getParentId());
        }
    }

    /**
     * 获取新版费用类型维护
     * @param feeId
     * @return
     */
    public static String getFeeCode(String feeId){
        if (StrUtil.isEmpty(feeId)) return null;
        return EkuaibaoFC.ekuaibaoFeeTypeService.getById(feeId).getCode().split("-")[0];
    }

    /**
     * 获取发票档案
     * @param invoiceId
     * @return
     */
    public static EkuaibaoInvoiceDO getInvoiceDO(String invoiceId){
        if(StrUtil.isEmpty(invoiceId)) return null;
        return EkuaibaoFC.ekuaibaoInvoiceService.getById(invoiceId);
    }
}
