package com.iaa.datacenter.module.buttjoint.service.xiaoman.customer;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.anwen.mongo.conditions.interfaces.Projection;
import com.anwen.mongo.conditions.query.QueryWrapper;
import com.anwen.mongo.service.impl.ServiceImpl;
import com.iaa.datacenter.framework.common.core.KeyValue;
import com.iaa.datacenter.framework.common.pojo.PageResult;
import com.iaa.datacenter.framework.common.util.collection.CollectionUtils;
import com.iaa.datacenter.framework.web.core.util.WebFrameworkUtils;
import com.iaa.datacenter.module.buttjoint.controller.admin.xiaoman.report.vo.XiaomanOpportunityReportPageReqVO;
import com.iaa.datacenter.module.buttjoint.controller.admin.xiaoman.report.vo.XiaomanOpportunityReportRespVO;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.erp.ErpItemShipDO;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.xiaoman.XiaomanCustomerDO;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.xiaoman.XiaomanOpportunityDO;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.xiaoman.XiaomanUserDO;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.xiaoman.XiaomanViewAuthorityDO;
import com.iaa.datacenter.module.buttjoint.dal.mapper.erp.ErpCustomMapper;
import com.iaa.datacenter.module.buttjoint.dal.mapper.erp.ErpShipMapper;
import com.iaa.datacenter.module.buttjoint.service.xiaoman.user.XiaomanUserService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class XiaomanCustomerServiceImpl extends ServiceImpl<XiaomanCustomerDO> implements XiaomanCustomerService {

    @Resource
    private XiaomanUserService xiaomanUserService;
    @Resource
    private ErpShipMapper erpShipMapper;
    @Resource
    private ErpCustomMapper erpCustomMapper;

    @Resource
    private XiaomanViewAuthorityService xiaomanViewAuthorityService;

    @Override
    public PageResult<XiaomanOpportunityReportRespVO> getOpportunityReportPage(XiaomanOpportunityReportPageReqVO reqVO) {
        // 构造查询条件并查询数据
        QueryWrapper<XiaomanCustomerDO> wrapper = new QueryWrapper<>();
        if (CollUtil.isEmpty(reqVO.getSalesperson())&&CollUtil.isEmpty(reqVO.getDepartmentName())&&WebFrameworkUtils.getLoginUserId() != 1) {
            XiaomanViewAuthorityDO authority = xiaomanViewAuthorityService.getById(WebFrameworkUtils.getLoginUserId());
            if (authority == null) {
                return new PageResult<>(new ArrayList<>(), 0L);
            }
            wrapper.in(XiaomanCustomerDO::getUser_id, authority.getXiaoman_user_ids());
        }
        wrapper.in(CollUtil.isNotEmpty(reqVO.getSalesperson()),XiaomanCustomerDO::getUser_id, reqVO.getSalesperson());
        if(CollUtil.isNotEmpty(reqVO.getDepartmentName())){
            List<XiaomanUserDO> list = xiaomanUserService.list(new QueryWrapper<XiaomanUserDO>().in(XiaomanUserDO::getDepartment_id, reqVO.getDepartmentName()));
            wrapper.in(XiaomanCustomerDO::getUser_id,list.stream().map(XiaomanUserDO::getUser_id).collect(Collectors.toSet()));
        }

        wrapper.like(StrUtil.isNotEmpty(reqVO.getCustomerName()),XiaomanCustomerDO::getName, reqVO.getCustomerName());
        wrapper.like(StrUtil.isNotEmpty(reqVO.getCountryName()),XiaomanCustomerDO::getCountry_name, reqVO.getCountryName());
        if(StrUtil.isNotEmpty(reqVO.getRegionOrProvince())){
            wrapper.or(p->p.like(XiaomanCustomerDO::getRegion, reqVO.getRegionOrProvince()).like(XiaomanCustomerDO::getProvince, reqVO.getRegionOrProvince()));
        }
        wrapper.like(StrUtil.isNotEmpty(reqVO.getCity()),XiaomanCustomerDO::getCity, reqVO.getCity());
        wrapper.in(CollUtil.isNotEmpty(reqVO.getCustomerPortraits()),XiaomanCustomerDO::getCustomer_portraits, reqVO.getCustomerPortraits());
        wrapper.like(StrUtil.isNotEmpty(reqVO.getNature()),XiaomanCustomerDO::getNature, reqVO.getNature());
        wrapper.like(StrUtil.isNotEmpty(reqVO.getHomepage()),XiaomanCustomerDO::getHomepage, reqVO.getHomepage());
        wrapper.in(CollUtil.isNotEmpty(reqVO.getRevenueScale()),XiaomanCustomerDO::getRevenue_scale, reqVO.getRevenueScale());
        wrapper.in(CollUtil.isNotEmpty(reqVO.getFragranceRevenueScale()),XiaomanCustomerDO::getFragrance_revenue_scale, reqVO.getFragranceRevenueScale());
        wrapper.like(StrUtil.isNotEmpty(reqVO.getUntoMachineRemark()),XiaomanCustomerDO::getUntoMachineRemark, reqVO.getUntoMachineRemark());
        wrapper.like(StrUtil.isNotEmpty(reqVO.getUntoEssentialOilRemark()),XiaomanCustomerDO::getUntoEssentialOilRemark, reqVO.getUntoEssentialOilRemark());
        wrapper.like(StrUtil.isNotEmpty(reqVO.getRelevantProductRemark()),XiaomanCustomerDO::getRelevantProductRemark, reqVO.getRelevantProductRemark());

        if(StrUtil.isNotEmpty(reqVO.getMachine())){
            List<ErpItemShipDO> machineShip = erpShipMapper.getItemShip(reqVO.getSalesTime(), 1, null,reqVO.getMachine());
            wrapper.in(XiaomanCustomerDO::getErp_code, machineShip.stream().map(p -> p.getCustomerCode().trim()).collect(Collectors.toSet()));
        }
        if(StrUtil.isNotEmpty(reqVO.getOil())){
            List<ErpItemShipDO> oilShip = erpShipMapper.getItemShip(reqVO.getSalesTime(), 2, null,reqVO.getOil());
            wrapper.in(XiaomanCustomerDO::getErp_code, oilShip.stream().map(p -> p.getCustomerCode().trim()).collect(Collectors.toSet()));
        }


        wrapper.between(XiaomanCustomerDO::getUpdate_time, reqVO.getCustomerTime()[0], reqVO.getCustomerTime()[1], true);
        wrapper.projectDisplay(XiaomanCustomerDO::getCompany_id,
                XiaomanCustomerDO::getName,
                XiaomanCustomerDO::getCountry_name,
                XiaomanCustomerDO::getProvince,
                XiaomanCustomerDO::getRegion,
                XiaomanCustomerDO::getCity,
                XiaomanCustomerDO::getUser_id,
                XiaomanCustomerDO::getErp_code,
                XiaomanCustomerDO::getCustomer_portraits,
                XiaomanCustomerDO::getNature,
                XiaomanCustomerDO::getHomepage,
                XiaomanCustomerDO::getRevenue_scale,
                XiaomanCustomerDO::getFragrance_revenue_scale,
                XiaomanCustomerDO::getEstimated_annual_sales,
                XiaomanCustomerDO::getUntoMachineRemark,
                XiaomanCustomerDO::getTrail_status,
                XiaomanCustomerDO::getUntoEssentialOilRemark,
                XiaomanCustomerDO::getRelevantProductRemark);
        com.anwen.mongo.model.PageResult<XiaomanCustomerDO> page = new com.anwen.mongo.model.PageResult<>();
        if (reqVO.getPageSize() == -1) {
            List<XiaomanCustomerDO> list = super.list(wrapper);
            page.setContentData(list);
            page.setTotalSize(list.size());
        } else {
            page = super.page(wrapper, reqVO.getPageNo(), reqVO.getPageSize());
        }
        // 获取ERP 客户的销量信息
        Set<String> customerCode = page.getContentData().stream().map(p -> p.getErp_code().trim()).filter(StrUtil::isNotEmpty).collect(Collectors.toSet());
        HashMap<String, List<ErpItemShipDO>> machineShipMap = new HashMap<>();
        HashMap<String, List<ErpItemShipDO>> oilShipMap = new HashMap<>();
        HashMap<String, String> customerMap = new HashMap<>();
        if (customerCode.size() > 0) {
            List<ErpItemShipDO> machineShip = erpShipMapper.getItemShip(reqVO.getSalesTime(), 1, customerCode,null);
            List<ErpItemShipDO> oilShip = erpShipMapper.getItemShip(reqVO.getSalesTime(), 2, customerCode,null);
            machineShipMap.putAll(machineShip.stream().collect(Collectors.groupingBy(ErpItemShipDO::getCustomerCode)));
            oilShipMap.putAll(oilShip.stream().collect(Collectors.groupingBy(ErpItemShipDO::getCustomerCode)));
            List<KeyValue<String, String>> customer = erpCustomMapper.getCustomer(customerCode);
            customerMap.putAll(customer.stream().collect(Collectors.toMap(KeyValue::getKey, KeyValue::getValue)));
        }

        // 构造返回数据
        Map<String, XiaomanUserDO> userNameMap = getUserNameMap(page.getContentData());
        List<XiaomanOpportunityReportRespVO> result = page.getContentData().stream()
                .map(item -> buildXiaomanOpportunityReportRespVO(item, userNameMap, machineShipMap, oilShipMap, customerMap))
                .collect(Collectors.toList());
        return new PageResult<>(result, page.getTotalSize());
    }

    @Override
    public XiaomanOpportunityReportRespVO calculateOpportunityReport(List<XiaomanOpportunityReportRespVO> contentList) {
        XiaomanOpportunityReportRespVO result = new XiaomanOpportunityReportRespVO();
        result.setSalesperson(List.of("合计"));
        for (XiaomanOpportunityReportRespVO opportunity : contentList) {
            result.setMachineTotalPrice(getNumber(result.getMachineTotalPrice()) + getNumber(opportunity.getMachineTotalPrice()));
            result.setOilTotalPrice(getNumber(result.getOilTotalPrice()) + getNumber(opportunity.getOilTotalPrice()));
        }
        return result;
    }

    private Double getNumber(Number value) {
        if (value == null) {
            return 0D;
        }
        return value.doubleValue();
    }

    /**
     * 获取小满用户名称
     *
     * @param dataList
     * @return
     */
    @Override
    public Map<String, XiaomanUserDO> getUserNameMap(List<XiaomanCustomerDO> dataList) {
        Set<String> userIds = dataList.stream()
                .flatMap(xiaomanCustomerDO -> {
                    JSONArray userIdArray = xiaomanCustomerDO.getUser_id();
                    return userIdArray.stream(); // 将 JSONArray 转换为 List，再转换为 Stream
                })
                .map(Object::toString) // 将 Object 转换为 String
                .collect(Collectors.toSet()); // 收集到 Set 中进行去重
        List<XiaomanUserDO> userList = xiaomanUserService.list(new QueryWrapper<XiaomanUserDO>().in(XiaomanUserDO::getUser_id, userIds));
        return userList.stream().collect(Collectors.toMap(XiaomanUserDO::getUser_id, p -> p));
    }

    /**
     * 填充返回结果类型数据
     *
     * @param xiaomanCustomerDO
     * @param userNameMap
     * @return
     */
    private XiaomanOpportunityReportRespVO buildXiaomanOpportunityReportRespVO(
            XiaomanCustomerDO xiaomanCustomerDO,
            Map<String, XiaomanUserDO> userNameMap,
            Map<String, List<ErpItemShipDO>> machineShipMap,
            Map<String, List<ErpItemShipDO>> oilShipMap,
            Map<String, String> customerMap) {
        List<XiaomanUserDO> users = CollectionUtils.convertList(xiaomanCustomerDO.getUser_id(), item -> userNameMap.getOrDefault(item.toString(), new XiaomanUserDO()));
        XiaomanOpportunityReportRespVO result = XiaomanOpportunityReportRespVO.builder()
                .salesperson(users.stream().map(XiaomanUserDO::getChinese_name).toList())
                .departmentName(CollUtil.isEmpty(users) ? "" : users.get(0).getDepartment_name())
                .customerName(customerMap.getOrDefault(xiaomanCustomerDO.getErp_code(), xiaomanCustomerDO.getName()))
                .countryName(xiaomanCustomerDO.getCountry_name())
                .regionOrProvince(Optional.ofNullable(xiaomanCustomerDO.getProvince()).orElse(xiaomanCustomerDO.getRegion()))
                .city(xiaomanCustomerDO.getCity())
                .customerPortraits(xiaomanCustomerDO.getCustomer_portraits())
                .nature(xiaomanCustomerDO.getNature())
                .trailStatus(xiaomanCustomerDO.getTrail_status())
                .homepage(xiaomanCustomerDO.getHomepage())
                .revenueScale(xiaomanCustomerDO.getRevenue_scale())
                .fragranceRevenueScale(xiaomanCustomerDO.getFragrance_revenue_scale())
                .estimatedAnnualSales(xiaomanCustomerDO.getEstimated_annual_sales())
                .untoMachineRemark(xiaomanCustomerDO.getUntoMachineRemark())
                .untoEssentialOilRemark(xiaomanCustomerDO.getUntoEssentialOilRemark())
                .relevantProductRemark(xiaomanCustomerDO.getRelevantProductRemark())
                .build();
        // 填充销量数据
        List<ErpItemShipDO> machine = machineShipMap.get(xiaomanCustomerDO.getErp_code().trim());
        List<ErpItemShipDO> oil = oilShipMap.get(xiaomanCustomerDO.getErp_code().trim());
        ErpItemShipDO defaultValue = new ErpItemShipDO();
        if (machine != null) {
            result.setMachineModel1(getOrDefault(machine, 0, defaultValue).getModel());
            result.setMachineModel2(getOrDefault(machine, 1, defaultValue).getModel());
            result.setMachineModel3(getOrDefault(machine, 2, defaultValue).getModel());
            result.setMachineModel4(getOrDefault(machine, 3, defaultValue).getModel());
            result.setMachineModel5(getOrDefault(machine, 4, defaultValue).getModel());
            result.setMachinePrice1(Optional.ofNullable(getOrDefault(machine, 0, defaultValue).getShipMoney()).orElse(BigDecimal.ZERO).doubleValue());
            result.setMachinePrice2(Optional.ofNullable(getOrDefault(machine, 1, defaultValue).getShipMoney()).orElse(BigDecimal.ZERO).doubleValue());
            result.setMachinePrice3(Optional.ofNullable(getOrDefault(machine, 2, defaultValue).getShipMoney()).orElse(BigDecimal.ZERO).doubleValue());
            result.setMachinePrice4(Optional.ofNullable(getOrDefault(machine, 3, defaultValue).getShipMoney()).orElse(BigDecimal.ZERO).doubleValue());
            result.setMachinePrice5(Optional.ofNullable(getOrDefault(machine, 4, defaultValue).getShipMoney()).orElse(BigDecimal.ZERO).doubleValue());
            result.setMachineTotalPrice(Optional.ofNullable(machine).orElse(new ArrayList<>()).stream().mapToDouble(p -> Optional.ofNullable(p.getShipMoney()).orElse(BigDecimal.ZERO).doubleValue()).sum());
        }
        if (oil != null) {
            result.setOilModel1(getOrDefault(oil, 0, defaultValue).getModel());
            result.setOilModel2(getOrDefault(oil, 1, defaultValue).getModel());
            result.setOilModel3(getOrDefault(oil, 2, defaultValue).getModel());
            result.setOilModel4(getOrDefault(oil, 3, defaultValue).getModel());
            result.setOilModel5(getOrDefault(oil, 4, defaultValue).getModel());
            result.setOilPrice1(Optional.ofNullable(getOrDefault(oil, 0, defaultValue).getShipMoney()).orElse(BigDecimal.ZERO).doubleValue());
            result.setOilPrice2(Optional.ofNullable(getOrDefault(oil, 1, defaultValue).getShipMoney()).orElse(BigDecimal.ZERO).doubleValue());
            result.setOilPrice3(Optional.ofNullable(getOrDefault(oil, 2, defaultValue).getShipMoney()).orElse(BigDecimal.ZERO).doubleValue());
            result.setOilPrice4(Optional.ofNullable(getOrDefault(oil, 3, defaultValue).getShipMoney()).orElse(BigDecimal.ZERO).doubleValue());
            result.setOilPrice5(Optional.ofNullable(getOrDefault(oil, 4, defaultValue).getShipMoney()).orElse(BigDecimal.ZERO).doubleValue());
            result.setOilTotalPrice(Optional.ofNullable(oil).orElse(new ArrayList<>()).stream().mapToDouble(p -> Optional.ofNullable(p.getShipMoney()).orElse(BigDecimal.ZERO).doubleValue()).sum());
        }
        return result;
    }

    public static <T> T getOrDefault(List<T> list, int index, T defaultValue) {
        if (list != null && index >= 0 && index < list.size()) {
            return list.get(index);
        } else {
            return defaultValue;
        }
    }

}
