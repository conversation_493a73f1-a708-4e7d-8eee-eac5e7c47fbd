package com.iaa.datacenter.module.buttjoint.dal.mapper.plm;

import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Update;

@DS("plm")
@Mapper
public interface MaterialUpdateMapper {

    @Update("""
            update BOM_027 set MODEL=#{model} where PARTID=#{itemCode};
            update BOM_028 set MaterialModel=#{model} where PARTID=#{itemCode};
            """)
    void updateMaterial(String itemCode,String model);


    @Insert("insert into sys_035 (tablename, colname, listvalue, coldescribe) values ('BOM_028','project_code',#{model},#{type})")
    void insertModel(String model,String type);
}
