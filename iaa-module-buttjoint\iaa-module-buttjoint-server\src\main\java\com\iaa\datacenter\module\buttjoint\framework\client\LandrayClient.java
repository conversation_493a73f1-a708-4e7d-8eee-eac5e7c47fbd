package com.iaa.datacenter.module.buttjoint.framework.client;

import cn.hutool.http.*;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.iaa.datacenter.framework.buttjoint.annotation.ClientIdentifier;
import com.iaa.datacenter.framework.buttjoint.config.ButtJointConfig;
import com.iaa.datacenter.framework.buttjoint.config.ButtJointProperties;
import com.iaa.datacenter.framework.buttjoint.constant.ButtJointNameConstants;
import com.iaa.datacenter.framework.buttjoint.service.ButtJointClientService;
import com.iaa.datacenter.framework.common.util.json.JsonUtils;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Map;

@Service
@ClientIdentifier(name= ButtJointNameConstants.LANDRAY)
public class LandrayClient implements ButtJointClientService {
    @Resource
    private ButtJointProperties buttJointProperties;
    @Override
    public JSONObject auth() {
        return null;
    }

    @Override
    public JSONObject auth(String code) {
        return null;
    }

    @Override
    public JSONObject readObj(String url, Method type, Map<String, Object> query, Map<String, Object> body, String... code) {
        ButtJointConfig buttJointConfig = buttJointProperties.getType().get(ButtJointNameConstants.LANDRAY);
        HttpRequest request = HttpUtil.createRequest(type,buttJointConfig.getUrl()+url)
                .contentType(ContentType.JSON.getValue())
                .body(JSON.toJSONString(body));
        try(HttpResponse response = request.execute()){
            return JsonUtils.parseObject(response.body(),JSONObject.class);
        }
    }

    @Override
    public JSONObject readObj(String url, Method type, Map<String, Object> query, ArrayList<Object> body, String... code) {
        return null;
    }
}
