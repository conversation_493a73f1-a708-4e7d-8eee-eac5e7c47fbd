package com.iaa.datacenter.module.buttjoint.dal.mapper.erp;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iaa.datacenter.module.buttjoint.controller.admin.erp.custom.vo.ErpCustomPageReqVO;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.erp.ErpProjectDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * ERP 项目 Mapper
 */
@DS("erp")
@Mapper
public interface ErpProjectMapper {

    IPage<ErpProjectDO> page(Page<ErpProjectDO> page, @Param(Constants.WRAPPER) Wrapper<ErpCustomPageReqVO> wrapper);

}
