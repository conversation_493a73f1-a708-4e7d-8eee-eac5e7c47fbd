package com.iaa.datacenter.framework.buttjoint.service;

import cn.hutool.http.Method;
import com.alibaba.fastjson.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/**
 * 对接客户端服务
 */
public interface ButtJointClientService {

    /**
     * 获取授权信息
     * @return
     */
    JSONObject auth();

    /**
     * 获取授权信息
     * @param code 唯一标志
     * @return
     */
    JSONObject auth(String code);

    /**
     * 基础get请求
     * @param url
     * @param query
     * @return
     */
    default JSONObject get(String url,Map<String,Object> query,String ...code){
        return readObj(url, Method.GET,query ,new HashMap<>(),code);
    }

    /**
     * 基础Post请求
     * @param url
     * @param query
     * @param body
     * @return
     */
    default JSONObject post(String url,Map<String,Object> query,Map<String,Object> body,String ...code){
        return readObj(url,Method.POST,query,body,code);
    }

    /**
     * 基础Post请求
     * @param url
     * @param query
     * @param body
     * @param code
     * @return
     */
    default JSONObject post(String url,Map<String,Object> query,ArrayList<Object> body,String ...code){
        return readObj(url,Method.POST,query,body,code);
    }
    /**
     * 请求数据
     * @param url
     * @param type
     * @param query
     * @param body
     * @return
     */
    JSONObject readObj(String url, Method type, Map<String,Object> query, Map<String,Object> body,String ...code);

    /**
     * 请求数据
     * @param url
     * @param type
     * @param query
     * @param body
     * @param code
     * @return
     */
    JSONObject readObj(String url, Method type, Map<String,Object> query, ArrayList<Object> body, String ...code);
}
