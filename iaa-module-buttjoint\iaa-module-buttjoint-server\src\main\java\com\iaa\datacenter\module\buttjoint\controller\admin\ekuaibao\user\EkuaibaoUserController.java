package com.iaa.datacenter.module.buttjoint.controller.admin.ekuaibao.user;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.anwen.mongo.conditions.query.QueryWrapper;
import com.anwen.mongo.conditions.update.UpdateWrapper;
import com.iaa.datacenter.framework.common.core.KeyValue;
import com.iaa.datacenter.framework.common.pojo.CommonResult;
import com.iaa.datacenter.framework.common.pojo.PageResult;
import com.iaa.datacenter.framework.common.pojo.WebTableColumn;
import com.iaa.datacenter.framework.common.util.object.BeanUtils;
import com.iaa.datacenter.framework.common.util.object.WebColumnUtils;
import com.iaa.datacenter.module.buttjoint.controller.admin.ekuaibao.user.vo.EkuaibaoUserPageReqVO;
import com.iaa.datacenter.module.buttjoint.controller.admin.ekuaibao.user.vo.EkuaibaoUserRespVO;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.ekuaibao.EkuaibaoDeptDO;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.ekuaibao.EkuaibaoStaffsDO;
import com.iaa.datacenter.module.buttjoint.dal.mapper.erp.ErpCustomMapper;
import com.iaa.datacenter.module.buttjoint.service.ekuaibao.dept.EkuaibaoDeptService;
import com.iaa.datacenter.module.buttjoint.service.ekuaibao.staffs.EkuaibaoStaffsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.iaa.datacenter.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 合思用户维护")
@RestController
@RequestMapping("/butt-joint/ekuaibao/user")
@Validated
@Slf4j
public class EkuaibaoUserController {
    @Resource
    private EkuaibaoStaffsService ekuaibaoStaffsService;
    @Resource
    private EkuaibaoDeptService ekuaibaoDeptService;
    @Resource
    private ErpCustomMapper erpCustomMapper;

    @GetMapping("/get-columns")
    @Operation(summary = "获取合思用户列信息")
    @PreAuthorize("@ss.hasPermission('ekuaibao:user:list')")
    public CommonResult<List<WebTableColumn>> getColumns(){
        return success(WebColumnUtils.parseTableColumn(EkuaibaoUserRespVO.class));
    }

    @GetMapping("/get-erp-all-seller")
    @Operation(summary = "获取U9所有业务员信息")
    @PreAuthorize("@ss.hasPermission('ekuaibao:user:list')")
    public CommonResult<List<KeyValue<String,String>>> getErpAllSeller(){
        return success(erpCustomMapper.getSeller());
    }

    @GetMapping("/get-erp-all-employee")
    @Operation(summary = "获取U9所有员工记录信息")
    @PreAuthorize("@ss.hasPermission('ekuaibao:user:list')")
    public CommonResult<List<KeyValue<String,String>>> getErpAllEmployee(){
        return success(erpCustomMapper.getEmployee());
    }

    @GetMapping("/page")
    @Operation(summary = "获取合思用户数据")
    @PreAuthorize("@ss.hasPermission('ekuaibao:user:list')")
    public CommonResult<PageResult<EkuaibaoUserRespVO>> page(@Valid EkuaibaoUserPageReqVO reqVO){
        QueryWrapper<EkuaibaoStaffsDO> wrapper = new QueryWrapper<>();
        wrapper.like(StrUtil.isNotEmpty(reqVO.getName()),EkuaibaoStaffsDO::getName,reqVO.getName());
        if(StrUtil.isNotEmpty(reqVO.getDeptName())){
            List<EkuaibaoDeptDO> list = ekuaibaoDeptService.list(new QueryWrapper<EkuaibaoDeptDO>().like(EkuaibaoDeptDO::getName, reqVO.getDeptName()));
            wrapper.in(CollUtil.isNotEmpty(list),EkuaibaoStaffsDO::getDefaultDepartment,list.stream().map(EkuaibaoDeptDO::getId).toList());
        }

        com.anwen.mongo.model.PageResult<EkuaibaoStaffsDO> page = ekuaibaoStaffsService.page(wrapper, reqVO.getPageNo(), reqVO.getPageSize());
        page.getContentData().forEach(item->{
            if(StrUtil.isNotEmpty(item.getDefaultDepartment())){
                EkuaibaoDeptDO deptDO = ekuaibaoDeptService.getById(item.getDefaultDepartment());
                if (deptDO!=null){
                    item.setDeptName(deptDO.getName());
                }else {
                    log.info("没有找到对应的部门信息,id={}",item.getDefaultDepartment());
                }
            }
        });
        return success(new PageResult<>(BeanUtils.toBean(page.getContentData(), EkuaibaoUserRespVO.class),page.getTotalSize()));
    }

    @PostMapping("/save")
    @Operation(summary = "更新合思用户数据")
    @PreAuthorize("@ss.hasPermission('ekuaibao:user:list')")
    public CommonResult<Boolean> save(@Valid @RequestBody EkuaibaoUserRespVO saveVO){
        return success(ekuaibaoStaffsService.update(new UpdateWrapper<EkuaibaoStaffsDO>()
                .eq(EkuaibaoStaffsDO::getId,saveVO.getId())
                .set(EkuaibaoStaffsDO::getErpUserCode,saveVO.getErpUserCode())
                .set(EkuaibaoStaffsDO::getErpSellerCode,saveVO.getErpSellerCode()))
        );
    }
}
