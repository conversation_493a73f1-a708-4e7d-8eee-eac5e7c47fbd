package com.iaa.datacenter.module.buttjoint.service.erp.project;

import com.iaa.datacenter.framework.common.pojo.PageResult;
import com.iaa.datacenter.module.buttjoint.controller.admin.erp.custom.vo.ErpCustomPageReqVO;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.erp.ErpProjectDO;

/**
 * U9 项目接口
 */
public interface ErpProjectService {

    /**
     * 分页查询未同步到合思的项目
     * @param pageReqVO
     * @return
     */
    PageResult<ErpProjectDO> page(ErpCustomPageReqVO pageReqVO);
}
