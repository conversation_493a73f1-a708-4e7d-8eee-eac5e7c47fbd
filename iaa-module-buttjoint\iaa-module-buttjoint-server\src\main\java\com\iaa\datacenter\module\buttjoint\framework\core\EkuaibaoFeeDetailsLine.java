package com.iaa.datacenter.module.buttjoint.framework.core;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 合思费用明细行
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EkuaibaoFeeDetailsLine implements Serializable {
    /**
     * 收支项目
     */
    private String income;
    private String incomeId;
    /**
     * 部门
     */
    private String dept;
    private String deptId;
    /**
     * 币种
     */
    private String currency;
    /**
     * 汇率
     */
    private BigDecimal rate;
    /**
     * 金额
     */
    private BigDecimal amount;
    private BigDecimal amountCny;
    /**
     * 结算方式
     */
    private String settlement;
    /**
     * 付款账号
     */
    private String payCode;
    /**
     * 是合思商城消费
     */
    private Boolean hasMall;
    /**
     * 是借支
     */
    private Boolean hasDebit;
    /**
     * 是押金
     */
    private Boolean hasDeposit;
    /**
     * 是充值
     */
    private Boolean hasRecharge;
    /**
     * 备注
     */
    private String remark;
    /**
     * 发票列表
     */
    List<EkuaibaoFeeInvoice> invoiceList;
}
