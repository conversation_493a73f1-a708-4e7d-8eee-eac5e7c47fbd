package com.iaa.datacenter.module.buttjoint.dal.mapper.erp;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.erp.ErpOrderDeliveryDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import java.util.List;

@Mapper
@DS("erp")
public interface ErpOrderDeliveryMapper {


    Page<ErpOrderDeliveryDO> selectPage(Page<?> page, String dateType,String sTime);

    List<ErpOrderDeliveryDO> selectPage(String dateType,String sTime);
}
