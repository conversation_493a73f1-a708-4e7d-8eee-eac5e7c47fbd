package com.iaa.datacenter.module.buttjoint.dal.dataobject.xiekeyun;

import com.anwen.mongo.annotation.ID;
import com.anwen.mongo.annotation.collection.CollectionName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@CollectionName("xiekeyun_employee")
public class XiekeyunEmployeeDO {

    /**
     * 携客云的编号
     */
    @ID
    private String xkUniqueCode;
    /**
     * 企业编码
     */
    private String companyCode;
    /**
     * 员工姓名
     */
    private String employeeName;
    /**
     * 工号
     */
    private String employeeNumber;
    /**
     * 部门名称
     */
    private String departmentName;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 手机地区码
     */
    private String mobileArea;
    /**
     * 邮箱
     */
    private String email;
    /**
     * ERP帐号
     */
    private String erpAccount;
    /**
     * ERP员工编码
     */
    private String erpEmployeeCode;
    /**
     * 员工状态 1：正常 0：停用
     */
    private Integer employeeFlag;
    /**
     * 用户类型 0：普通用户 1：管理员
     */
    private Integer userType;
    /**
     * 携客号
     */
    private String loginName;
    /**
     * 用户名
     */
    private String userName;
    /**
     * 岗位编码
     */
    private String roleCode;
    /**
     * 岗位名称
     */
    private String roleName;
    /**
     * 邀约状态 0：未发起 1：已发起，未处理 2：已发起已处理 3：邀约码失效 4：离职
     */
    private Integer inviteStatus;
    /**
     * 邀约时间（时间戳）
     */
    private Long inviteTime;
    /**
     *
     */
    private String remark;
}
