package com.iaa.datacenter.module.buttjoint.job.erp;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.iaa.datacenter.framework.quartz.core.handler.JobHandler;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.erp.ErpApproveMessageDO;
import com.iaa.datacenter.module.buttjoint.dal.mapper.erp.ErpApproveMessageMapper;
import com.iaa.datacenter.module.buttjoint.service.erp.approve.ErpApproveMessageService;
import com.iaa.datacenter.module.buttjoint.service.landray.todo.LandrayTodoService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@RequiredArgsConstructor
public class ErpApproveSendTodoJob implements JobHandler {

    private final ErpApproveMessageMapper erpApproveMessageMapper;

    private final StringRedisTemplate stringRedisTemplate;

    private final LandrayTodoService landrayTodoService;

    private final ErpApproveMessageService erpApproveMessageService;

    @Override
    public String execute(String param) throws Exception {
        String startTime = stringRedisTemplate.opsForValue().get("ERP_APPROVE_QUERY_END_TIME");
        if(StrUtil.isEmpty(startTime)){
            startTime= DateUtil.date().offset(DateField.DAY_OF_YEAR,-1).toString("yyyy-MM-dd HH:mm:ss");
        }
        String endTime = DateUtil.now();
        List<ErpApproveMessageDO> list = erpApproveMessageMapper.list(startTime,endTime);
        stringRedisTemplate.opsForValue().set("ERP_APPROVE_QUERY_END_TIME",endTime);
        for (ErpApproveMessageDO message : list) {
            if(message.getState() == 2 && message.getOperation() ==0){
                message.setIsApproved(true);
            }
            message.setIsApproved(message.getTackState()==2);
            if(message.getStateInfo()==2){
                message.setIsApproved(true);
            }
            if(message.getState()==3){
                message.setIsApproved(true);
            }

            ErpApproveMessageDO erpApproveMessageDO = landrayTodoService.sendToDo(message);
            erpApproveMessageService.saveOrUpdate(erpApproveMessageDO, true);
        }
        return "任务执行成功，同步待办至ERP"+list.size();
    }
}
