package com.iaa.datacenter.module.buttjoint.job.ekuaibao;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.iaa.datacenter.framework.buttjoint.constant.ButtJointNameConstants;
import com.iaa.datacenter.framework.buttjoint.service.ButtJointClientService;
import com.iaa.datacenter.framework.common.util.json.JsonUtils;
import com.iaa.datacenter.framework.quartz.core.handler.JobHandler;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.ekuaibao.EkuaibaoCustomDO;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.ekuaibao.EkuaibaoFeeTypeDO;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.ekuaibao.EkuaibaoPayDO;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.ekuaibao.EkuaibaoSubjectDO;
import com.iaa.datacenter.module.buttjoint.framework.constant.EkuaibaoApiConstant;
import com.iaa.datacenter.module.buttjoint.framework.factory.ButtJointClientFactory;
import com.iaa.datacenter.module.buttjoint.service.ekuaibao.account.EkuaibaoPayService;
import com.iaa.datacenter.module.buttjoint.service.ekuaibao.custom.EkuaibaoCustomService;
import com.iaa.datacenter.module.buttjoint.service.ekuaibao.feetype.EkuaibaoFeeTypeService;
import com.iaa.datacenter.module.buttjoint.service.ekuaibao.subject.EkuaibaoSubjectService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * 同步合思自定义档案任务
 */
@Component
@Slf4j
public class ReadCustomJob implements JobHandler {
    @Resource
    private ButtJointClientFactory buttJointClientFactory;
    @Resource
    private EkuaibaoCustomService ekuaibaoCustomService;
    @Resource
    private EkuaibaoFeeTypeService ekuaibaoFeeTypeService;
    @Resource
    private EkuaibaoSubjectService ekuaibaoSubjectService;
    @Resource
    private EkuaibaoPayService ekuaibaoPayService;
    @Override
    public String execute(String param) throws Exception {
        ButtJointClientService ekuaibao = buttJointClientFactory.getClient(ButtJointNameConstants.EKUAIBAO);
        /**
         * 获取合思自定义档案
         */
        HashMap<String, Object> query = new HashMap<>();
        int start=0,count;
        query.put("count",100);
        List<EkuaibaoCustomDO> dataList = new ArrayList<>();
        do {
            query.put("start",start);
            JSONObject result = ekuaibao.get(EkuaibaoApiConstant.DIMENSIONS, query);
            count=result.getInteger("count");
            dataList.addAll(result.getJSONArray("items").toJavaList(EkuaibaoCustomDO.class));
            start+=100;
        }while (start<=count);
        //防止自定部门被覆盖
        List<EkuaibaoCustomDO> notCustomDept = dataList.stream().filter(item -> !item.getDimensionId().equals("ID01AdKeqenyyz:自定部门")).toList();
        for (EkuaibaoCustomDO ekuaibaoCustomDO : dataList.stream().filter(item -> item.getDimensionId().equals("ID01AdKeqenyyz:自定部门")).toList()) {
            if (!ekuaibaoCustomService.exist(ekuaibaoCustomDO.getId())) {
                ekuaibaoCustomService.save(ekuaibaoCustomDO);
            }else{
                EkuaibaoCustomDO updateDD = ekuaibaoCustomService.getById(ekuaibaoCustomDO.getId());
                updateDD.setName(ekuaibaoCustomDO.getName());
                ekuaibaoCustomService.updateById(updateDD);
            }
        }
        ekuaibaoCustomService.saveOrUpdateBatch(notCustomDept,true);
        log.info("同步合思自定义档案成功，数据条数：{}",dataList.size());
        // 获取合思费用类型
        JSONObject feeResult = ekuaibao.get(EkuaibaoApiConstant.FEE_TYPE, new HashMap<>());
        List<EkuaibaoFeeTypeDO> feeTypeList = feeResult.getJSONArray("items").toJavaList(EkuaibaoFeeTypeDO.class);
        if(CollUtil.isNotEmpty(feeTypeList)){
            ekuaibaoFeeTypeService.saveOrUpdateBatch(feeTypeList,true);
        }
        feeTypeList.forEach(item->{
            Boolean exist = ekuaibaoSubjectService.exist(item.getId());
            if(!exist){
                ekuaibaoSubjectService.save(EkuaibaoSubjectDO.builder().id(item.getId()).name(item.getName()).build());
            }
        });
        log.info("同步合思费用类型成功，数据条数：{}",feeTypeList.size());
        //获取合思付款账号
        JSONObject accountResult = ekuaibao.get(EkuaibaoApiConstant.PAY_ACCOUNT, new HashMap<>());
        List<EkuaibaoPayDO> accountList = accountResult.getJSONArray("items").toJavaList(EkuaibaoPayDO.class);
        ekuaibaoPayService.saveOrUpdateBatch(accountList,true);
        log.info("同步合思付款账户成功，数据条数：{}",accountList.size());
        return "任务执行成功";
    }
}
