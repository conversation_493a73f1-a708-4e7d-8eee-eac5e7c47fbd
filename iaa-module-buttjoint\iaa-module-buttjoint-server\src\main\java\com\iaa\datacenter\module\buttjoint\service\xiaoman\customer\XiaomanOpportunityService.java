package com.iaa.datacenter.module.buttjoint.service.xiaoman.customer;

import com.anwen.mongo.service.IService;
import com.iaa.datacenter.framework.common.pojo.PageResult;
import com.iaa.datacenter.module.buttjoint.controller.admin.xiaoman.report.vo.XiaomanCustomerDevelopmentPageReqVO;
import com.iaa.datacenter.module.buttjoint.controller.admin.xiaoman.report.vo.XiaomanCustomerDevelopmentVO;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.xiaoman.XiaomanOpportunityDO;

/**
 * 小满商机服务
 */
public interface XiaomanOpportunityService extends IService<XiaomanOpportunityDO> {

    /**
     * 获取一段时间的商机信息列表，输出小满客户开发作战表
     * @return
     */
    PageResult<XiaomanCustomerDevelopmentVO> getCustomerDevelopmentPage(XiaomanCustomerDevelopmentPageReqVO pageReqVO);
}
