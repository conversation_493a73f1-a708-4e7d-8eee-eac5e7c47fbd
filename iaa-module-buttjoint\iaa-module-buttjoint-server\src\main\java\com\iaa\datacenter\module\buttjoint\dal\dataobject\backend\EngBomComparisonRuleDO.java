package com.iaa.datacenter.module.buttjoint.dal.dataobject.backend;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.iaa.datacenter.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;

import java.util.List;

@Data
@TableName(value = "eng_bom_comparison_rule", autoResultMap = true)
public class EngBomComparisonRuleDO extends BaseDO {

    @TableId
    private Long id;
    /**
     * 监控物料类型
     */
    private String watchType;
    /**
     * 消息推送人员
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<Long> pushPersonnel;
}
