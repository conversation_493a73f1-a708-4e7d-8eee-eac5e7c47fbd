package com.iaa.datacenter.module.buttjoint.dal.dataobject.backend;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.iaa.datacenter.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;

import java.time.LocalDateTime;

@TableName("eng_bom_comparison")
@Data
public class EngBomComparisonDO extends BaseDO {

    @TableId
    private Long id;
    /**
     * bom名称
     */
    private String bomName;
    /**
     * 归档事件
     */
    private LocalDateTime frozenTime;
    /**
     * 差异信息
     */
    private String diffMessage;
}
