package com.iaa.datacenter.module.buttjoint.dal.dataobject.ekuaibao;

import com.anwen.mongo.annotation.ID;
import com.anwen.mongo.annotation.collection.CollectionLogic;
import com.anwen.mongo.annotation.collection.CollectionName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
/**
 * 合思部门表
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@CollectionName("ekuaibao_dept")
public class EkuaibaoDeptDO {
    @ID
    private String id;

    @CollectionLogic
    private Boolean active;

    private String code;

    private String createTime;

    private List<String> erpCode;

    private String name;

    private String parentId;

    private String updateTime;
}
