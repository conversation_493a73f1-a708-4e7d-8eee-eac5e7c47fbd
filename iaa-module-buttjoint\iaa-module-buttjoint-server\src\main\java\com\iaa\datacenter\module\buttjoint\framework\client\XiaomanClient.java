package com.iaa.datacenter.module.buttjoint.framework.client;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.http.*;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.iaa.datacenter.framework.buttjoint.annotation.ClientIdentifier;
import com.iaa.datacenter.framework.buttjoint.config.ButtJointConfig;
import com.iaa.datacenter.framework.buttjoint.config.ButtJointProperties;
import com.iaa.datacenter.framework.buttjoint.constant.ButtJointNameConstants;
import com.iaa.datacenter.framework.buttjoint.dataobject.ButtJointAuthorization;
import com.iaa.datacenter.framework.buttjoint.service.ButtJointClientService;
import com.iaa.datacenter.framework.common.util.json.JsonUtils;
import com.iaa.datacenter.module.buttjoint.framework.constant.XiaomanApiConstant;
import com.iaa.datacenter.module.buttjoint.service.buttjointauth.ButtJointAuthService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.*;

@Service
@Slf4j
@ClientIdentifier(name= ButtJointNameConstants.XIAOMAN)
public class XiaomanClient implements ButtJointClientService {

    @Resource
    private ButtJointProperties buttJointProperties;
    @Resource
    private ButtJointAuthService buttJointAuthService;

    @Override
    public JSONObject auth() {
        ButtJointAuthorization auth = buttJointAuthService.getById(ButtJointNameConstants.XIAOMAN);
        if(Objects.nonNull(auth)&&Optional.ofNullable(auth.getAuthorization()).orElse(new JSONObject()).containsKey("access_token")){
            // 判断是否过期
            if(auth.getExpireTime()>System.currentTimeMillis()){
                return auth.getAuthorization();
            }else{
                return refresh(auth).getAuthorization();
            }
        }else{
            return access().getAuthorization();
        }
    }

    /**
     * 刷新token
     * @return
     */
    private ButtJointAuthorization refresh(ButtJointAuthorization historyAuth){
        ButtJointConfig xiaomanConfig = buttJointProperties.getType().get(ButtJointNameConstants.XIAOMAN);
        HashMap<String,Object> body = new HashMap<>();
        body.put("grant_type","refresh_token");
        body.put("client_id",xiaomanConfig.getAppKey());
        body.put("client_secret",xiaomanConfig.getAppSecret());
        body.put("refresh_token",historyAuth.getAuthorization().getString("refresh_token"));
        return getButtJointAuthorization(body,XiaomanApiConstant.REFRESH_TOKEN);
    }

    /**
     * 初次获取授权
     * @return
     */
    private ButtJointAuthorization access(){
        ButtJointConfig xiaomanConfig = buttJointProperties.getType().get(ButtJointNameConstants.XIAOMAN);
        HashMap<String,Object> body = new HashMap<>();
        body.put("grant_type","password");
        body.put("client_id",xiaomanConfig.getAppKey());
        body.put("client_secret",xiaomanConfig.getAppSecret());
        body.put("scope","product company opportunity invoices lead user");
        body.put("username",xiaomanConfig.getUserCode());
        body.put("password",xiaomanConfig.getUserPwd());
        return getButtJointAuthorization(body,XiaomanApiConstant.GET_TOKEN);
    }

    /**
     * 获取授权并保存到本地
     * @param body
     * @param url
     * @return
     */
    private ButtJointAuthorization getButtJointAuthorization(HashMap<String, Object> body,String url) {
        JSONObject token = getToken(body, url);
        ButtJointAuthorization auth = ButtJointAuthorization.builder()
                .id(ButtJointNameConstants.XIAOMAN)
                .authorization(token)
                .expireTime(System.currentTimeMillis()+(Optional.ofNullable(token.getLong("expires_in")).orElse(28800L) *1000))
                .build();
        buttJointAuthService.saveOrUpdate(auth,true);
        return auth;
    }

    /**
     * 获取授权
     * @param body
     * @return
     */
    private JSONObject getToken(HashMap<String,Object> body,String url){
        try (HttpResponse response = HttpUtil.createPost(buttJointProperties.getType().get(ButtJointNameConstants.XIAOMAN).getUrl()+url)
                .contentType(ContentType.FORM_URLENCODED.getValue())
                .body(JsonUtils.toJsonString(body))
                .execute()){
            return JsonUtils.parseObject(response.body(),JSONObject.class);
        }
    }

    @Override
    public JSONObject auth(String code) {
        return null;
    }

    @Override
    public JSONObject readObj(String url, Method type, Map<String, Object> query, Map<String, Object> body, String... code) {

        HttpRequest request = HttpUtil.createRequest(type, HttpUtil.urlWithFormUrlEncoded(buttJointProperties.getType().get(ButtJointNameConstants.XIAOMAN).getUrl()+url,query, StandardCharsets.UTF_8));
        JSONObject auth = auth();
        request.addHeaders(new HashMap<String,String>(){{
            put("Authorization",auth.getString("access_token"));
        }});
        if(CollUtil.isNotEmpty(body)){
            request.contentType(ContentType.JSON.getValue()).body(JSON.toJSONString(body));
        }
        try(HttpResponse response = request.execute()){
            return JsonUtils.parseObject(response.body(),JSONObject.class);
        }
    }

    @Override
    public JSONObject readObj(String url, Method type, Map<String, Object> query, ArrayList<Object> body, String... code) {
        return null;
    }
}
