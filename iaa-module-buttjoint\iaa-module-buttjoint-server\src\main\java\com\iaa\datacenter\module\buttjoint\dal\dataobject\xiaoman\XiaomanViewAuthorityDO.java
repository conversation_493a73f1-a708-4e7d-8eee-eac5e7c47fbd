package com.iaa.datacenter.module.buttjoint.dal.dataobject.xiaoman;

import com.anwen.mongo.annotation.ID;
import com.anwen.mongo.annotation.collection.CollectionName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 小满用户查看权限范围
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@CollectionName("xiaoman_view_authority")
public class XiaomanViewAuthorityDO {
    /**
     * 系统用户ID
     */
    @ID
    private Long backend_user_id;
    /**
     * 小满用户ID
     */
    private List<String> xiaoman_user_ids;

}
