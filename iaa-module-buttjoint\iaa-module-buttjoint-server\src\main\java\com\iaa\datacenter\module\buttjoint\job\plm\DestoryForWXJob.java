package com.iaa.datacenter.module.buttjoint.job.plm;

import com.iaa.datacenter.framework.quartz.core.handler.JobHandler;
import com.iaa.datacenter.module.buttjoint.controller.admin.plm.vo.SoapClientRespVO;
import com.iaa.datacenter.module.buttjoint.service.plm.doSoap.SoapClientService;
import jakarta.annotation.Resource;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 每天0点执行，删除PLM中用户下载的全部图纸
 */
@Component("destoryForWXJob")
public class DestoryForWXJob implements JobHandler {

    @Resource
    private SoapClientService soapClientService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public String execute(String param) throws Exception {
        SoapClientRespVO soapClientRespVO= new SoapClientRespVO();
        soapClientRespVO.setMethodName("destoryForWX");
        soapClientRespVO.setUuid("1");
        soapClientService.destoryOrWx(soapClientRespVO);
        return "PLM用户下载PDF数据删除成功！";
    }
}
