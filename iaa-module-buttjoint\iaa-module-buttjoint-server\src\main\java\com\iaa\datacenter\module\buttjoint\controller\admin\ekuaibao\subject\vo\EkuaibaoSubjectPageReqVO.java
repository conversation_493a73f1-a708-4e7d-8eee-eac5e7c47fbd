package com.iaa.datacenter.module.buttjoint.controller.admin.ekuaibao.subject.vo;

import com.iaa.datacenter.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 科目查询 Request VO")
@Data
public class EkuaibaoSubjectPageReqVO extends PageParam {

    @Schema(description = "费用类型")
    private String name;

    @Schema(description = "费用编码")
    private String code;
}
