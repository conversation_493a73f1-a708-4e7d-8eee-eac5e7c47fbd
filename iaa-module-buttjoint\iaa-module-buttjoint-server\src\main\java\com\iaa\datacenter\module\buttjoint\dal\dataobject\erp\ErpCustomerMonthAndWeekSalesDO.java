package com.iaa.datacenter.module.buttjoint.dal.dataobject.erp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * ERP 客户月和周销售额
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ErpCustomerMonthAndWeekSalesDO {
    /**
     * 客户编码
     */
    private String customerCode;
    /**
     * 客户名称
     */
    private String customerName;
    /**
     * 年度
     */
    private String businessYear;
    /**
     * 月份
     */
    private String businessMonth;
    /**
     * 周
     */
    private Integer businessWeek;
    /**
     * 销量
     */
    private Integer totalQty;
    /**
     * 销售额
     */
    private Double totalSales;
}
