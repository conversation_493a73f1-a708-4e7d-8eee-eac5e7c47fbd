package com.iaa.datacenter.module.buttjoint.service.landray.flow;

import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.iaa.datacenter.module.buttjoint.api.ekp.LandrayFlowApi;
import com.iaa.datacenter.module.buttjoint.api.ekp.dto.LandrayFlowQueryDTO;
import com.iaa.datacenter.module.buttjoint.api.ekp.dto.LandrayFlowDTO;
import com.iaa.datacenter.module.buttjoint.dal.mapper.landray.LandaryFlowMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Service
public class LandrayFlowServiceImpl implements LandrayFlowApi {

    @Resource
    private LandaryFlowMapper landaryFlowMapper;


    @Override
    public List<LandrayFlowDTO> selectFlowList(LandrayFlowQueryDTO queryDTO) {
        QueryWrapper<LandrayFlowDTO> wrapper = new QueryWrapper<>();
        wrapper.eq("km_review_template.fd_category_id","17b0abf5718267facc812234e2d9f5d6");
        wrapper.gt("km_review_main.doc_create_time","2025-03-31");
        wrapper.eq("km_review_main.doc_delete_flag",0);
        wrapper.notIn("km_review_main.doc_status","00","10","11","40");
        if(ArrayUtil.isNotEmpty(queryDTO.getPlanDate())){
            wrapper.apply("km_review_main.fd_id in (select * from dbo.GetXmlFieldValueFromXML({0},{1}))",
                    queryDTO.getPlanDate()[0].atStartOfDay(ZoneId.of("Asia/Shanghai")) // 指定时区
                            .toInstant()
                            .toEpochMilli(),
                    queryDTO.getPlanDate()[1].atStartOfDay(ZoneId.of("Asia/Shanghai")) // 指定时区
                            .toInstant()
                            .toEpochMilli()
            );
        }
        if(ArrayUtil.isNotEmpty(queryDTO.getCreateTime())){
            wrapper.and(p->
                    p.between("km_review_main.doc_create_time",queryDTO.getCreateTime()[0].format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),queryDTO.getCreateTime()[1].format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                    .or()
                    .between("km_review_main.doc_publish_time",queryDTO.getCreateTime()[0].format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),queryDTO.getCreateTime()[1].format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
            );
        }
        return landaryFlowMapper.selectFlowList(wrapper);
    }
}
