package com.iaa.datacenter.module.buttjoint.controller.admin.erp.custom.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ExcelIgnoreUnannotated
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "管理后台 - ERP 料品 Request VO")
public class ErpItemRespVO {

    @Schema(description = "品号")
    private String itemCode;

    @Schema(description = "品名")
    private String itemName;

    @Schema(description = "规格")
    private String spec;

}
