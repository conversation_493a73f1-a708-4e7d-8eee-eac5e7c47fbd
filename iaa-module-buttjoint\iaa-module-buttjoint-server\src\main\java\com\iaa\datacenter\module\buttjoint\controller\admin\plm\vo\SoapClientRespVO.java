package com.iaa.datacenter.module.buttjoint.controller.admin.plm.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - PLM 调用接口条件 VO")
@Data
public class SoapClientRespVO {
    @Schema(description = "请求地址")
    private String plmWsdlUrl;

    @Schema(description = "返回路径前缀")
    private String prefix;

    @Schema(description = "方法名")
    private String methodName;

    @Schema(description = "文档编号")
    private String docId;

    @Schema(description = "文档版本")
    private String docVer;

    @Schema(description = "文档名称")
    private String docName;

    @Schema(description = "文档格式")
    private String format;

    @Schema(description = "用户ID")
    private String uuid;

}
