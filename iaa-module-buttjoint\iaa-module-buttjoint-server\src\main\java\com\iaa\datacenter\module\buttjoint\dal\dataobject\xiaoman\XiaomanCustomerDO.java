package com.iaa.datacenter.module.buttjoint.dal.dataobject.xiaoman;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.anwen.mongo.annotation.ID;
import com.anwen.mongo.annotation.collection.CollectionName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@CollectionName("xiaoman_customer")
public class XiaomanCustomerDO implements Serializable {

    @ID
    private String company_id;
    /**
     * 客户编号
     */
    private String serial_id;
    /**
     * 创建人
     */
    private JSONArray user_id;
    /**
     * 创建人信息
     */
    private JSONArray owner;
    /**
     * 订单创建人
     */
    private Long create_user;

    private JSONObject last_owner_info;
    /**
     * 公海分组
     */
    private Long pool_id;
    /**
     * 公海分组名称
     */
    private String pool_name;

    private String private_time;

    private String public_time;

    private Long release_count;

    private String edit_time;
    /**
     * 来源线索
     */
    private Long main_lead_id;
    /**
     * 线索名称
     */
    private String lead_name;
    /**
     * 客户来源
     */
    private String origin;
    /**
     * 客户来源名称
     */
    private String origin_name;
    /**
     * 标签
     */
    private JSONArray tag;

    private JSONArray client_tag_list;
    /**
     * 来源明细
     */
    private JSONObject source_detail;

    private String update_time;

    private String create_time;
    /**
     * 客户名称
     */
    private String name;
    /**
     * 客户简称
     */
    private String short_name;
    /**
     * 客户来源
     */
    private JSONArray origin_list;
    /**
     * 分类ID
     */
    private JSONArray category_ids;
    /**
     * 城市
     */
    private String city;
    /**
     * 业务类型
     */
    private String biz_type;
    /**
     * 意向等级
     */
    private Long intention_level;
    /**
     * 年采购额
     */
    private Long annual_procurement;
    /**
     * 省
     */
    private String province;
    /**
     * 规模
     */
    private Long scale_id;
    /**
     * 国家地区
     */
    private String country;
    /**
     * 时区
     */
    private String timezone;
    /**
     * 客户星级
     */
    private Long star;
    /**
     * 客户分组
     */
    private String group_id;
    /**
     * 客户阶段
     */
    private String trail_status;
    /**
     * 主页
     */
    private String homepage;
    /**
     * 传真
     */
    private String fax;
    /**
     * 电话区号
     */
    private String tel_area_code;
    /**
     * 电话
     */
    private String tel;
    /**
     * 地址
     */
    private String address;
    /**
     * 备注
     */
    private String remark;
    /**
     * 客户联系人
     */
    private JSONArray customers;
    /**
     * 客户画像
     */
    @JSONField(name = "9519404599741")
    private String customer_portraits;
    /**
     * ERP系统下单名称
     */
    @JSONField(name = "19711714890")
    private String erp_order_name;
    /**
     * ERP 客户编码
     */
    @JSONField(name = "22579537764692")
    private String erp_code;
    /**
     * 是否竟对合作客户
     */
    @JSONField(name = "12964347737184")
    private String competitors_customer;
    /**
     * 合作阶段
     */
    @JSONField(name = "34130234685541")
    private String cooperation_stage;
    /**
     * 竞争对手
     */
    @JSONField(name = "12964404190898")
    private List<String> compertitors;
    /**
     * 大洲区域
     */
    @JSONField(name = "16717337964103")
    private String region;
    /**
     * 客户集训的产品分类
     */
    @JSONField(name = "20409171571252")
    private String consulted_product;
    /**
     * 营收规模评估
     */
    @JSONField(name = "34130602953412")
    private String revenue_scale;
    /**
     * 香氛产品规模评估
     */
    @JSONField(name = "34131082912009")
    private String fragrance_revenue_scale;
    /**
     * 预估年销售额(万元)
     */
    @JSONField(name = "34131917786240")
    private String estimated_annual_sales;
    /**
     * 客户画像 旧版本
     */
    @JSONField(name = "2012254834712")
    private String customer_portraits_old;
    /**
     * 客户画像 补充
     */
    @JSONField(name = "2055297153626")
    private String customer_portraits_append;
    /**
     * 客户销售渠道
     */
    @JSONField(name="*************")
    private String customer_channel_1;
    @JSONField(name="*************")
    private String customer_channel_2;
    /**
     * 地区/州（美国）
     */
    @JSONField(name = "*************")
    private String state;

    @JSONField(name = "**************")
    private String city_1;
    /**
     * 阿里旺旺
     */
    @JSONField(name = "*************")
    private String alipay_account;
    /**
     * 竞品型号
     */
    @JSONField(name = "**************")
    private String competitive_models;
    /**
     * 公司网址1
     */
    @JSONField(name = "**************")
    private String web_url_1;
    @JSONField(name = "**************")
    private String web_url_2;
    @JSONField(name = "**************")
    private String web_url_3;
    /**
     * 竟对精油类产品描述
     */
    @JSONField(name = "**************")
    private String untoEssentialOilRemark;
    /**
     * 竟对机器类产品描述
     */
    @JSONField(name="**************")
    private String untoMachineRemark;
    /**
     * 相关性产品描述
     */
    @JSONField(name = "**************")
    private String relevantProductRemark;
    /**
     * 合作机器类产品描述
     */
    @JSONField(name = "**************")
    private String cooperationMachineRemark;
    /**
     * 合作精油类产品描述
     */
    @JSONField(name = "**************")
    private String cooperationEssentialOilRemark;
    /**
     * 客户性质
     */
    @JSONField(name = "34569534861548")
    private String nature;

    /**
     * 2024 年预测销售额
     */
    @JSONField(name = "35217407611495")
    private Double tztfourPredictionSales;

    /**
     * 2025 年预测销售额
     */
    @JSONField(name = "35217438011612")
    private Double tztfivePredictionSales;
    /**
     * 国家名称
     */
    private String country_name;
}
