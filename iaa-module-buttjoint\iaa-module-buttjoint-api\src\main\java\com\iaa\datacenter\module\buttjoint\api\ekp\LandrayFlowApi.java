package com.iaa.datacenter.module.buttjoint.api.ekp;

import com.iaa.datacenter.module.buttjoint.api.ekp.dto.LandrayFlowQueryDTO;
import com.iaa.datacenter.module.buttjoint.api.ekp.dto.LandrayFlowDTO;

import java.util.List;

/**
 * 蓝凌OA数据
 */
public interface LandrayFlowApi {

    /**
     * 查询流程列表
     * @param queryDTO
     * @return
     */
    List<LandrayFlowDTO> selectFlowList(LandrayFlowQueryDTO queryDTO);
}
