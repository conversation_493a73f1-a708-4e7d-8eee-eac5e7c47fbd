package com.iaa.datacenter.module.buttjoint.service.xiaoman.customer;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.anwen.mongo.conditions.query.QueryWrapper;
import com.iaa.datacenter.framework.common.core.KeyValue;
import com.iaa.datacenter.framework.common.pojo.PageResult;
import com.iaa.datacenter.framework.common.util.collection.CollectionUtils;
import com.iaa.datacenter.framework.web.core.util.WebFrameworkUtils;
import com.iaa.datacenter.module.buttjoint.controller.admin.xiaoman.report.vo.XiaomanOrderPageReqVO;
import com.iaa.datacenter.module.buttjoint.controller.admin.xiaoman.report.vo.XiaomanOrderRespVO;
import com.iaa.datacenter.module.buttjoint.controller.admin.xiaoman.report.vo.XiaomanOrderRespVO.XiaomanOrderTotalVO;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.erp.ErpCustomerMonthAndWeekSalesDO;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.erp.ErpCustomerRcvDO;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.erp.ErpCustomerYearSalesDO;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.xiaoman.XiaomanCustomerDO;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.xiaoman.XiaomanOpportunityDO;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.xiaoman.XiaomanUserDO;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.xiaoman.XiaomanViewAuthorityDO;
import com.iaa.datacenter.module.buttjoint.dal.mapper.erp.ErpRecMapper;
import com.iaa.datacenter.module.buttjoint.dal.mapper.erp.ErpSoMapper;
import com.iaa.datacenter.module.buttjoint.service.xiaoman.user.XiaomanUserService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.net.http.HttpResponse;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class XiaomanOrderServiceImpl implements XiaomanOrderService {

    @Resource
    private XiaomanCustomerService xiaomanCustomerService;
    @Resource
    private XiaomanOpportunityService xiaomanOpportunityService;
    @Resource
    private ErpSoMapper erpSoMapper;
    @Resource
    private ErpRecMapper erpRecMapper;

    @Resource
    private XiaomanViewAuthorityService xiaomanViewAuthorityService;

    @Resource
    private XiaomanUserService xiaomanUserService;


    @Override
    public PageResult<XiaomanOrderRespVO> getXiaomanOrderPage(XiaomanOrderPageReqVO pageReqVO) {
        // 查询客户信息
        QueryWrapper<XiaomanCustomerDO> wrapper = new QueryWrapper<>();
        // 根据登录用户权限筛选客户信息
        if (CollUtil.isEmpty(pageReqVO.getSalesperson()) && CollUtil.isEmpty(pageReqVO.getDepartmentName()) && WebFrameworkUtils.getLoginUserId() != 1) {
            XiaomanViewAuthorityDO authority = xiaomanViewAuthorityService.getById(WebFrameworkUtils.getLoginUserId());
            if (authority == null) {
                return new PageResult<>(new ArrayList<>(), 0L);
            }
            wrapper.in(XiaomanCustomerDO::getUser_id, authority.getXiaoman_user_ids());
        }
        // 根据销售人员筛选客户信息
        wrapper.in(CollUtil.isNotEmpty(pageReqVO.getSalesperson()), XiaomanCustomerDO::getUser_id, pageReqVO.getSalesperson());
        // 根据部门名称筛选客户信息
        if (CollUtil.isNotEmpty(pageReqVO.getDepartmentName())) {
            List<XiaomanUserDO> list = xiaomanUserService.list(new QueryWrapper<XiaomanUserDO>().in(XiaomanUserDO::getDepartment_id, pageReqVO.getDepartmentName()));
            wrapper.in(XiaomanCustomerDO::getUser_id, list.stream().map(XiaomanUserDO::getUser_id).collect(Collectors.toSet()));
        }

        // 根据客户名称筛选
        wrapper.like(StrUtil.isNotEmpty(pageReqVO.getCustomerName()), XiaomanCustomerDO::getName, pageReqVO.getCustomerName());
        // 根据国家名称筛选
        wrapper.like(StrUtil.isNotEmpty(pageReqVO.getCountryName()), XiaomanCustomerDO::getCountry_name, pageReqVO.getCountryName());
        // 根据地区或省份筛选
        if (StrUtil.isNotEmpty(pageReqVO.getRegionOrProvince())) {
            wrapper.or(p -> p.like(XiaomanCustomerDO::getRegion, pageReqVO.getRegionOrProvince()).like(XiaomanCustomerDO::getProvince, pageReqVO.getRegionOrProvince()));
        }
        // 根据城市名称筛选
        wrapper.like(StrUtil.isNotEmpty(pageReqVO.getCity()), XiaomanCustomerDO::getCity, pageReqVO.getCity());
        // 根据客户画像筛选
        wrapper.in(CollUtil.isNotEmpty(pageReqVO.getCustomerPortraits()), XiaomanCustomerDO::getCustomer_portraits, pageReqVO.getCustomerPortraits());
        // 根据客户性质筛选
        wrapper.like(StrUtil.isNotEmpty(pageReqVO.getNature()), XiaomanCustomerDO::getNature, pageReqVO.getNature());
        // 根据客户主页URL筛选
        wrapper.like(StrUtil.isNotEmpty(pageReqVO.getHomepage()), XiaomanCustomerDO::getHomepage, pageReqVO.getHomepage());
        // 根据年收入规模筛选
        wrapper.in(CollUtil.isNotEmpty(pageReqVO.getRevenueScale()), XiaomanCustomerDO::getRevenue_scale, pageReqVO.getRevenueScale());
        // 根据香精年收入规模筛选
        wrapper.in(CollUtil.isNotEmpty(pageReqVO.getFragranceRevenueScale()), XiaomanCustomerDO::getFragrance_revenue_scale, pageReqVO.getFragranceRevenueScale());
        // 根据跟踪状态筛选
        wrapper.in(CollUtil.isNotEmpty(pageReqVO.getTrailStatus()), XiaomanCustomerDO::getTrail_status, pageReqVO.getTrailStatus());
        // 选择需要展示的字段
        wrapper.projectDisplay(XiaomanCustomerDO::getCompany_id,
                XiaomanCustomerDO::getName,
                XiaomanCustomerDO::getCountry_name,
                XiaomanCustomerDO::getProvince,
                XiaomanCustomerDO::getRegion,
                XiaomanCustomerDO::getCity,
                XiaomanCustomerDO::getUser_id,
                XiaomanCustomerDO::getErp_code,
                XiaomanCustomerDO::getCustomer_portraits,
                XiaomanCustomerDO::getNature,
                XiaomanCustomerDO::getHomepage,
                XiaomanCustomerDO::getRevenue_scale,
                XiaomanCustomerDO::getFragrance_revenue_scale,
                XiaomanCustomerDO::getEstimated_annual_sales,
                XiaomanCustomerDO::getUntoMachineRemark,
                XiaomanCustomerDO::getTrail_status,
                XiaomanCustomerDO::getUntoEssentialOilRemark,
                XiaomanCustomerDO::getRelevantProductRemark);
        // 按更新时间降序排序
        wrapper.orderByDesc(XiaomanCustomerDO::getUpdate_time);
        // 根据视图类型筛选
        if (pageReqVO.getViewType()) {
            List<XiaomanOpportunityDO> list = xiaomanOpportunityService.list(
                    new QueryWrapper<XiaomanOpportunityDO>()
                            .between(XiaomanOpportunityDO::getAccount_date, pageReqVO.getSalesTime()[0], pageReqVO.getSalesTime()[1], true)
                            .projectDisplay(XiaomanOpportunityDO::getCompany_id,XiaomanOpportunityDO::getAmount_rmb)
            );
            Set<String> companyIds = list.stream().filter(p->Objects.nonNull(p.getAmount_rmb())).map(XiaomanOpportunityDO::getCompany_id).collect(Collectors.toSet());
            wrapper.in(XiaomanCustomerDO::getCompany_id, companyIds);
        }
        // 分页查询客户信息
        com.anwen.mongo.model.PageResult<XiaomanCustomerDO> page = new com.anwen.mongo.model.PageResult<>();
        if (pageReqVO.getPageSize() == -1) {
            List<XiaomanCustomerDO> list = xiaomanCustomerService.list(wrapper);
            page.setContentData(list);
            page.setTotalSize(list.size());
        } else {
            page = xiaomanCustomerService.page(wrapper, pageReqVO.getPageNo(), pageReqVO.getPageSize());
        }
        // 获取用户名映射
        Map<String, XiaomanUserDO> userNameMap = xiaomanCustomerService.getUserNameMap(page.getContentData());
        // 查询客户销售额,回款信息
        Set<String> customers = page.getContentData().stream().map(p -> p.getErp_code().trim()).filter(StrUtil::isNotEmpty).collect(Collectors.toSet());
        Map<String, List<ErpCustomerYearSalesDO>> yearSalesMap = new HashMap<>();
        Map<String, List<ErpCustomerMonthAndWeekSalesDO>> monthAndWeekSalesMap = new HashMap<>();
        Map<String, List<ErpCustomerRcvDO>> rcvMap = new HashMap<>();

        int lastDay = DateUtil.parseDate(pageReqVO.getSalesTime()[1]).getLastDayOfMonth();
        if (CollUtil.isNotEmpty(customers)) {
            List<ErpCustomerYearSalesDO> customerYearSales = erpSoMapper.getCustomerYearSales(customers);
            yearSalesMap.putAll(customerYearSales.stream().collect(Collectors.groupingBy(ErpCustomerYearSalesDO::getCustomerCode)));

            List<ErpCustomerMonthAndWeekSalesDO> customerMonthAndWeekSales = erpSoMapper.getCustomerMonthAndWeekSales(customers, new String[]{
                    pageReqVO.getSalesTime()[0].substring(0, pageReqVO.getSalesTime()[0].length() - 2) + "01",
                    pageReqVO.getSalesTime()[1].substring(0, pageReqVO.getSalesTime()[1].length() - 2) + (lastDay < 10 ? "0" + lastDay : lastDay)
            });
            monthAndWeekSalesMap.putAll(customerMonthAndWeekSales.stream().collect(Collectors.groupingBy(ErpCustomerMonthAndWeekSalesDO::getCustomerCode)));

            List<ErpCustomerRcvDO> rcvList = erpRecMapper.getCustomerRcv(customers, new String[]{
                    pageReqVO.getSalesTime()[0].substring(0, pageReqVO.getSalesTime()[0].length() - 2) + "01",
                    pageReqVO.getSalesTime()[1].substring(0, pageReqVO.getSalesTime()[1].length() - 2) + (lastDay < 10 ? "0" + lastDay : lastDay)
            });
            rcvMap.putAll(rcvList.stream().collect(Collectors.groupingBy(ErpCustomerRcvDO::getCustomerCode)));
        }

        // 查询商机相关
        String startTime = "2024-01-01 00:00:00";
        String endTime = "2025-12-31 23:59:59";
        if (DateUtil.parseDate(pageReqVO.getSalesTime()[0]).isBefore(DateUtil.parseDate(startTime))) {
            startTime = pageReqVO.getSalesTime()[0].substring(0, pageReqVO.getSalesTime()[0].length() - 2) + "01";
        }

        if (DateUtil.parseDate(pageReqVO.getSalesTime()[1]).isAfter(DateUtil.parseDate(endTime))) {
            endTime = pageReqVO.getSalesTime()[1].substring(0, pageReqVO.getSalesTime()[1].length() - 2) + (lastDay < 10 ? "0" + lastDay : lastDay);
        }
        QueryWrapper<XiaomanOpportunityDO> opportunityWrapper = new QueryWrapper<>();
        opportunityWrapper.in(XiaomanOpportunityDO::getCompany_id, page.getContentData().stream().map(XiaomanCustomerDO::getCompany_id).collect(Collectors.toSet()))
                .between(XiaomanOpportunityDO::getAccount_date, startTime, endTime, true);
        opportunityWrapper.projectDisplay(XiaomanOpportunityDO::getCompany_id, XiaomanOpportunityDO::getAccount_date, XiaomanOpportunityDO::getAmount_rmb, XiaomanOpportunityDO::getExternal_field_data);
        List<XiaomanOpportunityDO> opportunityList = xiaomanOpportunityService.list(opportunityWrapper);
        Map<String, List<XiaomanOpportunityDO>> opportunityCompanyMap = opportunityList.stream().collect(Collectors.groupingBy(XiaomanOpportunityDO::getCompany_id));

        // 构建响应对象列表
        List<XiaomanOrderRespVO> list = page.getContentData().stream().map(
                item -> buildXiaomanOrderRespVO(
                        item,
                        userNameMap,
                        yearSalesMap,
                        opportunityCompanyMap,
                        monthAndWeekSalesMap,
                        rcvMap
                )).toList();
        return new PageResult<>(list, page.getTotalSize());
    }

    /**
     * todo 计算订单合计 未完成
     *
     * @param orderList
     * @return
     */
    @Override
    public XiaomanOrderRespVO calculateOrderTotal(List<XiaomanOrderRespVO> orderList) {
        XiaomanOrderRespVO orderTotal = new XiaomanOrderRespVO();
        orderTotal.setSalesperson(List.of("合计"));
        orderTotal.setYearOrderMap(new HashMap<>());
        orderTotal.setOrderTotalList(new HashMap<>());
        orderTotal.setPaymentMap(new HashMap<>());
        for (XiaomanOrderRespVO order : orderList) {
            Optional.ofNullable(order.getYearOrderMap()).orElse(new HashMap<>()).forEach((key, value) -> {
                if (orderTotal.getYearOrderMap().containsKey(key)) {
                    orderTotal.getYearOrderMap().put(key, getNumber(orderTotal.getYearOrderMap().get(key)) + getNumber(value));
                } else {
                    orderTotal.getYearOrderMap().put(key, value);
                }
            });
            orderTotal.setTztfourPredictionSales(getNumber(orderTotal.getTztfourPredictionSales()) + getNumber(order.getTztfourPredictionSales()));
            orderTotal.setTztfourReachingStandard(getNumber(orderTotal.getTztfourReachingStandard()) + getNumber(order.getTztfourReachingStandard()));

            orderTotal.setTztfivePredictionSales(getNumber(orderTotal.getTztfivePredictionSales()) + getNumber(order.getTztfivePredictionSales()));
            orderTotal.setTztfiveReachingStandard(getNumber(orderTotal.getTztfiveReachingStandard()) + getNumber(order.getTztfiveReachingStandard()));

            Optional.ofNullable(order.getOrderTotalList()).orElse(new HashMap<>()).forEach((key, value) -> {
                if (orderTotal.getOrderTotalList().containsKey(key)) {
                    XiaomanOrderTotalVO xiaomanOrderTotalVO = orderTotal.getOrderTotalList().get(key);
                    xiaomanOrderTotalVO.setTotalAmount(getNumber(xiaomanOrderTotalVO.getTotalAmount()) + getNumber(value.getTotalAmount()));
                    xiaomanOrderTotalVO.setTotalQty(getNumber(xiaomanOrderTotalVO.getTotalQty()).intValue() + getNumber(value.getTotalQty()).intValue());
                    xiaomanOrderTotalVO.setForecastAmount(getNumber(xiaomanOrderTotalVO.getForecastAmount()) + getNumber(value.getForecastAmount()));
                    xiaomanOrderTotalVO.setForecastQty(getNumber(xiaomanOrderTotalVO.getForecastQty()).intValue() + getNumber(value.getForecastQty()).intValue());
                    xiaomanOrderTotalVO.setOneWeekAmount(getNumber(xiaomanOrderTotalVO.getOneWeekAmount()) + getNumber(value.getOneWeekAmount()));
                    xiaomanOrderTotalVO.setTwoWeekAmount(getNumber(xiaomanOrderTotalVO.getTwoWeekAmount()) + getNumber(value.getTwoWeekAmount()));
                    xiaomanOrderTotalVO.setThreeWeekAmount(getNumber(xiaomanOrderTotalVO.getThreeWeekAmount()) + getNumber(value.getThreeWeekAmount()));
                    xiaomanOrderTotalVO.setFourWeekAmount(getNumber(xiaomanOrderTotalVO.getFourWeekAmount()) + getNumber(value.getFourWeekAmount()));
                } else {
                    orderTotal.getOrderTotalList().put(key, value);
                }
            });

            Optional.ofNullable(order.getPaymentMap()).orElse(new HashMap<>()).forEach((key, value) -> {
                if (orderTotal.getPaymentMap().containsKey(key)) {
                    orderTotal.getPaymentMap().put(key, getNumber(orderTotal.getPaymentMap().get(key)) + getNumber(value));
                } else {
                    orderTotal.getPaymentMap().put(key, value);
                }
            });
        }
        return orderTotal;
    }


    private Double getNumber(Number value) {
        if (value == null) {
            return 0D;
        }
        return value.doubleValue();
    }

    private XiaomanOrderRespVO buildXiaomanOrderRespVO(
            XiaomanCustomerDO xiaomanCustomerDO,
            Map<String, XiaomanUserDO> userNameMap,
            Map<String, List<ErpCustomerYearSalesDO>> yearSalesMap,
            Map<String, List<XiaomanOpportunityDO>> opportunityCompanyMap,
            Map<String, List<ErpCustomerMonthAndWeekSalesDO>> monthAndWeekSalesMap,
            Map<String, List<ErpCustomerRcvDO>> rcvMap) {
        // 用户信息
        List<XiaomanUserDO> users = CollectionUtils.convertList(xiaomanCustomerDO.getUser_id(), item -> userNameMap.getOrDefault(item.toString(), new XiaomanUserDO()));
        // 年销售额
        List<ErpCustomerYearSalesDO> yearSaleList = yearSalesMap.getOrDefault(xiaomanCustomerDO.getErp_code(), new ArrayList<>())
                .stream().sorted(Comparator.comparing(ErpCustomerYearSalesDO::getYear)).toList();
        Map<String, Double> yearSaleMap = yearSaleList.stream().collect(
                Collectors.toMap(ErpCustomerYearSalesDO::getYear, ErpCustomerYearSalesDO::getTotalSales, (existing, replacement) -> existing, LinkedHashMap::new));
        // 月销售额
        List<ErpCustomerMonthAndWeekSalesDO> monthAndWeekSalesList = monthAndWeekSalesMap.getOrDefault(xiaomanCustomerDO.getErp_code(), new ArrayList<>())
                .stream().sorted(Comparator.comparing(ErpCustomerMonthAndWeekSalesDO::getBusinessYear)
                        .thenComparing(ErpCustomerMonthAndWeekSalesDO::getBusinessMonth)).toList();
        // 使用 LinkedHashMap 收集结果并保持插入顺序
        Map<String, List<ErpCustomerMonthAndWeekSalesDO>> monthMap = monthAndWeekSalesList.stream()
                .collect(Collectors.groupingBy(
                        p -> p.getBusinessYear() + "-" + p.getBusinessMonth(),
                        LinkedHashMap::new, // 指定使用 LinkedHashMap
                        Collectors.toList()
                ));
        // 商机
        List<XiaomanOpportunityDO> opportunityList = opportunityCompanyMap.getOrDefault(xiaomanCustomerDO.getCompany_id(), new ArrayList<>());
        Map<String, Double> amountRmbByYear = opportunityList.stream()
                .collect(Collectors.groupingBy(
                        p -> p.getAccount_date().substring(0, 4),
                        Collectors.summingDouble(XiaomanOpportunityDO::getAmount_rmb)
                ));
        Map<String, Double> amountRmbByMonth = opportunityList.stream()
                .collect(Collectors.groupingBy(
                        p -> p.getAccount_date().substring(0, 7),
                        Collectors.summingDouble(XiaomanOpportunityDO::getAmount_rmb)
                ));
        Map<String, Integer> qtyByMonth = opportunityList.stream()
                .collect(Collectors.groupingBy(
                        p -> p.getAccount_date().substring(0, 7),
                        Collectors.summingInt(p -> {
                            if (JSON.toJSONString(p.getExternal_field_data()).startsWith("{")) {
                                JSONObject result = JSONObject.parseObject(JSON.toJSONString(p.getExternal_field_data()));
                                Integer qty = result.getInteger("**************");
                                if (qty == null) qty = 0;
                                return qty;
                            }
                            return 0;
                        })
                ));
        XiaomanOrderRespVO result = XiaomanOrderRespVO.builder()
                .salesperson(users.stream().map(XiaomanUserDO::getChinese_name).toList())
                .departmentName(CollUtil.isEmpty(users) ? "" : users.get(0).getDepartment_name())
                .customerName(CollUtil.isNotEmpty(yearSaleList) ? yearSaleList.get(0).getCustomerName() : xiaomanCustomerDO.getName())
                .countryName(xiaomanCustomerDO.getCountry_name())
                .regionOrProvince(Optional.ofNullable(xiaomanCustomerDO.getProvince()).orElse(xiaomanCustomerDO.getRegion()))
                .city(xiaomanCustomerDO.getCity())
                .customerPortraits(xiaomanCustomerDO.getCustomer_portraits())
                .nature(xiaomanCustomerDO.getNature())
                .trailStatus(xiaomanCustomerDO.getTrail_status())
                .homepage(xiaomanCustomerDO.getHomepage())
                .revenueScale(xiaomanCustomerDO.getRevenue_scale())
                .fragranceRevenueScale(xiaomanCustomerDO.getFragrance_revenue_scale())
                .estimatedAnnualSales(xiaomanCustomerDO.getEstimated_annual_sales())
                .corrival(
                        (StrUtil.isNotEmpty(xiaomanCustomerDO.getUntoMachineRemark()) ? "机器类竟对：" : "") + Optional.ofNullable(xiaomanCustomerDO.getUntoMachineRemark()).orElse("") + "\n" +
                                (StrUtil.isNotEmpty(xiaomanCustomerDO.getUntoMachineRemark()) ? "精油类竟对：" : "") + Optional.ofNullable(xiaomanCustomerDO.getUntoEssentialOilRemark()).orElse("")
                )
                .tztfourPredictionSales(xiaomanCustomerDO.getTztfourPredictionSales())
                .tztfivePredictionSales(xiaomanCustomerDO.getTztfivePredictionSales())
                .yearOrderMap(yearSaleMap)
                .tztfourPredictionSales(amountRmbByYear.get("2024"))
                .tztfourReachingStandard(yearSaleMap.get("2024"))
                .tztfivePredictionSales(amountRmbByYear.get("2025"))
                .tztfiveReachingStandard(yearSaleMap.get("2025"))
                .build();
        result.setOrderTotalList(new HashMap<>());
        amountRmbByMonth.forEach((key,value)->{
            result.getOrderTotalList().put(key, XiaomanOrderTotalVO.builder()
                    .forecastQty(qtyByMonth.get(key))
                    .forecastAmount(amountRmbByMonth.get(key))
                    .build());
        });
        monthMap.forEach((key, value) -> {
            Map<Integer, ErpCustomerMonthAndWeekSalesDO> weekMap = value.stream().collect(Collectors.toMap(ErpCustomerMonthAndWeekSalesDO::getBusinessWeek, p -> p));
            result.getOrderTotalList().put(key, XiaomanOrderTotalVO.builder()
                    .forecastQty(qtyByMonth.get(key))
                    .forecastAmount(amountRmbByMonth.get(key))
                    .oneWeekAmount(weekMap.getOrDefault(1, new ErpCustomerMonthAndWeekSalesDO()).getTotalSales())
                    .twoWeekAmount(weekMap.getOrDefault(2, new ErpCustomerMonthAndWeekSalesDO()).getTotalSales())
                    .threeWeekAmount(weekMap.getOrDefault(3, new ErpCustomerMonthAndWeekSalesDO()).getTotalSales())
                    .fourWeekAmount(weekMap.getOrDefault(4, new ErpCustomerMonthAndWeekSalesDO()).getTotalSales())
                    .totalQty(value.stream().mapToInt(ErpCustomerMonthAndWeekSalesDO::getTotalQty).sum())
                    .totalAmount(value.stream().mapToDouble(ErpCustomerMonthAndWeekSalesDO::getTotalSales).sum())
                    .build());
        });
        result.setPaymentMap(new HashMap<>());
        for (ErpCustomerRcvDO erpCustomerRcvDO : rcvMap.getOrDefault(xiaomanCustomerDO.getErp_code(), new ArrayList<>())) {
            result.getPaymentMap().put(erpCustomerRcvDO.getBusinessYear() + "-" + erpCustomerRcvDO.getBusinessMonth(), erpCustomerRcvDO.getTotalMoney());
        }
        return result;
    }

    @Override
    public void exportXiaomanOrderPage(HttpServletResponse response, XiaomanOrderPageReqVO pageReqVO, List<XiaomanOrderRespVO> contentList) throws Exception {
        DateTime startMonth = DateUtil.parseDate(pageReqVO.getSalesTime()[0]);
        DateTime endMonth = DateUtil.parseDate(pageReqVO.getSalesTime()[1]);
        long month = DateUtil.betweenMonth(startMonth, endMonth, true) + 1;
        List<KeyValue<String, String>> columns = getColumns(XiaomanOrderRespVO.class, true);
        List<KeyValue<String, String>> totalColumns = getColumns(XiaomanOrderTotalVO.class, false);
        // 创建一个新的工作簿
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("数据");
            putColumn(startMonth, month, columns, totalColumns, workbook, sheet);
            int rowNum = 2;
            for (XiaomanOrderRespVO xiaomanOrderRespVO : contentList) {
                Row row = sheet.createRow(rowNum++);
                int columnIndex = 0;
                row.createCell(columnIndex++).setCellValue(StrUtil.join(",", Optional.ofNullable(xiaomanOrderRespVO.getSalesperson()).orElse(new ArrayList<>())));
                row.createCell(columnIndex++).setCellValue(Optional.ofNullable(xiaomanOrderRespVO.getDepartmentName()).orElse(""));
                row.createCell(columnIndex++).setCellValue(Optional.ofNullable(xiaomanOrderRespVO.getCustomerName()).orElse(""));
                row.createCell(columnIndex++).setCellValue(Optional.ofNullable(xiaomanOrderRespVO.getCountryName()).orElse(""));
                row.createCell(columnIndex++).setCellValue(Optional.ofNullable(xiaomanOrderRespVO.getRegionOrProvince()).orElse(""));
                row.createCell(columnIndex++).setCellValue(Optional.ofNullable(xiaomanOrderRespVO.getCity()).orElse(""));
                row.createCell(columnIndex++).setCellValue(Optional.ofNullable(xiaomanOrderRespVO.getCustomerPortraits()).orElse(""));
                row.createCell(columnIndex++).setCellValue(Optional.ofNullable(xiaomanOrderRespVO.getNature()).orElse(""));
                row.createCell(columnIndex++).setCellValue(Optional.ofNullable(xiaomanOrderRespVO.getTrailStatus()).orElse(""));
                row.createCell(columnIndex++).setCellValue(Optional.ofNullable(xiaomanOrderRespVO.getHomepage()).orElse(""));
                row.createCell(columnIndex++).setCellValue(Optional.ofNullable(xiaomanOrderRespVO.getRevenueScale()).orElse(""));
                row.createCell(columnIndex++).setCellValue(Optional.ofNullable(xiaomanOrderRespVO.getFragranceRevenueScale()).orElse(""));
                row.createCell(columnIndex++).setCellValue(Optional.ofNullable(xiaomanOrderRespVO.getEstimatedAnnualSales()).orElse(""));
                for (int i = 2; i >= 0; i--) {
                    row.createCell(columnIndex++).setCellValue(Optional.ofNullable(xiaomanOrderRespVO.getYearOrderMap()).orElse(new HashMap<>()).getOrDefault(String.valueOf(DateUtil.year(DateUtil.date()) - i), 0D));
                }
                row.createCell(columnIndex++).setCellValue(Optional.ofNullable(xiaomanOrderRespVO.getCorrival()).orElse(""));
                row.createCell(columnIndex++).setCellValue(Optional.ofNullable(xiaomanOrderRespVO.getTztfourPredictionSales()).orElse(0D));
                row.createCell(columnIndex++).setCellValue(Optional.ofNullable(xiaomanOrderRespVO.getTztfourReachingStandard()).orElse(0D));
                row.createCell(columnIndex++).setCellValue(Optional.ofNullable(xiaomanOrderRespVO.getTztfivePredictionSales()).orElse(0D));
                row.createCell(columnIndex++).setCellValue(Optional.ofNullable(xiaomanOrderRespVO.getTztfiveReachingStandard()).orElse(0D));
                for (int i = 0; i < month; i++) {
                    DateTime dateTime = DateUtil.parseDate(pageReqVO.getSalesTime()[0]);
                    DateTime offset = dateTime.offset(DateField.MONTH, i);
                    XiaomanOrderTotalVO xiaomanOrderTotalVO = Optional.ofNullable(xiaomanOrderRespVO.getOrderTotalList()).orElse(new HashMap<>()).get(offset.toString("yyyy-MM"));
                    if (xiaomanOrderTotalVO == null) {
                        columnIndex += totalColumns.size();
                        continue;
                    }
                    row.createCell(columnIndex++).setCellValue(Optional.ofNullable(xiaomanOrderTotalVO.getForecastQty()).orElse(0));
                    row.createCell(columnIndex++).setCellValue(Optional.ofNullable(xiaomanOrderTotalVO.getForecastAmount()).orElse(0D));
                    row.createCell(columnIndex++).setCellValue(Optional.ofNullable(xiaomanOrderTotalVO.getOneWeekAmount()).orElse(0D));
                    row.createCell(columnIndex++).setCellValue(Optional.ofNullable(xiaomanOrderTotalVO.getTwoWeekAmount()).orElse(0D));
                    row.createCell(columnIndex++).setCellValue(Optional.ofNullable(xiaomanOrderTotalVO.getThreeWeekAmount()).orElse(0D));
                    row.createCell(columnIndex++).setCellValue(Optional.ofNullable(xiaomanOrderTotalVO.getFourWeekAmount()).orElse(0D));
                    row.createCell(columnIndex++).setCellValue(Optional.ofNullable(xiaomanOrderTotalVO.getTotalQty()).orElse(0));
                    row.createCell(columnIndex++).setCellValue(Optional.ofNullable(xiaomanOrderTotalVO.getTotalAmount()).orElse(0D));
                }

                for (int i = 0; i < month; i++) {
                    DateTime dateTime = DateUtil.parseDate(pageReqVO.getSalesTime()[0]);
                    DateTime offset = dateTime.offset(DateField.MONTH, i);
                    row.createCell(columnIndex++).setCellValue(Optional.ofNullable(xiaomanOrderRespVO.getPaymentMap()).orElse(new HashMap<>()).getOrDefault(offset.toString("yyyy-MM"), 0D));
                }
            }
            for (int i = 0; i < sheet.getRow(0).getLastCellNum(); i++) {
                sheet.autoSizeColumn(i);
            }
            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=filename.xlsx");
            // 建立输出流的连接
            OutputStream outputStream = response.getOutputStream();

            // 将数据导出到Excel表格
            workbook.write(outputStream);

            // 关闭输出流
            outputStream.close();
        }
    }

    /**
     * 填充表头
     *
     * @param startMonth
     * @param month
     * @param columns
     * @param totalColumns
     * @param workbook
     * @param sheet
     */
    private void putColumn(DateTime startMonth, long month, List<KeyValue<String, String>> columns, List<KeyValue<String, String>> totalColumns, Workbook workbook, Sheet sheet) {
        Row headerRow = sheet.createRow(0);
        Row headerRow2 = sheet.createRow(1);
        int columnIndex = 0;
        CellStyle headerStyle = workbook.createCellStyle();
        headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.index);
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        Font font = workbook.createFont();
        font.setBold(true);
        headerStyle.setFont(font);
        headerStyle.setBorderTop(BorderStyle.THIN);
        headerStyle.setBorderBottom(BorderStyle.THIN);
        headerStyle.setBorderLeft(BorderStyle.THIN);
        headerStyle.setBorderRight(BorderStyle.THIN);

        for (KeyValue<String, String> column : columns) {
            switch (column.getKey()) {
                case "yearOrderMap" -> {
                    Cell cell = headerRow.createCell(columnIndex);
                    cell.setCellValue(column.getValue());
                    CellRangeAddress mergedRegion = new CellRangeAddress(0, 0, columnIndex, columnIndex + 2);
                    sheet.addMergedRegion(mergedRegion);
                    applyCellStyleToMergedRegion(sheet, mergedRegion, headerStyle);
                    int year = DateUtil.year(DateUtil.date());
                    Cell year1 = headerRow2.createCell(columnIndex++);
                    year1.setCellValue(year - 2);
                    year1.setCellStyle(headerStyle);
                    Cell year2 = headerRow2.createCell(columnIndex++);
                    year2.setCellValue(year - 1);
                    year2.setCellStyle(headerStyle);
                    Cell year3 = headerRow2.createCell(columnIndex++);
                    year3.setCellValue(year);
                    year3.setCellStyle(headerStyle);
                }
                case "orderTotalList" -> {
                    for (long l = 0; l < month; l++) {
                        DateTime tempDateTime = new DateTime(startMonth);
                        Cell cell = headerRow.createCell(columnIndex);
                        DateTime offset = tempDateTime.offset(DateField.MONTH, (int) l);
                        cell.setCellValue(offset.toString("yyyy-MM") + "销售数据");
                        CellRangeAddress mergedRegion = new CellRangeAddress(0, 0, columnIndex, columnIndex + totalColumns.size() - 1);
                        sheet.addMergedRegion(mergedRegion);
                        applyCellStyleToMergedRegion(sheet, mergedRegion, headerStyle);
                        for (KeyValue<String, String> totalColumn : totalColumns) {
                            Cell tempCell = headerRow2.createCell(columnIndex++);
                            tempCell.setCellValue(totalColumn.getValue());
                            tempCell.setCellStyle(headerStyle);
                        }
                    }
                }
                case "paymentMap" -> {
                    for (long l = 0; l < month; l++) {
                        DateTime tempDateTime = new DateTime(startMonth);
                        Cell cell = headerRow.createCell(columnIndex);
                        cell.setCellValue(tempDateTime.offset(DateField.MONTH, (int) l).toString("yyyy-MM") + "回款数据");
                        CellRangeAddress mergedRegion = new CellRangeAddress(0, 1, columnIndex, columnIndex);
                        sheet.addMergedRegion(mergedRegion);
                        applyCellStyleToMergedRegion(sheet, mergedRegion, headerStyle);
                        columnIndex++;
                    }
                }
                default -> {
                    Cell cell = headerRow.createCell(columnIndex);
                    cell.setCellValue(column.getValue());
                    CellRangeAddress mergedRegion = new CellRangeAddress(0, 1, columnIndex, columnIndex);
                    sheet.addMergedRegion(mergedRegion);
                    applyCellStyleToMergedRegion(sheet, mergedRegion, headerStyle);
                    columnIndex++;
                }
            }
        }
    }

    private void applyCellStyleToMergedRegion(Sheet sheet, CellRangeAddress mergedRegion, CellStyle cellStyle) {
        for (int rowNum = mergedRegion.getFirstRow(); rowNum <= mergedRegion.getLastRow(); rowNum++) {
            Row row = sheet.getRow(rowNum);
            if (row == null) {
                row = sheet.createRow(rowNum);
            }
            for (int colNum = mergedRegion.getFirstColumn(); colNum <= mergedRegion.getLastColumn(); colNum++) {
                Cell cell = row.getCell(colNum);
                if (cell == null) {
                    cell = row.createCell(colNum);
                }
                cell.setCellStyle(cellStyle);
            }
        }
    }


    /**
     * 获取表头
     *
     * @param clazz
     * @param isParent
     * @return
     */
    private List<KeyValue<String, String>> getColumns(Class<?> clazz, Boolean isParent) {
        List<Field> fields = getAllFields(clazz, isParent);

        List<KeyValue<String, String>> heads = new ArrayList<>();
        for (Field field : fields) {
            // 获取 ExcelProperty 注解
            ExcelProperty excelProperty = field.getAnnotation(ExcelProperty.class);
            if (excelProperty != null) {
                heads.add(new KeyValue<>(field.getName(), excelProperty.value()[0]));
            } else {
                heads.add(new KeyValue<>(field.getName(), field.getName()));
            }
        }
        return heads;
    }

    private static List<Field> getAllFields(Class<?> clazz, Boolean isParent) {
        List<Field> fields = new ArrayList<>(Arrays.asList(clazz.getDeclaredFields()));
        if (isParent) {
            Class<?> superclass = clazz.getSuperclass();
            if (superclass != null) {
                fields.addAll(0, getAllFields(superclass, true));
            }
        }
        return fields;
    }
}
