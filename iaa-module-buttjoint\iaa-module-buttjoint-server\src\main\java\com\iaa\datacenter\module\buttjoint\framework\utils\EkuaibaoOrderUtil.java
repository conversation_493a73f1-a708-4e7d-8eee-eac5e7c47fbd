package com.iaa.datacenter.module.buttjoint.framework.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.ekuaibao.EkuaibaoInvoiceDO;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.ekuaibao.EkuaibaoOrderDO;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.ekuaibao.EkuaibaoPayDO;
import com.iaa.datacenter.module.buttjoint.enums.EkuaibaoToErpOrderTypeEnum;
import com.iaa.datacenter.module.buttjoint.framework.core.EkuaibaoFeeDetails;
import com.iaa.datacenter.module.buttjoint.framework.core.EkuaibaoFeeDetailsLine;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

import com.iaa.datacenter.module.buttjoint.framework.core.EkuaibaoFeeInvoice;
import com.jayway.jsonpath.DocumentContext;
import com.jayway.jsonpath.JsonPath;
import org.bson.Document;

/**
 * 合思单据工具类
 */
public class EkuaibaoOrderUtil {

    /**
     * 单据模板
     */
    interface FORM_TYPE {
        // 预付款单
        String PREPAYMENT = "ID01Aezqej2wDt";
        /**
         * 模具预付款但
         */
        String PREPAYMENT_1 = "ID01KJ7MfWBHrh";
        // 借支单
        String DEBIT = "ID01Aezqej2wmX";
        String DEBIT_1 = "ID01CPQtmGBV03";
        // 平台充值单
        String RECHARGE = "ID01BEWu2fZVUP";
        // 日常报销单
        String DAILY_REIMBURSEMENT = "ID01Aezqej2vPV";
        // 差旅报销单
        String TRAVEL_REIMBURSEMENT = "ID01Aezqej2vzp";
        // 付款单
        String PAYMENT = "ID01Aezqej2w6r";
    }

    private static final HashMap<String, String> INVOICE_TYPE_MAP = new HashMap<String, String>() {{
        put("FULL_DIGITAl_NORMAL", "普票");//电子发票（普通发票）
        put("FULL_DIGITAl_SPECIAL", "专票");//电子发票（增值税专用发票）
        put("FULL_DIGITAl_PAPER", "专票");//全电纸质发票（增值税专用发票）
        put("FULL_DIGITAl_PAPER_NORMAL", "普票");//全电纸质发票（增值税普通发票）
        put("DIGITAL_NORMAL", "普票");//增值税电子普通发票
        put("DIGITAL_SPECIAL", "专票");//增值税电子专用发票
        put("PAPER_NORMAL", "普票");//增值税普通发票
        put("PAPER_ROLL", "普票");//增值税普通发票（卷式）
        put("PAPER_SPECIAL", "专票");//增值税普通发票（卷式）
        put("PAPER_CAR", "普票");//机动车销售统一发票
        put("SECOND_CAR", "普票");//二手车销售统一发票
        put("PAPER_FEE", "普票");//通行费发票
        put("BLOCK_CHAIN", "普票");//区块链电子发票
        put("ELECTRONIC_AIRCRAFT_INVOICE", "普票");//电子发票（航空运输电子客票行程单）
        put("ELECTRONIC_TRAIN_INVOICE", "普票");//电子发票（铁路电子客票）
    }};

    private static final HashMap<String, String> TAX_MAP = new HashMap<String, String>() {{
        put("0.00", "T07");
        put("0.01", "T10");
        put("0.02", "T09");
        put("0.03", "T08");
        put("0.05", "T12");
        put("0.06", "T06");
        put("0.09", "T11");
        put("0.10", "T03");
        put("0.11", "T13");
        put("0.13", "T05");
        put("0.17", "T01");
    }};

    /**
     * 解析费用明细
     *
     * @param form
     * @return
     */
    public static List<EkuaibaoFeeDetails> parseFeeDetails(String formType, JSONObject form) throws Exception {
        return switch (formType) {
            case "loan" -> parseLoanOrder(form);
            case "expense" -> parseExpenseOrder(form);
            default -> throw new IllegalArgumentException("不支持的单据类型");
        };
    }

    /**
     * 解析报销单费用明细
     *
     * @param form
     * @return
     */
    private static List<EkuaibaoFeeDetails> parseExpenseOrder(JSONObject form) throws Exception {
        List<EkuaibaoFeeDetails> result = new ArrayList<>();
        String org = EkuaibaoFC.getCustomCode(Optional.ofNullable(form.getString("法人实体"))
                .orElse(form.getString("u_费用主体")));
        List<EkuaibaoFeeDetailsLine> detailsLines = new ArrayList<>();
        parseDetailsToFee(form, org, detailsLines);
        // 有无核销
        boolean hasWriteOff = form.containsKey("writtenOffRecords") && form.getJSONArray("writtenOffRecords").size() > 0;

        if (hasWriteOff) {
            handleWriteOffRecords(form, org, detailsLines, result);
        } else {
            handleNonWriteOffRecords(form, detailsLines, result);
        }

        return result;
    }

    /**
     * 有核销逻辑
     *
     * @param form
     * @param org
     * @param detailsLines
     * @param result
     */
    private static void handleWriteOffRecords(JSONObject form, String org, List<EkuaibaoFeeDetailsLine> detailsLines, List<EkuaibaoFeeDetails> result) {
        BigDecimal[] prices = calculatePricesFromWriteOffRecords(form);
        BigDecimal debitPrice = prices[0];
        BigDecimal depositPrice = prices[1];
        BigDecimal rechargePrice = prices[2];
        BigDecimal paymentPrice = prices[3];

        addVoucherIfNotEmpty(form, detailsLines, debitPrice, true, false, false, false, EkuaibaoToErpOrderTypeEnum.VOUCHER, result);
        addVoucherIfNotEmpty(form, detailsLines, depositPrice, false, true, false, false, EkuaibaoToErpOrderTypeEnum.VOUCHER, result);
        addVoucherIfNotEmpty(form, detailsLines, rechargePrice, false, false, true, false, EkuaibaoToErpOrderTypeEnum.VOUCHER, result);
        addVoucherIfNotEmpty(form, detailsLines, paymentPrice, false, false, false, true, EkuaibaoToErpOrderTypeEnum.DUE, result);

        List<EkuaibaoFeeDetailsLine> filteredDetailsLines = detailsLines.stream()
                .filter(e -> e.getAmountCny().compareTo(BigDecimal.ZERO) > 0)
                .toList();
        if(CollUtil.isEmpty(filteredDetailsLines)){
            return;
        }

        EkuaibaoFeeDetails pay = createPayVoucher(form, filteredDetailsLines);
        result.add(pay);

        if (pay.getHasPayment()) {
            result.add(createDueVoucher(form, filteredDetailsLines));
        }
    }

    /**
     * 无核销逻辑
     *
     * @param form
     * @param detailsLines
     * @param result
     */
    private static void handleNonWriteOffRecords(JSONObject form, List<EkuaibaoFeeDetailsLine> detailsLines, List<EkuaibaoFeeDetails> result) {
        List<EkuaibaoFeeDetailsLine> mallLine = detailsLines.stream()
                .filter(EkuaibaoFeeDetailsLine::getHasMall)
                .toList();

        if (CollUtil.isNotEmpty(mallLine)) {
            EkuaibaoFeeDetails voucherVoucher = createVoucherVoucher(form, mallLine);
            voucherVoucher.getLines().forEach(item -> {
                item.setHasRecharge(false);
                item.setHasDebit(false);
                item.setHasDeposit(false);
            });
            result.add(voucherVoucher);
        }

        List<EkuaibaoFeeDetailsLine> nonMallLine = detailsLines.stream()
                .filter(e -> !e.getHasMall())
                .toList();
        if(nonMallLine.size()==0) return;
        EkuaibaoFeeDetails pay = createPayVoucher(form, nonMallLine);
        result.add(pay);

        if (pay.getHasPayment()) {
            result.add(createDueVoucher(form, nonMallLine));
        }
    }

    /**
     * 按用途划分费用行
     *
     * @param form
     * @return
     */
    private static BigDecimal[] calculatePricesFromWriteOffRecords(JSONObject form) {
        BigDecimal debitPrice = BigDecimal.ZERO;
        BigDecimal depositPrice = BigDecimal.ZERO;
        BigDecimal rechargePrice = BigDecimal.ZERO;
        BigDecimal paymentPrice = BigDecimal.ZERO;

        List<Document> writtenOffRecords = readJson(JsonPath.parse(form), "$.writtenOffRecords");
        for (Document writtenOffRecord : writtenOffRecords) {
            EkuaibaoOrderDO order = EkuaibaoFC.ekuaibaoOrderService.getById(writtenOffRecord.getString("loanId"));
            String specificationIdPrefix = order.getForm().getString("specificationId").split(":")[0];
            double amount = Double.parseDouble(writtenOffRecord.getString("amount"));

            switch (specificationIdPrefix) {
                case FORM_TYPE.DEBIT, FORM_TYPE.DEBIT_1 -> {
                    String borrowType = order.getForm().getString("u_借支类型");
                    if (List.of("ID01ANR6nji8ZF", "ID01ANR16h2Ocf").contains(borrowType)) {
                        debitPrice = debitPrice.add(BigDecimal.valueOf(amount));
                    } else {
                        depositPrice = depositPrice.add(BigDecimal.valueOf(amount));
                    }
                }
                case FORM_TYPE.PREPAYMENT -> paymentPrice = paymentPrice.add(BigDecimal.valueOf(amount));
                case FORM_TYPE.RECHARGE -> rechargePrice = rechargePrice.add(BigDecimal.valueOf(amount));
            }
        }

        return new BigDecimal[]{debitPrice, depositPrice, rechargePrice, paymentPrice};
    }

    /**
     * 添加转单单据
     *
     * @param form
     * @param detailsLines
     * @param price
     * @param hasDebit
     * @param hasDeposit
     * @param hasRecharge
     * @param hasPayment
     * @param typeEnum
     * @param result
     */
    private static void addVoucherIfNotEmpty(JSONObject form, List<EkuaibaoFeeDetailsLine> detailsLines, BigDecimal price, boolean hasDebit, boolean hasDeposit, boolean hasRecharge, boolean hasPayment, EkuaibaoToErpOrderTypeEnum typeEnum, List<EkuaibaoFeeDetails> result) {
        EkuaibaoFeeDetails ekuaibaoFeeDetails = addVoucherIfNotEmpty(form, detailsLines, price, hasDebit, hasDeposit, hasRecharge, hasPayment, typeEnum);
        if (Objects.nonNull(ekuaibaoFeeDetails)) {
            result.add(ekuaibaoFeeDetails);
        }
    }

    /**
     * 填入单据
     *
     * @param form
     * @param detailsLines
     * @param price
     * @param hasDebit
     * @param hasDeposit
     * @param hasRecharge
     * @param hasPayment
     * @param typeEnum
     * @return
     */
    private static EkuaibaoFeeDetails addVoucherIfNotEmpty(JSONObject form, List<EkuaibaoFeeDetailsLine> detailsLines
            , BigDecimal price, boolean hasDebit, boolean hasDeposit, boolean hasRecharge, boolean hasPayment, EkuaibaoToErpOrderTypeEnum typeEnum) {
        if (price.compareTo(BigDecimal.ZERO) > 0) {
            List<EkuaibaoFeeDetailsLine> details = getEkuaibaoFeeDetailsLines(detailsLines, price);
            if (CollUtil.isNotEmpty(details)) {
                details.forEach(item -> {
                    item.setHasDebit(hasDebit);
                    item.setHasDeposit(hasDeposit);
                    item.setHasRecharge(hasRecharge);
                });

                EkuaibaoFeeDetails voucher = getEkuaibaoFeeDetails(form);
                voucher.setHasPayment(hasPayment);
                voucher.setType(typeEnum.getType());
                voucher.setLines(details);
                return voucher;
            }
        }
        return null;
    }

    /**
     * 创建付款单
     *
     * @param form
     * @param detailsLines
     * @return
     */
    private static EkuaibaoFeeDetails createPayVoucher(JSONObject form, List<EkuaibaoFeeDetailsLine> detailsLines) {
        EkuaibaoFeeDetails pay = getEkuaibaoFeeDetails(form);
        pay.setHasPayment(detailsLines.stream().anyMatch(p -> p.getInvoiceList().stream().anyMatch(EkuaibaoFeeInvoice::isHasSpecial)));
        pay.setType(EkuaibaoToErpOrderTypeEnum.PAY.getType());
        pay.setLines(detailsLines);
        return pay;
    }

    /**
     * 创建应付单
     *
     * @param form
     * @param detailsLines
     * @return
     */
    private static EkuaibaoFeeDetails createDueVoucher(JSONObject form, List<EkuaibaoFeeDetailsLine> detailsLines) {
        EkuaibaoFeeDetails ap = getEkuaibaoFeeDetails(form);
        ap.setHasPayment(true);
        ap.setType(EkuaibaoToErpOrderTypeEnum.DUE.getType());
        ap.setLines(detailsLines);
        return ap;
    }

    /**
     * 创建凭证
     *
     * @param form
     * @param detailsLines
     * @return
     */
    private static EkuaibaoFeeDetails createVoucherVoucher(JSONObject form, List<EkuaibaoFeeDetailsLine> detailsLines) {
        EkuaibaoFeeDetails voucher = getEkuaibaoFeeDetails(form);
        voucher.setHasPayment(true);
        voucher.setType(EkuaibaoToErpOrderTypeEnum.VOUCHER.getType());
        voucher.setLines(detailsLines);
        return voucher;
    }

    /**
     * 根据核销金额获取订单明细行
     *
     * @param detailsLines
     * @param writePrice
     * @return
     */
    private static List<EkuaibaoFeeDetailsLine> getEkuaibaoFeeDetailsLines(List<EkuaibaoFeeDetailsLine> detailsLines, BigDecimal writePrice) {
        List<EkuaibaoFeeDetailsLine> dueDetails = new ArrayList<>();
        for (EkuaibaoFeeDetailsLine detailsLine : detailsLines) {
            if (writePrice.compareTo(BigDecimal.ZERO) == 0) break;
            if (writePrice.compareTo(detailsLine.getAmountCny()) >= 0) {
                writePrice = writePrice.subtract(detailsLine.getAmountCny());
                dueDetails.add(ObjectUtil.clone(detailsLine));
                detailsLine.setAmount(BigDecimal.ZERO);
                detailsLine.setAmountCny(BigDecimal.ZERO);
            } else {
                EkuaibaoFeeDetailsLine clone = ObjectUtil.clone(detailsLine);
                clone.setAmountCny(writePrice);
                clone.setAmount(clone.getAmountCny().multiply(clone.getRate()));
                dueDetails.add(clone);
                detailsLine.setAmountCny(detailsLine.getAmountCny().multiply(writePrice));
                detailsLine.setAmount(detailsLine.getAmountCny().multiply(detailsLine.getRate()));
                writePrice = BigDecimal.ZERO;
            }
        }
        return dueDetails;
    }

    /**
     * 解析费用明细行
     *
     * @param form
     * @param org
     * @param detailsLines
     */
    private static void parseDetailsToFee(JSONObject form, String org, List<EkuaibaoFeeDetailsLine> detailsLines) throws Exception {
        EkuaibaoPayDO payDO = EkuaibaoFC.getPayDO(form.getString("paymentAccountId"));
        for (Object details : form.getJSONArray("details")) {
            DocumentContext ctx = JsonPath.parse(details);
            BigDecimal amount = readJsonBigDecimal(ctx, "$.feeTypeForm.amount.standard");
            if (amount == null || amount.compareTo(BigDecimal.ZERO) == 0) continue;
            // 发票明细
            List<Document> invoices = readJson(ctx, "$.feeTypeForm.invoiceForm.invoices");
            List<EkuaibaoFeeInvoice> ekuaibaoFeeInvoices = new ArrayList<>();
            if (CollUtil.isNotEmpty(invoices)) {
                for (Document invoice : invoices) {
                    EkuaibaoInvoiceDO invoiceDO = EkuaibaoFC.getInvoiceDO(invoice.getString("invoiceId"));
                    if (Objects.isNull(invoiceDO.getE_system_发票主体_价税合计())) continue;
                    EkuaibaoFeeInvoice feeInvoice = EkuaibaoFeeInvoice.builder()
                            .amount(invoiceDO.getE_system_发票主体_价税合计().getBigDecimal("standard"))
                            .taxAmount(invoiceDO.getE_税额().getBigDecimal("standard"))
                            .hasSpecial(INVOICE_TYPE_MAP.getOrDefault(invoiceDO.getE_system_发票主体_发票类别(), "普票").equals("专票"))
                            .build();
                    BigDecimal rate = feeInvoice.getTaxAmount().divide(feeInvoice.getAmount().subtract(feeInvoice.getTaxAmount()), 2, RoundingMode.HALF_UP);
                    feeInvoice.setTaxCode(TAX_MAP.getOrDefault(rate.toString(), "T07"));// 找不到对应的税号默认为0
                    feeInvoice.setRate(rate);
                    ekuaibaoFeeInvoices.add(feeInvoice);
                }
            }

            // 分摊明细
            List<Document> apportions = readJson(ctx, "$.feeTypeForm.apportions");
            // 是否分摊
            if (CollUtil.isEmpty(apportions) || apportions.stream()
                    .noneMatch(p -> p.getString("specificationId").contains("部门"))) {
                EkuaibaoFeeDetailsLine line = new EkuaibaoFeeDetailsLine();
                detailsLines.add(line);
                line.setDept(EkuaibaoFC.getCustomDept(form.getString("u_费用部门"), org));
                line.setDeptId(form.getString("u_费用部门"));
                line.setIncomeId(readJson(ctx, "$.feeTypeId"));
                line.setIncome(EkuaibaoFC.getFeeCode(line.getIncomeId()));
                line.setAmountCny(readJsonBigDecimal(ctx, "$.feeTypeForm.amount.standard"));
                BigDecimal foreign = readJsonBigDecimal(ctx, "$.feeTypeForm.amount.foreign");
                line.setAmount(Optional.ofNullable(foreign).orElse(line.getAmountCny()));
                String currency = Optional.ofNullable(EkuaibaoFC.getCurrencyCode(readJson(ctx, "$.feeTypeForm.amount.foreignNumCode")))
                        .orElse(EkuaibaoFC.getCurrencyCode(readJson(ctx, "$.feeTypeForm.amount.standardNumCode")));
                line.setCurrency(currency);
                line.setRate(EkuaibaoFC.getExchangeRate(line.getCurrency(), DateUtil.now()));
                line.setInvoiceList(ekuaibaoFeeInvoices);
                line.setHasMall(StrUtil.isNotEmpty(readJson(ctx, "$.feeTypeForm.settlement")));
                line.setRemark(readJson(ctx,"$.feeTypeForm.consumptionReasons"));
                if (Objects.isNull(payDO)) continue;
                if (StrUtil.isNotEmpty(payDO.getCode()) && payDO.getCode().equals("CASH")) {
                    line.setSettlement("YZ01");
                } else {
                    line.setSettlement("YZ02");
                    line.setPayCode(StrUtil.isEmpty(payDO.getDetail().getString("number"))
                            ? StrUtil.isEmpty(payDO.getCode())
                            ? payDO.getRemark()
                            : payDO.getCode()
                            : payDO.getDetail().getString("number"));
                }
            } else {
                for (Document apportion : apportions) {
                    if (!apportion.getString("specificationId").contains("部门")) continue;
                    EkuaibaoFeeDetailsLine line = new EkuaibaoFeeDetailsLine();
                    detailsLines.add(line);
                    DocumentContext apportionCtx = JsonPath.parse(apportion);
                    // 计算单行税率
                    BigDecimal percentage = Optional.ofNullable(readJsonBigDecimal(apportionCtx, "$.apportionForm.apportionPercent")).orElse(BigDecimal.ZERO);
                    List<EkuaibaoFeeInvoice> apportionInvoices = new ArrayList<>();
                    for (EkuaibaoFeeInvoice invoice : ekuaibaoFeeInvoices) {
                        apportionInvoices.add(EkuaibaoFeeInvoice.builder()
                                .amount(invoice.getAmount().multiply(percentage.divide(new BigDecimal(100), RoundingMode.HALF_UP)))
                                .taxCode(invoice.getTaxCode())
                                .taxAmount(invoice.getTaxAmount().multiply(percentage.divide(new BigDecimal(100), RoundingMode.HALF_UP)))
                                .hasSpecial(invoice.isHasSpecial())
                                .rate(invoice.getRate())
                                .build()
                        );
                    }
                    line.setInvoiceList(apportionInvoices);
                    // 其他关键属性
                    line.setDept(EkuaibaoFC.getCustomDept(readJson(apportionCtx, "$.apportionForm.u_费用部门"), org));
                    line.setDeptId(readJson(apportionCtx, "$.apportionForm.u_费用部门"));
                    line.setIncomeId(readJson(ctx, "$.feeTypeId"));
                    line.setRemark(readJson(ctx,"$.feeTypeForm.consumptionReasons"));
                    line.setIncome(EkuaibaoFC.getFeeCode(line.getIncomeId()));
                    line.setAmountCny(readJsonBigDecimal(apportionCtx, "$.apportionForm.apportionMoney.standard"));
                    BigDecimal foreign = readJsonBigDecimal(apportionCtx, "$.apportionForm.apportionMoney.foreign");
                    line.setAmount(Optional.ofNullable(foreign).orElse(line.getAmountCny()));
                    String currency = Optional.ofNullable(EkuaibaoFC.getCurrencyCode(readJson(apportionCtx, "$.apportionForm.apportionMoney.foreignNumCode")))
                            .orElse(EkuaibaoFC.getCurrencyCode(readJson(apportionCtx, "$.apportionForm.apportionMoney.standardNumCode")));
                    line.setCurrency(currency);
                    line.setRate(EkuaibaoFC.getExchangeRate(line.getCurrency(), DateUtil.now()));
                    line.setHasMall(StrUtil.isNotEmpty(readJson(ctx, "$.feeTypeForm.settlement")));

                    if (Objects.isNull(payDO)) continue;
                    if (StrUtil.isNotEmpty(payDO.getCode()) && payDO.getCode().equals("CASH")) {
                        line.setSettlement("YZ01");
                    } else {
                        line.setSettlement("YZ02");
                        line.setPayCode(StrUtil.isEmpty(payDO.getDetail().getString("number"))
                                ? StrUtil.isEmpty(payDO.getCode())
                                ? payDO.getRemark()
                                : payDO.getCode()
                                : payDO.getDetail().getString("number"));
                    }
                }
            }
        }
    }

    /**
     * 获取一个全新的费用明细对象
     *
     * @param form
     * @return
     */
    private static EkuaibaoFeeDetails getEkuaibaoFeeDetails(JSONObject form) {
        EkuaibaoFeeDetails head = new EkuaibaoFeeDetails();
        head.setCode(form.getString("code"));
        head.setOrg(EkuaibaoFC.getCustomCode(
                Optional.ofNullable(form.getString("法人实体"))
                        .orElse(form.getString("u_费用主体")))
        );
        head.setUser(EkuaibaoFC.getStaffUserCode(form.getString("submitterId")));
        head.setTitle(form.getString("title"));
        head.setSupplier(EkuaibaoFC.getCustomCode(form.getString("u_供应商档案")));
        head.setCustomer(EkuaibaoFC.getCustomCode(form.getString("u_客户")));
        head.setCustomerId(form.getString("u_客户"));
        //付款对象既不是客户，又是供应商，则设置为36-118（员工报销）
        if (StrUtil.isEmpty(head.getSupplier()) && StrUtil.isEmpty(head.getCustomer())) {
            head.setSupplier("36-118");
        }
        if (form.containsKey("payDate")) {
            head.setPayDate(DateUtil.date(form.getLong("payDate")).toString("yyyy-MM-dd HH:mm:ss"));
        } else {
            head.setPayDate(DateUtil.date(form.getLong("submitDate")).toString("yyyy-MM-dd HH:mm:ss"));
        }
        head.setHasPayment(false);
        head.setHasRecharge(false);
        head.setRemark(EkuaibaoFC.getStaffName(form.getString("submitterId")) + "&" + form.getString("title"));
        return head;
    }

    /**
     * 解析借款单费用明细
     *
     * @param form
     * @return
     */
    private static List<EkuaibaoFeeDetails> parseLoanOrder(JSONObject form) throws Exception {
        EkuaibaoFeeDetails result = new EkuaibaoFeeDetails();

        result.setOrg(EkuaibaoFC.getCustomCode(
                Optional.ofNullable(form.getString("法人实体"))
                        .orElse(form.getString("u_费用主体")))
        );//费用组织
        EkuaibaoFeeDetailsLine line = new EkuaibaoFeeDetailsLine();
        result.setLines(new ArrayList<>() {{
            add(line);
        }});
        // 拼接摘要
        StringBuilder sb = new StringBuilder();
        sb.append(EkuaibaoFC.getStaffName(form.getString("submitterId")))
                .append("&")
                .append(form.getString("title"));
        if (form.containsKey("出纳备注")) {
            sb.append("&").append(form.getString("出纳备注"));
        }
        result.setHasPayment(true); //是预付款
        result.setHasRecharge(false);
        setToOrderType(form, result, sb);//设置转单类型
        sb.append("&").append(form.getString("code")).append(";");
        result.setRemark(sb.toString());
        if (form.containsKey("payDate")) {
            result.setPayDate(DateUtil.date(form.getLong("payDate")).toString("yyyy-MM-dd HH:mm:ss"));
        } else {
            result.setPayDate(DateUtil.date(form.getLong("submitDate")).toString("yyyy-MM-dd HH:mm:ss"));
        }
        result.getLines().get(0).setDept(EkuaibaoFC.getCustomDept(form.getString("u_费用部门"), result.getOrg()));//部门
        result.setCode(form.getString("code"));//单号
        result.setUser(EkuaibaoFC.getStaffUserCode(form.getString("submitterId")));//用户


        JSONObject payMoney = form.getJSONObject("payMoney");
        if (payMoney.containsKey("foreignNumCode")) {
            String currency = EkuaibaoFC.getCurrencyCode(payMoney.getString("foreignNumCode"));
            result.getLines().get(0).setCurrency(currency);//币种
            result.getLines().get(0).setRate(EkuaibaoFC.getExchangeRate(currency, result.getPayDate()));
            result.getLines().get(0).setAmount(payMoney.getBigDecimal("foreign"));//金额
        } else {
            result.getLines().get(0).setCurrency("C001");//币种、
            result.getLines().get(0).setRate(BigDecimal.ONE);//汇率
            result.getLines().get(0).setAmount(payMoney.getBigDecimal("standard"));//金额
        }

        // 支付账号
        EkuaibaoPayDO payDO = EkuaibaoFC.getPayDO(form.getString("paymentAccountId"));
        if (!Objects.isNull(payDO)) {
            if (StrUtil.isNotEmpty(payDO.getCode()) && payDO.getCode().equals("CASH")) {
                result.getLines().get(0).setSettlement("YZ01");
            } else {
                result.getLines().get(0).setSettlement("YZ02");
                result.getLines().get(0).setPayCode(StrUtil.isEmpty(payDO.getDetail().getString("number"))
                        ? StrUtil.isEmpty(payDO.getCode())
                        ? payDO.getRemark()
                        : payDO.getCode()
                        : payDO.getDetail().getString("number"));
            }
        }
        return new ArrayList<>() {{
            add(result);
        }};
    }

    /**
     * 设定转单类型
     *
     * @param form
     * @param result
     */
    private static void setToOrderType(JSONObject form, EkuaibaoFeeDetails result, StringBuilder sb) {
        List<String> customNames = new ArrayList<>();
        // 设定转单类型
        switch (form.getString("specificationId").split(":")[0]) {
            case FORM_TYPE.DEBIT, FORM_TYPE.DEBIT_1 -> {
                String borrowType = form.getString("u_借支类型");
                if (List.of("ID01ANR6nji8ZF", "ID01ANR16h2Ocf").contains(borrowType)) {
                    result.setType(EkuaibaoToErpOrderTypeEnum.CM_PAY.getType());
                    result.setSupplier("36-118");
                    result.setHasPayment(false); //是预付款
                } else {
                    result.setType(EkuaibaoToErpOrderTypeEnum.PAY.getType());
                    if(form.containsKey("u_供应商档案")){
                        result.setSupplier(EkuaibaoFC.getCustomCode(form.getString("u_供应商档案")));
                    }else{
                        result.setCustomer(EkuaibaoFC.getCustomCode(form.getString("u_客户")));
                        result.setHasPayment(false);
                    }

                }
                result.getLines().get(0).setIncome(EkuaibaoFC.getCustomCode(form.getString("u_借支类型")).split("-")[0]);
                EkuaibaoFC.getCustomNames(customNames, form.getString("u_借支类型"));
            }
            case FORM_TYPE.PREPAYMENT,FORM_TYPE.PREPAYMENT_1 -> {
                result.getLines().get(0).setIncome(EkuaibaoFC.getCustomCode(form.getString("u_实际费用")).split("-")[0]);
                result.setType(EkuaibaoToErpOrderTypeEnum.PAY.getType());
                result.setSupplier(EkuaibaoFC.getCustomCode(form.getString("u_供应商档案")));
                EkuaibaoFC.getCustomNames(customNames, form.getString("u_实际费用"));
            }
            case FORM_TYPE.RECHARGE -> {
                result.getLines().get(0).setIncome("0020");
                result.setType(EkuaibaoToErpOrderTypeEnum.PAY.getType());
                result.setCustomer(EkuaibaoFC.getCustomCode(form.getString("u_客户")));
                result.setHasRecharge(true);
                EkuaibaoFC.getCustomNames(customNames, "ID01BjeBB2J8cf");
            }
        }
//        sb.append("&").append(String.join("\\", customNames));
    }

    private static <T> T readJson(DocumentContext documentContext, String path) {
        try {
            return documentContext.read(path);
        } catch (com.jayway.jsonpath.PathNotFoundException e) {
            return null;
        }
    }

    private static BigDecimal readJsonBigDecimal(DocumentContext documentContext, String path) {
        try {
            return new BigDecimal(documentContext.read(path).toString());
        } catch (com.jayway.jsonpath.PathNotFoundException e) {
            return null;
        }
    }
}
