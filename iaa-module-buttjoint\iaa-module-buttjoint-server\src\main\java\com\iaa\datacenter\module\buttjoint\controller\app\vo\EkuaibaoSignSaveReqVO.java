package com.iaa.datacenter.module.buttjoint.controller.app.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 合思单点登录数据请求对象
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EkuaibaoSignSaveReqVO {
    /**
     * 查看页面类型
     */
    private String pageType;
    /**
     * 流程ID
     */
    private String flowId;
    /**
     * 是否移动端
     */
    private Boolean isApplet;
    /**
     * 蓝凌OA登录名
     */
    private String loginName;
    /**
     * 是否OA登录
     */
    private Boolean isOA;
}
