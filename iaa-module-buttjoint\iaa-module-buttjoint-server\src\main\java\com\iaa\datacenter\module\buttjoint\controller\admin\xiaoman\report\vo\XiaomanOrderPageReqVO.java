package com.iaa.datacenter.module.buttjoint.controller.admin.xiaoman.report.vo;

import com.iaa.datacenter.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class XiaomanOrderPageReqVO extends PageParam {

    @Schema(description = "销售数据查询时间")
    private String[] salesTime;

    @Schema(description = "数据查看类型，1 商机数据 ，2 销售数据")
    private Boolean viewType;

    @Schema(description = "业务员")
    private List<String> salesperson;

    @Schema(description = "组别")
    private List<Long> departmentName;

    @Schema(description = "客户名称")
    private String customerName;

    @Schema(description = "国家/地区")
    private String countryName;

    @Schema(description = "洲/省")
    private String regionOrProvince;

    @Schema(description = "城市")
    private String city;

    @Schema(description = "合作阶段")
    List<String> trailStatus;

    @Schema(description = "客户画像")
    private List<String> customerPortraits;

    @Schema(description = "客户性质")
    private String nature;

    @Schema(description = "客户官网")
    private String homepage;

    @Schema(description = "客户营收规模评估")
    private List<String> revenueScale;

    @Schema(description = "香氛产品规模评估")
    private List<String> fragranceRevenueScale;
}
