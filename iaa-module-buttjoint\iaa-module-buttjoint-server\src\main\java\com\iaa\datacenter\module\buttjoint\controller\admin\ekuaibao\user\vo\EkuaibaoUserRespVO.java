package com.iaa.datacenter.module.buttjoint.controller.admin.ekuaibao.user.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.iaa.datacenter.framework.common.annotation.WebTableColumn;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Data
@ExcelIgnoreUnannotated
@Schema(description = "管理后台 - 合思用户 Request VO")
public class EkuaibaoUserRespVO {

    @ExcelProperty("用户ID")
    @WebTableColumn(label="用户ID",noSearch = true)
    @NotNull(message="用户ID不能为空")
    private String id;

    @ExcelProperty("用户名称")
    @WebTableColumn(label="用户名称")
    private String name;

    @ExcelProperty("部门")
    @WebTableColumn(label="部门")
    private String deptName;

    @ExcelProperty("U9工作记录")
    @WebTableColumn(label="U9工作记录",noSearch = true)
    private String erpUserCode;

    @ExcelProperty("U9业务员信息")
    @WebTableColumn(label="U9业务员信息",noSearch = true)
    private List<String> erpSellerCode;

}
