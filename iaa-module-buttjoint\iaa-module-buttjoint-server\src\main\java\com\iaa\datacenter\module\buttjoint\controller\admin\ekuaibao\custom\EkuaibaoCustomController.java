package com.iaa.datacenter.module.buttjoint.controller.admin.ekuaibao.custom;

import com.iaa.datacenter.framework.common.pojo.CommonResult;
import com.iaa.datacenter.module.buttjoint.service.ekuaibao.custom.EkuaibaoCustomService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Tag(name = "管理后台 - 合思自定义档案维护")
@RestController
@RequestMapping("/butt-joint/ekuaibao/custom")
@Validated
@Slf4j
public class EkuaibaoCustomController {
    @Resource
    private EkuaibaoCustomService ekuaibaoCustomService;

    @PostMapping("/send-data")
    @Operation(summary = "推送本地数据到合思自定义档案")
    @PreAuthorize("@ss.hasPermission('ekuaibao:custom:list')")
    public CommonResult<Boolean> sendDataToCustom(@RequestParam("dimensionId") String dimensionId,
                                                  @RequestParam("code") String code,
                                                  @RequestParam("name") String name) {
        ekuaibaoCustomService.sendDataToCustom(dimensionId,code,name);
        return CommonResult.success(true);
    }
}
