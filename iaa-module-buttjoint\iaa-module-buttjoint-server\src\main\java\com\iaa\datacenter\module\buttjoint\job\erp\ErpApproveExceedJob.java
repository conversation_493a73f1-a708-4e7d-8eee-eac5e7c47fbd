package com.iaa.datacenter.module.buttjoint.job.erp;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.iaa.datacenter.framework.quartz.core.handler.JobHandler;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.erp.ErpApproveMessageDO;
import com.iaa.datacenter.module.buttjoint.dal.mapper.erp.ErpApproveMessageMapper;
import com.iaa.datacenter.module.buttjoint.service.erp.approve.ErpApproveMessageService;
import com.iaa.datacenter.module.buttjoint.service.landray.todo.LandrayTodoService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.List;


@Component
@RequiredArgsConstructor
public class ErpApproveExceedJob implements JobHandler {

    private final ErpApproveMessageMapper erpApproveMessageMapper;
    private final LandrayTodoService landrayTodoService;
    private final ErpApproveMessageService erpApproveMessageService;
    @Override
    public String execute(String param) throws Exception {
        String startTime = DateUtil.date().offset(DateField.DAY_OF_YEAR,-3).toString("yyyy-MM-dd HH:mm:ss");
        String endTime = DateUtil.date().offset(DateField.HOUR_OF_DAY,-12).toString("yyyy-MM-dd HH:mm:ss");
        List<ErpApproveMessageDO> list = erpApproveMessageMapper.list(startTime,endTime);
        int count = 0;
        for (ErpApproveMessageDO message : list) {
            if(message.getState() == 2 && message.getOperation() ==0){
                message.setIsApproved(true);
            }
            message.setIsApproved(message.getTackState()==2);
            if(message.getStateInfo()==2){
                message.setIsApproved(true);
            }
            if(message.getState()==3){
                message.setIsApproved(true);
            }
            if(!message.getIsApproved()){
                message.setIsApproved(true);
                landrayTodoService.sendToDo(message);
                message.setIsApproved(false);
                Thread.sleep(50);
                count++;
            }
            if(message.getIsApproved()){
                continue;
            }
            ErpApproveMessageDO erpApproveMessageDO = landrayTodoService.sendToDo(message);
            erpApproveMessageService.saveOrUpdate(erpApproveMessageDO, true);
        }
        return "任务执行成功，同步待办至ERP"+count;
    }
}
