package com.iaa.datacenter.module.buttjoint.job.wework;

import cn.hutool.core.date.DateUtil;
import com.iaa.datacenter.framework.quartz.core.handler.JobHandler;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.wework.AnniversaryMessageDO;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.wework.WeworkEmployeeDO;
import com.iaa.datacenter.module.buttjoint.dal.mapper.wework.AnniversaryMessageMapper;
import com.iaa.datacenter.module.buttjoint.dal.mapper.wework.WeworkEmployeeMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.api.WxCpMessageService;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.api.impl.WxCpServiceImpl;
import me.chanjar.weixin.cp.bean.message.WxCpMessage;
import me.chanjar.weixin.cp.config.impl.WxCpDefaultConfigImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Component
public class AnniversaryJob implements JobHandler {

    @Resource
    private WeworkEmployeeMapper weworkEmployeeMapper;
    @Resource
    private AnniversaryMessageMapper anniversaryMessageMapper;



    @Override
    public String execute(String param) throws Exception {
        WxCpService cpService = new WxCpServiceImpl();
        WxCpDefaultConfigImpl config = new WxCpDefaultConfigImpl();
        config.setCorpId("ww82c87150c4c5a9ba");
        config.setAgentId(1000047);
        config.setCorpSecret("VWGim4-WUTT36ZIp5VxyMrkWjTSJ_IK5Tfbs1rhsbeQ");
        cpService.setWxCpConfigStorage(config);

        List<WeworkEmployeeDO> list = weworkEmployeeMapper.selectAnniversary();
        List<AnniversaryMessageDO> msgList = anniversaryMessageMapper.selectList();
        for (WeworkEmployeeDO employee : list) {
            if (employee.getYear() > 20) {
                employee.setYear(20);
            }
            // 需要添加 commons-collections4 依赖
            AnniversaryMessageDO msgYear = CollectionUtils.find(msgList, msg ->
                    Objects.equals(employee.getYear(), msg.getYear()) && msg.getType() == 0
            );

            // 需要添加 commons-collections4 依赖
            AnniversaryMessageDO letter = CollectionUtils.find(msgList, msg ->
                    msg.getYear() == (employee.getYear()%2==0?-2:-1) && msg.getType() == 1
            );

            String content = letter.getContent()
                    .replaceAll("<p>","")
                    .replaceAll("</p>","\n")
                    .replaceAll("<p style=\"text-indent: 2em;\">","       ")
                    .replaceAll("<p style=\"text-align: right;\">","                                                                                                      ")
                    .replaceAll("<br>","")
                    .replaceAll("\\[普通消息]",msgYear.getContent())
                    .replaceAll("\\[员工姓名]",employee.getName())
                    .replaceAll("\\[具体周年数]", String.valueOf(employee.getYear()))
                    .replaceAll("\\[日期]", DateUtil.today());
            log.info("周年消息推送:{}",content);
            WxCpMessageService messageService = cpService.getMessageService();

            WxCpMessage message = WxCpMessage.TEXT()
                    .toUser(employee.getWeworkId())
                    .content(content)
                    .build();
            messageService.send(message);
        }
        return "周年消息推送成功："+list.stream().map(WeworkEmployeeDO::getName).collect(Collectors.joining(","));
    }
}
