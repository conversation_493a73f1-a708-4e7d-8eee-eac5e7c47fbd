package com.iaa.datacenter.module.buttjoint.dal.dataobject.erp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * ERP 客户收款DO
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ErpCustomerRcvDO {
    /**
     * 客户编码
     */
    private String customerCode;
    /**
     * 年
     */
    private String businessYear;
    /**
     * 月
     */
    private String businessMonth;
    /**
     * 收款金额
     */
    private Double totalMoney;
}
