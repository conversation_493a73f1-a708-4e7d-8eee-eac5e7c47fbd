<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iaa.datacenter.module.buttjoint.dal.mapper.red.RedFlowerMapper">
    <select id="selectList" resultType="com.iaa.datacenter.module.buttjoint.dal.dataobject.red.RedFlowerDO">
        WITH RECURSIVE ParentHierarchy AS (
            -- 初始查询：携带原始部门ID
            SELECT
                d.id,
                d.parentId,
                d.name,
                d.id AS rootId
            FROM hr_dept d

            UNION ALL

            -- 递归查询：逐级向上找父节点
            SELECT
                d.id,
                d.parentId,
                d.name,
                ph.rootId
            FROM hr_dept d
                     INNER JOIN ParentHierarchy ph ON d.id = ph.parentId
        )
        SELECT
            toDeptInfo.topLevelName AS endDept,
            toUser.name AS endUser,
            user.name AS startUser,
            deptInfo.topLevelName AS startDept,
            record.title AS title,
            record.content AS content,
            IF(record.is_red = '1', '小红花', '小黄花') AS type,
            COUNT(CASE WHEN record.is_red = '1' THEN 1 END) OVER (PARTITION BY record.to_userid,record.is_red) AS countRed,
                COUNT(CASE WHEN record.is_red = '2' THEN 1 END) OVER (PARTITION BY record.to_userid,record.is_red) AS countBlack,
                record.create_date AS dateTime
        FROM user_number_record record
                 LEFT JOIN hr_user user on record.userid = user.userid
            LEFT JOIN (SELECT userid, min(dept_id) AS dept_id FROM hr_dept_user GROUP BY userid) userDept ON record.userid = userDept.userid
            LEFT JOIN ( SELECT
            ph.rootId AS deptId,
            ph.id AS topLevelId,
            ph.name AS topLevelName
            FROM ParentHierarchy ph
            WHERE ph.parentId=16)
            deptInfo ON deptInfo.deptId = userDept.dept_id
            LEFT JOIN hr_user toUser ON record.to_userid = toUser.userid
            LEFT JOIN (SELECT userid, min(dept_id) AS dept_id FROM hr_dept_user GROUP BY userid) toUserDept ON toUser.userid = toUserDept.userid
            LEFT JOIN ( SELECT
            ph.rootId AS deptId,
            ph.id AS topLevelId,
            ph.name AS topLevelName
            FROM ParentHierarchy ph
            WHERE ph.parentId=16) toDeptInfo ON toDeptInfo.deptId = toUserDept.dept_id
        where 1=1 and toDeptInfo.topLevelId =21
        <if test=' sTime != null '>
            <choose>
                <when test='dateType == "year"'>AND record.create_date BETWEEN DATE_FORMAT(#{sTime}, '%Y-01-01') AND DATE_ADD(DATE_FORMAT(#{sTime},
                    '%Y-01-01'), INTERVAL 1 YEAR)
                </when>
                <otherwise>AND record.create_date BETWEEN DATE_FORMAT(#{sTime}, '%Y-%m-01') AND DATE_ADD(DATE_FORMAT(#{sTime}, '%Y-%m-01'), INTERVAL 1
                    MONTH)
                </otherwise>
            </choose>
        </if>
        order by record.create_date desc
    </select>
</mapper>
