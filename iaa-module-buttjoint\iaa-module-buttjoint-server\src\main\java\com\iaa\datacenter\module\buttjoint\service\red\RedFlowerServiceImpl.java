package com.iaa.datacenter.module.buttjoint.service.red;

import cn.hutool.core.collection.CollUtil;
import com.iaa.datacenter.framework.common.util.object.BeanUtils;
import com.iaa.datacenter.module.buttjoint.api.red.RedApi;
import com.iaa.datacenter.module.buttjoint.api.red.dto.RedFlowerDTO;
import com.iaa.datacenter.module.buttjoint.dal.dataobject.red.RedFlowerDO;
import com.iaa.datacenter.module.buttjoint.dal.mapper.red.RedFlowerMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class RedFlowerServiceImpl implements RedApi {

    @Resource
    private RedFlowerMapper redFlowerMapper;
    @Override
    public List<RedFlowerDTO> getFlowerList(String dateType, String sTime) {
        List<RedFlowerDO> redFlowerDOS = redFlowerMapper.selectList(dateType, sTime);
        if (CollUtil.isEmpty(redFlowerDOS)){
            return CollUtil.newArrayList();
        }
        return BeanUtils.toBean(redFlowerDOS, RedFlowerDTO.class);
    }
}
