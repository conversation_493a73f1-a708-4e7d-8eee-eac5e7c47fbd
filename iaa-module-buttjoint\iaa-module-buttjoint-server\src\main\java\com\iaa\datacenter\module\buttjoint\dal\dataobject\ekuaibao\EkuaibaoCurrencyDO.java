package com.iaa.datacenter.module.buttjoint.dal.dataobject.ekuaibao;

import com.anwen.mongo.annotation.ID;
import com.anwen.mongo.annotation.collection.CollectionName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 合思币种数据对象
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@CollectionName("ekuaibao_currency")
public class EkuaibaoCurrencyDO {
    @ID
    private String id;

    private Integer pipeline;

    private String grayver;

    private Integer version;

    private Boolean active;

    private Long createTime;

    private Long updateTime;

    private String corporationId;

    private String sourceCorporationId;

    private String dataCorporationId;

    private String numCode;

    private String strCode;

    private Integer scale;

    private String name;

    private String symbol;

    private String unit;

    private String icon;

    private BigDecimal rate;

    private String budgetRate;

    private Long startTime;

    private Long endTime;

    private Long order;

    private String originalId;

    private String isDefault;

    private String u9cCode;
}
